# frozen_string_literal: true
require 'benchmark'

require_relative '../utils/error_logger'
include ErrorLogger

desc "Send reminder emails to teachers who haven't logged in for 2+ weeks"
task send_inactive_teacher_reminders: :environment do
  begin
    puts "Starting inactive teacher reminder email task"
    
    time = Benchmark.measure do
      # Find teachers who haven't been active for 14+ days
      cutoff_date = 14.days.ago

      inactive_teachers = Teacher.joins(:school)
                                .where('users.last_activity_at < ?', cutoff_date)
                                .where('users.last_activity_at > ?', 16.days.ago) # Exclude teachers who have been inactive for more than 16 days
                                .where('users.deleted = false')
                                .where('schools.deleted = false')
      
      puts "Found #{inactive_teachers.count} inactive teachers"
      
      emails_sent = 0
      errors = 0
      
      inactive_teachers.find_each do |teacher|
        begin
          # Check if teacher allows activity notifications
          if teacher.allows_notification?(:activity)
            # Check if we've already sent a reminder recently (don't spam)
            # We'll use a simple approach: check if they have any recent email tracking
            # For a more robust solution, you might want to add a dedicated tracking table
            last_reminder_key = "inactive_reminder_sent_#{teacher.id}"
            last_sent = Rails.cache.read(last_reminder_key)

            if last_sent.nil? || last_sent < 1.week.ago
              SchoolMailer.inactive_teacher_reminder(teacher).deliver_now
              # Cache that we sent this email for 1 week
              Rails.cache.write(last_reminder_key, Time.current, expires_in: 1.week)
              emails_sent += 1
              puts "Sent reminder email to #{teacher.name} (#{teacher.email})"
            else
              puts "Skipped #{teacher.name} - reminder sent recently"
            end
          else
            puts "Skipped #{teacher.name} - activity notifications disabled"
          end
        rescue => e
          errors += 1
          puts "Error sending email to #{teacher.name} (#{teacher.email}): #{e.message}"
          log_error(e, "send_inactive_teacher_reminders - teacher #{teacher.id}")
        end
      end
      
      puts "Task completed: #{emails_sent} emails sent, #{errors} errors"
    end
    
    puts "Inactive teacher reminder task completed - took: #{time.real} seconds"
    
  rescue => e
    log_error(e, "send_inactive_teacher_reminders")
    puts "Task failed with error: #{e.message}"
  end
end
