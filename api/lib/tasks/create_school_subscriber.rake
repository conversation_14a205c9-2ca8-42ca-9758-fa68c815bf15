# frozen_string_literal: true

desc 'Create a subscriber record for a school'
task create_school_subscriber: :environment do
  begin
    puts "🏫 School Subscriber Creation Tool"
    puts "=" * 40
    puts

    # Step 1: Ask for school ID
    print "Enter the School ID: "
    school_id = STDIN.gets.chomp.to_i

    if school_id <= 0
      puts "❌ Invalid School ID. Exiting."
      exit
    end

    # Step 2: Find and validate the school
    school = School.find_by(id: school_id)
    unless school
      puts "❌ School with ID #{school_id} not found. Exiting."
      exit
    end

    puts "✅ Found school: #{school.name} (ID: #{school.id})"
    puts "   Finance Email: #{school.finance_email}"
    puts "   Finance Name: #{school.finance_name}"
    puts

    # Step 3: Check if school already has a subscriber record
    if school.subscriber.present?
      puts "❌ ERROR: This school already has a subscriber record!"
      puts "   Subscriber ID: #{school.subscriber.id}"
      puts "   Subscription Status: #{school.subscriber.subscription_status}"
      puts "   Cannot create a new subscriber for a school that already has one."
      exit
    end

    puts "✅ School does not have a subscriber record. Proceeding..."
    puts

    # Step 4: Ask for Stripe customer ID
    print "Enter the Stripe Customer ID: "
    stripe_customer_id = STDIN.gets.chomp.strip

    if stripe_customer_id.empty?
      puts "❌ Stripe Customer ID cannot be empty. Exiting."
      exit
    end

    # Step 5: Ask for Stripe subscription ID
    print "Enter the Stripe Subscription ID: "
    stripe_subscription_id = STDIN.gets.chomp.strip

    if stripe_subscription_id.empty?
      puts "❌ Stripe Subscription ID cannot be empty. Exiting."
      exit
    end

    puts
    puts "📋 Summary:"
    puts "   School: #{school.name} (ID: #{school.id})"
    puts "   Stripe Customer ID: #{stripe_customer_id}"
    puts "   Stripe Subscription ID: #{stripe_subscription_id}"
    puts "   Subscription Status: active"
    puts

    # Step 6: Show billing address that will be copied
    puts "📍 Billing address to be copied from school:"
    puts "   Address Line 1: #{school.default_billing_address_line_1 || 'N/A'}"
    puts "   Address Line 2: #{school.default_billing_address_line_2 || 'N/A'}"
    puts "   City: #{school.default_billing_city || 'N/A'}"
    puts "   State: #{school.default_billing_state || 'N/A'}"
    puts "   Postal Code: #{school.default_billing_postal_code || 'N/A'}"
    puts "   Country: #{school.default_billing_country&.name || 'N/A'}"
    puts

    # Step 7: Confirmation
    print "Do you want to proceed with creating the subscriber record? (yes/no): "
    confirmation = STDIN.gets.chomp.downcase

    unless %w[yes y].include?(confirmation)
      puts "❌ Operation cancelled by user."
      exit
    end

    # Step 8: Create the subscriber record
    puts
    puts "🚀 Creating subscriber record..."

    subscriber_attributes = {
      subscriber: school,
      stripe_customer_id: stripe_customer_id,
      stripe_subscription_id: stripe_subscription_id,
      subscription_status: 'active',
      active_products: {
        'products' => [],
        'synced_at' => Time.current.iso8601,
        'subscription_status' => 'active',
        'sync_message' => 'Created via rake task'
      },
      free_subscription: {
        'ai' => false,
        'science' => false,
        'geography' => false
      }
    }

    # Copy billing address from school if available
    if school.default_billing_address_line_1.present?
      subscriber_attributes.merge!(
        address_line1: school.default_billing_address_line_1,
        address_line2: school.default_billing_address_line_2,
        city: school.default_billing_city,
        state: school.default_billing_state,
        postal_code: school.default_billing_postal_code,
        country: 'GB'
      )
    end

    subscriber = Subscriber.create!(subscriber_attributes)

    puts "✅ Subscriber record created successfully!"
    puts "   Subscriber ID: #{subscriber.id}"
    puts "   Subscription Status: #{subscriber.subscription_status}"
    puts "   Stripe Customer ID: #{subscriber.stripe_customer_id}"
    puts "   Stripe Subscription ID: #{subscriber.stripe_subscription_id}"
    puts
    puts "🎉 Task completed successfully!"

  rescue ActiveRecord::RecordInvalid => e
    puts "❌ Failed to create subscriber record:"
    puts "   Validation errors: #{e.record.errors.full_messages.join(', ')}"
  rescue => e
    puts "❌ An error occurred: #{e.message}"
    puts "   #{e.backtrace.first}"
  end
end
