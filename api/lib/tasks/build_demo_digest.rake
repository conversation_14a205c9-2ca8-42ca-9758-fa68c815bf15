# frozen_string_literal: true

task cleanup_demo_digest: :environment do
  puts "🧹 Cleaning up existing demo digest data..."
  
  forms_count = Form.where('name ilike ?', 'Demo Digest Form%').count
  pupils_count = Pupil.where('name ilike ?', 'Digest Pupil %').count
  homework_count = Homework.where('title ilike ?', 'Demo Digest Homework %').count
  
  Form.where('name ilike ?', 'Demo Digest Form%').destroy_all
  Form.where('name ilike ?', 'Demo Digest Past Form%').destroy_all
  Form.where('name ilike ?', 'Demo Digest Future Form%').destroy_all
  Pupil.where('name ilike ?', 'Digest Pupil %').destroy_all
  Homework.where('title ilike ?', 'Demo Digest Homework %').destroy_all
  
  puts "   ✅ Cleaned up #{forms_count} forms, #{pupils_count} pupils, #{homework_count} homework assignments"
end

task build_demo_digest: :environment do
  puts "🏗️  Building demo digest data..."
  
  # Cleanup first
  Rake::Task['cleanup_demo_digest'].invoke
  
  teacher = Teacher.find_by(email: '<EMAIL>')
  unless teacher
    puts "   ❌ Teacher not found with email '<EMAIL>'"
    exit
  end
  
  school = teacher.school
  puts "   📚 Using teacher: #{teacher.name} at #{school.name}"

  # Create a single form (most teachers only have one class)
  puts "   📝 Creating form..."
  form = teacher.forms.create!(name: 'Demo Digest Form Y7 Science', school: school)
  puts "      ✅ Created form: #{form.name}"

  # Create varied pupils (20-25 pupils typical for a class)
  puts "   👥 Creating pupils with varied activity levels..."
  pupil_count = 23
  pupils = []
  
  pupil_count.times do |i|
    pupil_name = "Digest Pupil #{['Alice', 'Bob', 'Charlie', 'Diana', 'Emma', 'Felix', 'Grace', 'Harry', 'Iris', 'Jack', 'Katie', 'Leo', 'Maya', 'Noah', 'Olivia', 'Peter', 'Quinn', 'Ruby', 'Sam', 'Tara', 'Uma', 'Victor', 'Willow'].sample} #{i+1}"
    
    # Varied activity levels - some highly active, some moderate, some low
    activity_multiplier = case i % 5
    when 0, 1 # 40% highly active
      rand(0.8..1.0)
    when 2, 3 # 40% moderately active  
      rand(0.4..0.7)
    else # 20% low activity
      rand(0.1..0.3)
    end
    
    last_activity = 1.week.ago + rand(1..7).days
    pupil = school.pupils.create!(
      name: pupil_name, 
      last_activity_at: last_activity
    )
    pupils << { pupil: pupil, activity_level: activity_multiplier }
  end
  
  form.pupils << pupils.map { |p| p[:pupil] }
  puts "      ✅ Created #{pupil_count} pupils with varied activity levels"

  # Get templates for lessons
  puts "   📖 Setting up lesson templates..."
  curriculum = NewLibrary::Curriculum.find(18)
  all_templates = curriculum.lesson_templates.shuffle
  
  unless all_templates.any?
    puts "   ❌ No lesson templates found for curriculum ID 18"
    exit
  end

  # Create lessons for different time periods
  past_week_templates = all_templates.shift(4)  # 4 lessons last week
  next_week_templates = all_templates.shift(3)  # 3 lessons next week

  last_week = 1.week.ago.beginning_of_week
  next_week = 1.week.from_now.beginning_of_week

  puts "   📅 Creating past week lessons..."
  past_lessons = past_week_templates.map.with_index do |template, i|
    lesson_time = last_week + (i + 1).days + rand(0..8).hours
    lesson = form.lessons.create!(template: template, time: lesson_time)
    puts "      ✅ Created lesson: #{lesson.name} for #{lesson_time.strftime('%A %d %b')}"
    lesson
  end

  puts "   📅 Creating next week lessons..."
  next_lessons = next_week_templates.map.with_index do |template, i|
    lesson_time = next_week + (i + 1).days + rand(8..16).hours
    lesson = form.lessons.create!(template: template, time: lesson_time)
    puts "      ✅ Created lesson: #{lesson.name} for #{lesson_time.strftime('%A %d %b')}"
    lesson
  end

  # Create homework with varied completion
  puts "   📋 Creating homework assignments..."
  base_homework = Homework.where(school: school).first
  unless base_homework
    puts "   ⚠️  No existing homework found to duplicate, skipping homework creation"
  else
    # Homework for past lesson
    hw1 = base_homework.dup
    hw1.title = "Plant Structure Investigation"
    hw1.lesson_lesson_id = past_lessons.first.id
    hw1.published = true
    hw1.date_set = past_lessons.first.time.to_date
    hw1.date_due = hw1.date_set + 3.days
    hw1.save!
    
    # Create a task for the homework
    if HomeworkTask.where(task_type: 'lesson_quiz').any?
      task = HomeworkTask.where(task_type: 'lesson_quiz').first.dup
      task.homework = hw1
      task.save
    end
    
    puts "      ✅ Created homework: #{hw1.title}"

    # Homework for upcoming lesson
    hw2 = base_homework.dup
    hw2.title = "Photosynthesis Worksheet"
    hw2.lesson_lesson_id = next_lessons.last.id
    hw2.published = true
    hw2.date_set = Date.current
    hw2.date_due = next_lessons.last.time.to_date + 1.day
    hw2.save!
    
    puts "      ✅ Created homework: #{hw2.title}"
  end

  # Ensure form units exist
  puts "   🏗️  Setting up form units..."
  form.lessons.each { |l| l.add_corresponding_unit }
  puts "      ✅ Form units created"

  # Create varied pupil activity data
  puts "   📊 Generating pupil activity data..."
  today = Date.current
  activity_scores = {}
  
  pupils.each do |pupil_data|
    pupil = pupil_data[:pupil]
    activity_level = pupil_data[:activity_level]
    total_score = 0
    
    past_lessons.each do |lesson|
      lesson_date = lesson.time.to_date
      created_at = lesson.time
      
      # Always track lesson view
      TrackingLessonTemplateViewed.create!(
        user: pupil,
        lesson: lesson,
        lesson_template: lesson.template,
        created_at: created_at
      )
      total_score += 5
      
      # Presentation views based on activity level
      if rand < activity_level
        TrackingPresentationView.create!(
          user: pupil,
          lesson: lesson,
          lesson_template: lesson.template,
          created_at: created_at
        )
        total_score += 10
      end
      
      # Quiz participation based on activity level
      if rand < activity_level * 0.9
        quiz_score = (3 + rand * 2 * activity_level).round
        QuipQuizResult.create!(
          pupil_id: pupil.id,
          score: quiz_score,
          total: 5,
          results_json: {a:1},
          created_at: created_at,
          updated_at: created_at,
          lesson_template_id: lesson.template.id,
          time_seconds: rand(60..300),
          quip_quiz_id: 1,
          user_id: pupil.id,
          result_type: 0,
          flow_progress_id: nil
        )
        total_score += quiz_score * 3
      end
      
      # Lesson marks (teacher assessment) - less frequent
      if rand < activity_level * 0.4
        mark_value = activity_level > 0.7 ? ['3', '4'].sample : ['1', '2', '3'].sample
        PupilLessonMark.create!(
          user_id: pupil.id,
          lesson_id: lesson.id,
          mark: mark_value,
          created_at: created_at
        )
        total_score += mark_value.to_i * 5
      end
      
      # Active days - highly active pupils have more active days
      week_start = lesson_date.beginning_of_week
      week_end = week_start + 6.days
      active_day_count = (activity_level * 4).round + 1 # 1-5 active days per week
      
      (week_start..week_end).to_a.sample(active_day_count).each do |day|
        ActiveDay.find_or_create_by(user_id: pupil.id, date: day) do |ad|
          ad.created_at = day.to_time
          ad.updated_at = day.to_time
        end
      end
    end
    
    # Add some bonus activity for highly engaged pupils
    if activity_level > 0.8
      total_score += rand(20..50)
    elsif activity_level > 0.5
      total_score += rand(5..20)
    end
    
    activity_scores[pupil.name] = total_score.round
  end
  
  # Log top pupils
  top_pupils = activity_scores.sort_by { |name, score| -score }.first(5)
  puts "      ✅ Generated activity data. Top pupils:"
  top_pupils.each_with_index do |(name, score), index|
    puts "         #{index + 1}. #{name}: #{score} points"
  end

  # Teacher activity
  puts "   👨‍🏫 Creating teacher activity..."
  week_start = last_week.to_date
  week_end = Date.current
  teacher_active_days = 0
  
  (week_start..week_end).each do |day|
    next if day.saturday? || day.sunday? # Teachers less active on weekends
    if rand < 0.8 # 80% chance of activity on weekdays
      ActiveDay.find_or_create_by(user_id: teacher.id, date: day) do |ad|
        ad.created_at = day.to_time
        ad.updated_at = day.to_time
      end
      teacher_active_days += 1
    end
  end
  
  puts "      ✅ Teacher had #{teacher_active_days} active days this week"
  
  puts ""
  puts "🎉 Demo digest data creation complete!"
  puts "   📊 Summary:"
  puts "      • 1 form created with #{pupil_count} pupils"
  puts "      • #{past_lessons.count} lessons from last week"
  puts "      • #{next_lessons.count} lessons scheduled for next week"
  puts "      • Varied pupil activity levels (high: 40%, moderate: 40%, low: 20%)"
  puts "      • Top 3 most active pupils: #{top_pupils.first(3).map { |name, score| "#{name} (#{score}pts)" }.join(', ')}"
  puts ""
  puts "🔍 You can now view the weekly digest to see the generated data in action!"
end