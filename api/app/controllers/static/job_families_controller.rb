module Static
  class JobFamiliesController < AnonController
    include PaginationHelper
    include BreadcrumbsHelper
    layout :dynamic_layout

    before_action :set_base_breadcrumbs

    def index
      @job_families = JobFamily.all.order(:name)
    end
    
    def show
      @job_family = JobFamily.includes(career_paths: [:career]).friendly.find(params[:id])

      raise ActiveRecord::RecordNotFound if @job_family.blank?

      add_breadcrumb @job_family.name, job_family_path(@job_family)

      if request.path != job_family_path(@job_family)
        return redirect_to job_family_path(@job_family), status: :moved_permanently
      end
    end

    private

    def set_base_breadcrumbs
      add_root_breadcrumb(current_user, @current_pupil)
      add_breadcrumb "Careers", careers_index_path
      add_breadcrumb "Job Families", job_families_path
    end

    def dynamic_layout
      if @current_user&.pupil?
        'pupil'
      elsif @current_user&.teacher?
        'school'
      else
        'static'
      end
    end
  end
end
