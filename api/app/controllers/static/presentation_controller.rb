# api/app/controllers/static/presentation_controller.rb
module Static
  class PresentationController < StaticApplicationController
    include VideoUrlHelpers
    layout 'presentation'
    before_action :allow_iframe
    before_action :allowed_params

    def allowed_params
      @page_params = {}
      @page_params['slide'] = params[:slide] if params[:slide].present?
      @page_params['return_to'] = params[:return_to] if params[:return_to].present?
      @page_params['return_tab'] = params[:return_tab] if params[:return_tab].present?
      @page_params['no-redirect'] = params['no-redirect'] if params['no-redirect'].present?
    end

    def show
      template = Lesson::Template.find_by(id: params[:id])
      unless template
        head :not_found
        return
      end

      pupil_for_tracking = nil
      if params[:user_id]
        pupil_for_tracking = User.find_by(id: params[:user_id])
      elsif current_user&.pupil?
        pupil_for_tracking = current_user
      end

      if pupil_for_tracking
        lesson = Lesson::Lesson.find_by(id: params[:lesson_id]) if params[:lesson_id] # prefer by id
        if lesson.blank?
          lesson = template.lesson_for_user(pupil_for_tracking) unless lesson.present? # try find by template
        end
      end

      TrackingService.track_presentation_view(template, pupil_for_tracking.presence || current_user, {
        lesson_id: lesson&.id,
      })

      has_new_presentation = template.new_presentation.present? && template.new_presentation.slides.any? && !template.new_presentation.converted && (template.user_generated || template.new_presentation.published != 'unpublished')
      path = "/presentation/new/#{template.id}" if has_new_presentation
      path = "/presentation/static/#{template.id}" unless has_new_presentation
      redirect_to path + '?' + @page_params.to_query
    end

    # presentation/slide/:slide_id
    def show_single_slide
      slide = Lesson::Slide.find(params[:slide_id])
      template = slide.template
      authorize! :read, template

      all_video_ids_in_dynamic_panels = []
      template.slides.each do |s|
        if s.slide_type.to_s == 'dynamic' && s.data.is_a?(Hash) && s.data['panels'].is_a?(Array)
          s.data['panels'].each do |p|
            if p.is_a?(Hash) && p['media_type'] == 'video' && p['video_id'].present?
              all_video_ids_in_dynamic_panels << p['video_id']
            end
          end
        end
      end
      # Fetch all relevant Video records in one go
      videos_by_id = Video.where(id: all_video_ids_in_dynamic_panels.uniq).index_by(&:id)

      @slide = parse_presentation_template_slide(0, template, slide, videos_by_id)
      @slide[:content][:quiz_question][:id] = nil if @slide.dig(:content, :quiz_question, :id).present?
      @presentation = {}
      @keywords = template.keywords.map { |keyword| { name: keyword[:name], body: keyword[:body] }}

      render :show_single_slide, layout: false
    end

    # presentation/static/:id
    def template_slides_presentation
      @current_user = current_user
      @feedback_associate_to = 'template'
      @feedback_associate_to_id = params[:id]
    
      template = Lesson::Template.find_by(id: params[:id])
      unless template
        head :not_found
        return
      end
    
      # Eager load Video records for panels to avoid N+1 queries later
      # This is a bit tricky as video_ids are inside JSON.
      # A more optimized approach might involve plucking all video_ids first,
      # then fetching Video records, then mapping back.
      # For now, let's do it during the mapping.
      # Consider pre-fetching Video records if performance becomes an issue.
      all_video_ids_in_dynamic_panels = []
      template.slides.each do |s|
        if s.slide_type.to_s == 'dynamic' && s.data.is_a?(Hash) && s.data['panels'].is_a?(Array)
          s.data['panels'].each do |p|
            if p.is_a?(Hash) && p['media_type'] == 'video' && p['video_id'].present?
              all_video_ids_in_dynamic_panels << p['video_id']
            end
          end
        end
      end
      # Fetch all relevant Video records in one go
      videos_by_id = Video.where(id: all_video_ids_in_dynamic_panels.uniq).index_by(&:id)

      slides_data = template.slides.includes([:quip_question, :video, :tour, :homework, :homework_task]) # Include main slide video if used by other types
                      .where.not(slide_type: 'previous_keywords')
                      .order(weight: :asc)

      # Reject intro slide only if it's the very first slide overall
      # The original logic `slides.reject.with_index { |slide, index| slide.slide_type == 'intro' && index == 0 }`
      # might behave unexpectedly if slides_data is an ActiveRecord_Relation.
      # It's often better to convert to_a first if using reject with index on a relation.
      # However, typically intro slides are handled by specific rendering logic outside the main slide loop.
      # For now, I'll keep your original rejection logic's intent.
      # Let's assume intro/outro are handled by the main show.html.erb logic.

      processed_slides = slides_data.to_a # Work with an array for safe rejection with index
      processed_slides = processed_slides.reject.with_index { |slide, index| slide.slide_type.to_s == 'intro' && index == 0 }
      processed_slides = processed_slides.reject { |slide| slide.slide_type.to_s == 'quip_question' && slide.quip_question.nil? }

      @presentation = {
        title: template.name,
        sponsor_images: template_sponsors(template), # Assuming this helper exists
        objectives: template.objectives,
        scientific_enquiry_types: template.scientific_enquiry_types.as_json,
        cover: fileboy_url(template.fileboy_image_id), # Assuming this helper exists
        slides: processed_slides.map.with_index do |slide, index|
          parse_presentation_template_slide(index, template, slide, videos_by_id)
        end
      }
    
      # Removed puts "@presentation: #{@presentation}" for cleaner output, uncomment for debugging
      @current_slide = params[:slide].presence || @presentation[:slides].first&.[](:weight)&.to_s || '1' # Default to first slide's weight
      @url = "/presentation/static/#{template.id}"
      @keywords = template.keywords.map { |keyword| { name: keyword[:name], body: keyword[:body] }}

      respond_to do |format|
        format.html { render :show }
        format.js { render :show } # Assuming you handle JS responses for navigation
      end
    end

    # presentation/new/:id
    def new_presentation
      @current_user = current_user
      template = Lesson::Template.find_by(id: params[:id])

      unless template
        head :not_found
        return
      end

      presentation = template.new_presentation

      @feedback_associate_to = 'new_presentation'
      @feedback_associate_to_id = template.new_presentation.id

      slides = presentation.slides.order(weight: :asc)

      # If the first slide is an intro slide AND is not modified, remove it as its replaced by the custom cover slide.
      # A slide is considered un-modified if it starts with 'Mission Objectives' and has no header / empty header string
      f_slide = slides.first
      if f_slide.type == 'intro' && f_slide.data['header'].blank?
        # this is the body default.. it doesn't have a correctly closed h3 tag
        matched_default_body = f_slide.data['body'] == '<h3>Mission Objectives<h3>'
        # I've fixed it, so also match for new slides
        matched_corrected_body = f_slide.data['body'] == '<h3>Mission Objectives</h3>'
        # this is the body if converted from an old presentation format.
        matched_converted_body = f_slide.data['body'].start_with?('<p>Mission Objectives</p>')

        if matched_default_body || matched_corrected_body || matched_converted_body
          slides = slides.reject { |slide| slide.id == f_slide.id }
        end
      end

      settings = presentation.settings
      presentation_bg = nil
      default_background_color = settings['backgroundColor']
      if settings['presentationBackground'].present?
        presentation_bg = { type: 'image', url: settings.dig('presentationBackground', 'value') } if settings.dig('presentationBackground', 'type') == 'imageUrl'
        presentation_bg = { type: 'image', url: fileboy_url(settings.dig('presentationBackground', 'value')) } if settings.dig('presentationBackground', 'type') == 'imageId'
        default_background_color = settings.dig('presentationBackground', 'value') if settings.dig('presentationBackground', 'type') == 'color'
      end

      default_background_color = '#000000' if default_background_color.blank?

      @presentation = {
        title: presentation.name,
        sponsor_images: template_sponsors(template),
        objectives: template.objectives,
        cover: fileboy_url(template.fileboy_image_id),
        scientific_enquiry_types: template.scientific_enquiry_types.as_json,
        slides: slides.map.with_index do |slide, index|
          # New presentation slide types:
          # intro # video # timer # multiImage # quipQuiz # rocketWordQuiz # rocketWords # embed
          # three60 # endScreen # disclaimer # homework

          data = slide.data

          # convert a timer slide into a video slide so it doesnt need special handling as it is
          # just a video slide with a preset video anyway
          if slide.type == 'timer'
            timer_slide_preset = [
              {
                label: '30 seconds (red)',
                fileboyVideoId: '33ec57c0-e265-40ca-9f9a-bb907ce26fdf',
                duration: 31,
              },
              { label: '30 seconds', fileboyVideoId: 'e8ccd43a-a06b-404d-b3dc-72ba6d9256e2', duration: 30 },
            ].detect { |preset| preset[:duration] == data['duration'] }

            slide.type = 'video'
            data['videoType'] = 'fileboy'
            data['videoId'] = timer_slide_preset&.dig(:fileboyVideoId)
          end

          # defualt to the thumbnail
          thumbnail_id = data['thumbnailFileboyId']
          thumbnail = fileboy_url_small(thumbnail_id) if thumbnail_id.present?
          # if slide type with image override and no set thumbnail, use override image
          unless thumbnail_id.present?
            thumbnail = fileboy_video_thumbnail(data['videoId']) if %w[video].include?(slide.type)
            thumbnail = fileboy_rocket_word_thumbnail if %w[rocketWords].include?(slide.type)
            thumbnail = fileboy_quiz_thumbnail if %w[rocketWordQuiz].include?(slide.type)
          end

          slide_bg = { type: 'image', url: '', background_size: slide.data['backgroundImageMode'] }
          if slide.type == 'rocketWords' || slide.type == 'quipQuiz'
            slide_bg[:type] = 'color'
            slide_bg[:color] = 'black'
          elsif data['backgroundImageFileboyId'].present?
            slide_bg[:url] = fileboy_url(data['backgroundImageFileboyId'])

            thumbnail = fileboy_url_small(data['backgroundImageFileboyId']) unless thumbnail.present?
          elsif data['thumbnailFileboyId'].present?
            slide_bg[:url] = fileboy_url(data['thumbnailFileboyId'])
          elsif data['background'].present?
            slide_bg[:type] = 'color'
            slide_bg[:color] = data['background']
          end

          top_text = data['header']
          bottom_text = data['footer']
          top_text = "Today's Rocket Words: Your 5 words and meanings to learn" if slide.type == 'rocketWords' && top_text.blank?

          media = []
          if slide.type == 'multiImage'
            data['images'].each do |image|
              media << { type: 'image', url: fileboy_url(image['fileboyImageId']), background_size: image['imageMode'] }
            end
          else
            media << slide_bg if slide_bg[:url].present?
            media << presentation_bg if presentation_bg.present? && media.empty? && !data['background'].present?
            default_background_color = data['background'] if data['background'].present?
          end

          keywords = []
          if slide.type == 'rocketWords' || slide.type == 'rocketWordQuiz'
            template_id = data['lessonTemplateId']
            keywords = Lesson::Template.find(template_id).keywords.includes([:quiz_question])
            keywords = template.keywords if keywords.empty?
          end

          video = nil
          if slide.type == 'video' || slide.type == 'expertVideo'
            video = { type: data['videoType'], url: '' }
            if data['videoType'] == 'youtube' && (match = youtube_url_check(data['videoId']))
              video[:url] = yt_convert_to_embed_url(data['videoId'])
            end
            if data['videoType'] == 'vimeo' && (match = vimeo_url_check(data['videoId']))
              id = match[1]
              video[:url] = "https://player.vimeo.com/video/#{id}"
            end
            video[:url] = data['videoId'] if data['videoType'] == 'fileboy'
          end
          video = nil if video.present? && video[:url].blank?

          video[:loop] = true if video && data['loop']

          iframe_src = data['embedUrl']
          if slide.type == 'three60' && data['tourId'].present?
            iframe_src = "#{Rails.application.config.x.frontend_url}/tours/#{data['tourId']}"
            frame_params = { no_exit: 1 }
            auto_focus_id = data.dig('autoFocusVideo', 'value')
            frame_params["auto_focus_video_id"] = auto_focus_id if auto_focus_id
            iframe_src += "?#{frame_params.to_query}"
          end

          question = slide['type'] == 'quipQuiz' ? { id: slide.id.to_s, dataJson: data['questionData'] } : nil
          if slide['type'] == 'quipQuiz'
            question_types = {
              'multi-choice' => 0,
              'fill-in-blanks' => 1,
              'sort-list' => 2,
              'image-bucket' => 3,
              'free-text' => 4,
            }
            question['type'] = question_types.keys[data['questionType']]
            question.deep_transform_keys!(&:to_s)
          end

          # IF intro slide and NOT first slide, set type to text, else set to slide type
          slide_type = slide.type == 'intro' ? 'text' : slide.type

          font_scale = case data['fontScale']
                       when nil then 'text-lg sm:text-2xl md:text-3xl'
                       when '' then 'text-lg sm:text-2xl md:text-3xl'
                       when 0...24 then 'text-lg sm:text-2xl md:text-3xl'
                       when 24...999 then 'text-xl sm:text-3xl md:text-4xl' # catch anything over 24
                       else 'text-2xl sm:text-4xl md:text-5xl'
                       end
          text_align = case data['alignText']
                       when 'start' then 'align-items: flex-start;'
                       when 'end' then 'align-items: flex-end;'
                       when 'center' then 'align-items: center;'
                       else ''
                       end
          text_justify = case data['justifyText']
                         when 'left' then 'text-align: left;'
                         when 'right' then 'text-align: right;'
                         when 'center' then 'text-align: center;'
                         else ''
                         end

          font_color = data['fontColor'].present? ? "color: #{data['fontColor']};" : ''
          
          @keywords = keywords.map do |keyword|
            {
              name: keyword.name,
              body: keyword.body,
              image: fileboy_url_small(keyword.fileboy_image_id),
              question: keyword.quiz_question.body,
            }
          end
          slide = {
            weight: index + 1,
            top_text: top_text,
            bottom_text: bottom_text,
            background: {
              media: media,
              color: default_background_color,
            },
            thumbnail: thumbnail,
            content: {
              text: {
                body: data['body'],
                align: text_align,
                justify: text_justify,
                font_color: font_color,
                font_scale: font_scale,
              },
              quiz_question: question,
              iframe_src: iframe_src,
              type: slide_type,
              de_video_id: '',
              video: video,
              homework: Homework.accessible_by(current_ability).find_by(id: data['homeworkId']),
              homework_task: HomeworkTask.accessible_by(current_ability).find_by(id: data['taskId']),
              keywords: @keywords,
            }
          }

          slide[:background][:color] = '#000000' if slide[:content][:type] == 'multiImage'

          slide
        end
      }

      @current_slide = params[:slide].presence || 0 # Default to first slide if none specified

      @url = "/presentation/new/#{template.id}"

      respond_to do |format|
        format.html do
          render :show
        end
        format.js do
          render :show
        end
      end
    end

    # /alpha/presentation/ai/:id
    def ai_show
      @feedback_associate_to = 'ai_lesson'
      @feedback_associate_to_id = params[:id]
      base_url = ''
      if Rails.env.development?
        base_url = 'http://localhost:8080'
      else
        base_url = 'https://ai-unstable-preview.developingexperts.com'
      end
      base_url += '/ai/lessons/'
      # if Rails.env.development? || Rails.env.test?
      #   base_url = "http://localhost:8080/ai/lessons/"
      # end
      id = params[:id]
      uri = URI("#{base_url}#{id}/presentation")
      Rails.logger.debug uri
      response = Net::HTTP.get(uri)
      lesson_data = JSON.parse(response)

      raise StandardError.new("No slides found in this lesson") if lesson_data['LessonSlides'].blank?

      @presentation = {
        title: lesson_data.dig('Title'),
        objectives: lesson_data.dig('LessonObjectives'),
        cover: lesson_data.dig('Image'),
        slides: lesson_data.dig('LessonSlides').select { |slide| slide.is_a?(Hash) && slide.dig("Weight").present? }
                                               .map do |slide|
          thumbnail = slide.dig('ImageThumb')
          if slide.dig('Type') == 'keyword'
            thumbnail = fileboy_url('92ff4093-f09c-4ab0-9851-cf76204f019c+rocket_word_thumbnail')
          elsif slide.dig('Type') == 'quiz'
            thumbnail = fileboy_url('92ff4093-f09c-4ab0-9851-cf76204f019c+quiz_thumbnail')
          end
          {
            weight: slide.dig('Weight'),
            top_text: slide.dig('Body'),
            background: {
              media: parse_media(slide),
              color: slide.dig('BackgroundColor'),
              background_size: 'cover',
            },
            thumbnail: thumbnail,
            content: {
              type: slide.dig('Type'),
              de_video_id: slide.dig('DeVideoID'),
              keywords: lesson_data.dig('Keywords').map do |keyword|
                {
                  name: keyword.dig('Name'),
                  body: keyword.dig('Body'),
                  image: keyword.dig('Image'),
                  question: keyword.dig('Question'),
                }
              end,
            }
          }
        end.sort_by { |slide| slide[:weight] }
      }

      @current_slide = params[:slide].presence || 0 # Default to first slide if none specified
      @url = "/alpha/presentation/ai/#{id}"

      respond_to do |format|
        format.html do
          render :show
        end
        format.js do
          render :show
        end
      end
    end

    def quiz_slide
      @slide = JSON.parse(params[:slide])
      if params[:full_quiz] == 'true'
        @quiz = QuipQuiz.find(@slide.dig('content', 'quiz_id'))
        @submit_path = quip_quiz_by_id_path(@quiz, user_id: params[:user_id])
        render partial: 'complete_quiz_slide'
      else
        @question = @slide.dig('content', 'quiz_question')
        @quiz = QuipQuestion.find_by(id: @question['id'])&.quip_quiz
        render partial: 'quiz_question'
      end
    end

    private

    def parse_presentation_template_slide(index, template, slide, videos_by_id)
      slide_json = {} # Initialize hash for this slide
      if slide.slide_type.to_s == 'dynamic'
        # Handle DYNAMIC slide type
        slide_data_for_view = slide.data_for_form # Get augmented data (includes video_source/external_id for panels)
                                                  # We need to re-process videos for rendering output format

        slide_json[:top_text] = slide_data_for_view['top_text']
        # bottom_text will be handled by the main _slide.html.erb structure if we put it there too
        slide_json[:bottom_text] = slide_data_for_view['bottom_text']
        slide_json[:thumbnail] = slide.thumbnail # Uses the updated slide.thumbnail method
        slide_json[:type] = 'dynamic' # Explicitly set type for rendering logic

        dynamic_content_panels = (slide_data_for_view['panels'] || []).map do |panel_config|
          panel_output = panel_config.deep_dup # Start with all existing panel config

          if panel_config['media_type'] == 'video' && panel_config['video_id'].present?
            video_record = videos_by_id[panel_config['video_id']] # Use pre-fetched video
            if video_record
              panel_output['video_render_data'] = {
                type: video_record.source, # e.g., 'youtube', 'vimeo', 'fileboy'
                source: video_record.source, # Redundant but can be useful
                external_id: video_record.external_id, # The ID (e.g. YouTube ID, Vimeo ID, Fileboy UUID)
                # Construct full URLs if needed by video partial, or let partial do it
                # This depends on how your _video.html.erb is structured
                # For example, if _video.html.erb expects a full embed URL for youtube/vimeo:
                # url: video_record.source == 'youtube' ? yt_convert_to_embed_url(video_record.external_id) : 
                #      (video_record.source == 'vimeo' ? "https://player.vimeo.com/video/#{video_record.external_id}" : video_record.external_id),
                de_video_id: video_record.id, # Pass the internal DB ID if your /video/:id endpoint uses it
                loop: panel_config['loop_video'] || false # Assuming you might add loop_video to panel data
              }
            else
              panel_output['video_render_data'] = nil # Video not found
            end
          elsif panel_config['media_type'] == 'image' && panel_config['image_url'].present?
            panel_output['image_render_url'] = fileboy_url(panel_config['image_url'])
          end
          panel_output
        end

        slide_json[:content] = {
          type: 'dynamic',
          dynamic_data: { # Pass all original dynamic slide data, augmented
            top_text: slide_data_for_view['top_text'],
            bottom_text: slide_data_for_view['bottom_text'],
            panels: dynamic_content_panels,
            max_columns: slide_data_for_view['max_columns'].presence || 'auto'
          }
        }
        # Dynamic slides might not use the traditional 'background.media'
        slide_json[:background] = { media: [], color: slide_data_for_view['background_color'].presence || '#FFFFFF' } # Simple white background for the slide itself

        if slide_data_for_view['background_image'].present?
          slide_json[:background][:media] << {
            background_size: slide_data_for_view['image_style_type'].present? ? slide_data_for_view['image_style_type'].to_s : 'cover',
            type: 'image',
            url: fileboy_url(slide_data_for_view['background_image'])
          }
        end
      else
        top_text = slide.body || slide.name || slide.intro_text
        if slide.slide_type.to_s == 'keywords' && top_text.blank?
          top_text = "Today's Rocket Words: Your 5 words and meanings to learn"
        elsif slide.slide_type.to_s == 'investigation'
          top_text = 'Writing up your experiment: Complete the investigation sheet.'
        end

        video_data_for_json = nil # Changed variable name
        # Your existing video logic
        if slide.fileboy_video_id.present? # Assuming this is a Fileboy UUID for direct player
          video_data_for_json = { type: 'fileboy_direct', url: slide.fileboy_video_id }
        end

        if !slide.fileboy_video_id.present? &&  slide.video_url.present?
          v_url, type = Video.video_embed_url(slide.video_url) 
          video_data_for_json = { type: type, url: v_url }
        end
        video_data_for_json[:loop] = true if video_data_for_json && slide.loop_video

        de_video_id_for_json = slide.video_id if slide.video_id.present? # This is the internal Video record ID

        iframe_src = slide.iframe_src
        if slide.slide_type.to_s == 'tour' && slide.tour_id
          iframe_src = "#{Rails.application.config.x.frontend_url}/tours/#{slide.tour_id}"
          frame_params = { no_exit: 1 }
          frame_params['auto_focus_video_id'] = "#{slide.autoFocusVideoId}" if slide.autoFocusVideoId.present?
          iframe_src += "?#{frame_params.to_query}"
        end

        question = slide.quip_question&.format_for_static
        slide_media_url = if slide.slide_type.to_s == 'keywords'
                        nil
                      else
                        slide.fileboy_image_id ? fileboy_url(slide.fileboy_image_id) : nil
                      end
        
        
        slide_media_url = fileboy_url(slide.homework&.cover_image_fileboy_id) if slide_media_url.nil? && slide.slide_type == 'homework'
        quiz_id = slide.quiz.id if slide.slide_type.to_s == 'complete_quiz'

        slide_json.merge!({
          top_text: top_text,
          bottom_text: slide.footer,
          background: {
            media: if slide_media_url
                      [{
                        background_size: slide.image_style_type.present? ? slide.image_style_type.to_s : 'cover',
                        type: 'image',
                        url: slide_media_url
                      }]
                    else
                      []
                    end,
            color: '#000000' # Default background color for other slides
          },
          thumbnail: slide.thumbnail,
          type: slide.slide_type.to_s, # Ensure type is always set
          content: {
            type: slide.slide_type.to_s,
            homework: slide.homework,
            homework_task: slide.homework_task,
            iframe_src: iframe_src,
            video: video_data_for_json, # For direct URL videos
            de_video_id: de_video_id_for_json, # For videos from your Video model
            quiz_question: question,
            quiz_id: quiz_id,
            keywords: if ['keywords', 'previous_keywords', 'quiz'].include?(slide.slide_type.to_s) # Only include if relevant
                        template.keywords.includes([:quiz_question]).map do |keyword|
                          { name: keyword.name, body: keyword.body, image: fileboy_url_small(keyword.fileboy_image_id), question: keyword.quiz_question&.body }
                        end
                      else
                        [] # Or nil, depending on how your frontend handles it
                      end
          }
        })
      end

      # Common for all slides
      slide_json[:weight] = index + 1 # Use the mapped index for consistent weight
      slide_json
    end

    def parse_media(slide)
      media = []
      media << { type: 'video', url: slide['Video'] } if slide['Video'].present?
      media << { type: 'image', url: slide['Image'] } if slide['Image'].present?
      media
    end

    def fileboy_rocket_word_thumbnail
      fileboy_url_small('92ff4093-f09c-4ab0-9851-cf76204f019c+rocket_word_thumbnail')
    end

    def fileboy_quiz_thumbnail
      ImageHelpers.thumbnail_url('92ff4093-f09c-4ab0-9851-cf76204f019c+quiz_thumbnail')
    end

    def fileboy_video_thumbnail(fileboy_id)
      ImageHelpers.video_thumbnail('fileboy', fileboy_id)
    end

    def fileboy_url(fileboy_id)
      ImageHelpers.image_url(fileboy_id, { format: 'webp', resize: '1200x_', quality: 75 })
    end

    def fileboy_url_small(fileboy_id)
      ImageHelpers.image_url(fileboy_id, { format: 'webp', resize: '250x_', quality: 75 })
    end

    private

    def allow_iframe
      response.headers.except! 'X-Frame-Options'
    end

    def template_sponsors(template)
      template&.new_library_units.includes([campaign_units: [campaign: :organisation]])&.flat_map do |u|
        u.campaign_units.flat_map { |unit| unit.campaign&.organisation&.fileboy_image_id }.compact
      end&.map(&method(:fileboy_url_small)) || []
    end

  end
end
