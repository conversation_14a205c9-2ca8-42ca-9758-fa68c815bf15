module StaticAdmin
  class QuipQuizzesController < StaticAdminController
    include PaginationHelper

    admin_section :content
    
    def index
      @quizzes = QuipQuiz.platform_quizzes

      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      if params[:query].present?
        @quizzes = @quizzes.where('quip_quizzes.name ILIKE :query', query: "%#{params[:query]}%")
      end

      @quizzes = safe_paginate(@quizzes.order("#{sort} #{order}"), page: params[:page])
    end
  end
end
