module StaticSchool
  class StaticSchool::MarkBooksController < StaticSchoolController
    include <PERSON>readcrumbsHelper

    before_action :set_can_view_index
    before_action :check_access
    before_action :set_base_breadcrumbs, only: [:index, :show]

    def index
      @classes = build_classes(current_user.forms)
      @all_classes = build_classes(current_user.school.forms) if current_user.is_school_admin?

      redirect_to school_mark_book_path(@classes[0][:id]) if !@can_view_index && @classes.present?
    end

    def show
      id = params.require(:id)
      form = Form.accessible_by(current_ability).find(id)

      mapped_units = form.mapped_units
      @class = form.generate_class_data(mapped_units)
      @saved_markbooks = form.markbook_sheets

      add_breadcrumb form.name, school_mark_book_path(form.id)
    end

    def update_unit_mark
      unit_id = params[:unitId].to_i
      user_id = params[:pupilId].to_i
      mark = params[:mark].to_s

      # Check if this is the teacher's first time using markbook
      check_and_send_first_time_markbook_email

      pupil_unit_mark = PupilUnitMark.find_or_initialize_by(unit_id: unit_id, user_id: user_id)
      pupil_unit_mark.mark = mark
      pupil_unit_mark.save if pupil_unit_mark.changed?
      render json: { unit_id: unit_id, user_id: user_id, mark: mark }
    end

    def update_lesson_mark
      lesson_id = params[:lessonId].to_i
      user_id = params[:pupilId].to_i
      mark = params[:mark].to_s

      # Check if this is the teacher's first time using markbook
      check_and_send_first_time_markbook_email

      pupil_lesson_mark = PupilLessonMark.find_or_initialize_by(lesson_id: lesson_id, user_id: user_id)
      pupil_lesson_mark.mark = mark
      pupil_lesson_mark.save if pupil_lesson_mark.changed?
      render json: { lesson_id: lesson_id, user_id: user_id, mark: mark }
    end

    def update_pupil_notes
      all_notes = params[:notes]

      # Check if this is the teacher's first time using markbook
      check_and_send_first_time_markbook_email

      all_notes.each do |note_obj|
        pupil_note = PupilUnitNote.find_or_initialize_by(unit_id: note_obj[:unitId], user_id: note_obj[:pupilId])
        pupil_note.note = note_obj[:notes]
        pupil_note.last_edited_by_id = current_user[:id]

        pupil_note.save if pupil_note.changed?
      end

      render json: { success: true }
    end

    def update_unit_mark_labels
      form_id = params[:id]
      labels = params[:labels]

      form = Form.accessible_by(current_ability).find(form_id)
      form.unit_mark_labels = labels
      form.save if form.changed?
      render json: { form_id: form_id, labels: labels }
    end

    private

    def check_and_send_first_time_markbook_email
      return unless current_user.teacher?
      return unless current_user.allows_notification?(:activity)

      # Check if this teacher has ever created any marks before for pupils in their forms
      pupil_ids = current_user.pupils.pluck(:id)
      unit_ids = current_user.forms.joins(:form_units).pluck('form_units.new_library_unit_id')
      lesson_ids = current_user.lessons.pluck(:id)

      has_used_markbook_before = PupilUnitMark.where(user_id: pupil_ids, unit_id: unit_ids).exists? ||
                                 PupilLessonMark.where(user_id: pupil_ids, lesson_id: lesson_ids).exists?

      # If they haven't used markbook before, send the email
      unless has_used_markbook_before
        SchoolMailer.markbook_first_time_usage(current_user).deliver_now
      end
    end

    public

    def build_sheet
      id = params.require(:id)
      form = Form.find(id)

      data = form.build_markbook_sheet

      data[:sheet].serialize("#{Rails.root}/tmp/class_#{data[:name]}_markbook.xlsx")
      send_file("#{Rails.root}/tmp/class_#{data[:name]}_markbook.xlsx", filename: "class_#{data[:name]}_markbook.xlsx", type: 'application/vnd.ms-excel')
    end

    private

    def build_classes(forms)
      forms_data = forms
        .left_joins(:pupils, :lessons, :form_units)
        .group('forms.id', 'forms.name')
        .order('forms.name ASC')
        .pluck(
          'forms.id',
          'forms.name',
          'COUNT(DISTINCT users.id)',
          'COUNT(DISTINCT form_units.id)',
          'COUNT(DISTINCT lesson_lessons.id)'
        )

      forms_data.map do |id, name, pupil_count, unit_count, lesson_count|
        {
          id: id,
          name: name,
          number_of_pupils: pupil_count,
          number_of_units: unit_count,
          number_of_lessons: lesson_count
        }
      end
    end

    def set_can_view_index
      @can_view_index = current_user.forms.length + current_user.school.forms.length != 1
    end

    def check_access
      redirect_to helpers.universal_dashboard_link and return unless @current_user&.school.show_mark_book
    end

    def set_base_breadcrumbs
      add_root_breadcrumb @current_user, @current_pupil
      add_breadcrumb "Markbooks", school_mark_book_index_path
    end
  end
end
