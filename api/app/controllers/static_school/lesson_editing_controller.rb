module StaticSchool
  class LessonEditingController < StaticSchoolController
    include BreadcrumbsHelper
    include PaginationHelper
    
    before_action :set_template_editing_data, only: %i[show_details show_lesson_plan show_presentation show_quiz show_keywords show_documents show_delete update_details update_lesson_plan convert_new_presentation_to_slides toggle_published destroy show_json_update update_from_json modify_with_ai apply_ai_changes preview_modifications show_careers add_career add_career_path remove_career_path]
    before_action :set_base_breadcrumbs
    before_action :set_show_breadcrumbs, only:  %i[show_details show_lesson_plan show_presentation show_quiz show_keywords show_documents show_delete show_json_update show_careers]
    before_action :set_new_breadcrumbs, only: %i[new new_from_scratch new_from_templates]

    def index
      @recently_edited = current_user.user_lesson_templates.order(updated_at: :desc).limit(5)
      @folders = LessonTemplateFolder.where(user: current_user, parent_folder_id: nil).order(name: :asc)
      @all_folders = LessonTemplateFolder.where(user: current_user).order(name: :asc)
      @loose_templates = current_user.user_lesson_templates.where(lesson_template_folder_id: nil)

      @sort = params['sort'] || 'name_asc'
      @filter_year = params['year_id'] || ''
      @filter_unit = params['unit_id'] || ''
      source_template_ids = current_user.user_lesson_templates.order(updated_at: :desc).pluck(:source_template_id).compact.uniq
      @years = NewLibrary::Year.joins(units: :lesson_templates).where(lesson_templates: { id: source_template_ids }).uniq
      @units = NewLibrary::Unit.joins(:lesson_templates).where(lesson_templates: { id: source_template_ids }).distinct
      @units = @units.where(year_id: @filter_year) if @filter_year.present?
    end

    # create a new lesson - select a sub type create
    def new; end

    # create a new lesson with a name
    def new_from_scratch
      @template = Lesson::Template.new
    end

    # copy an existing lesson - main route
    def new_from_templates
      country_id = current_user&.country&.id.presence || 1

      unless params.permit(:curriculum_id).key?(:curriculum_id)
        @default_curriculum_id = current_user&.school&.new_library_curriculum_id
        redirect_to new_from_templates_school_lesson_editing_index_path(curriculum_id: @default_curriculum_id) 
        return
      end

      accessible_lessons = current_user ? current_user.accessible_lesson_templates : Lesson::Template.accessible
      lessons = accessible_lessons
                .where.not(disable_viewing: true)
                .where(viewable_only: false) # original copy page always excluded viewable only
                .joins(:available_countries, :new_library_units)
                .left_joins(:new_library_years, :new_library_curricula)
                .where(countries: { id: country_id })
                .group(:id)

      curricula = NewLibrary::Curriculum.published
      units = NewLibrary::Unit.joins(year: :curriculum)
      years = NewLibrary::Year.joins(:curriculum).where('new_library_curricula.published = ?', true)

      curriculum_id = params[:curriculum_id]
      if curriculum_id.present?
        lessons = lessons.where(new_library_curricula: { id: curriculum_id })
        years = years.where(curriculum_id: curriculum_id)
        units = units.where(year_id: years.pluck(:id))
      end

      if params[:year_id].present?
        lessons = lessons.where(new_library_years: { id: params[:year_id] })
        units = units.where(year_id: params[:year_id])
        # Do not limit curricula when selecting a year
      end

      lessons = lessons.where(new_library_units: { id: params[:unit_id] }) if params[:unit_id].present?

      year_id = params[:year_id]
      unit_id = params[:unit_id]

      invalid_year = year_id.present? && !years.find_by(id: year_id)
      invalid_unit = unit_id.present? && !units.find_by(id: unit_id)

      if invalid_year
        # clear year and unit as these cant be selected on this curriculum
        redirect_to new_from_templates_school_lesson_editing_index_path(curriculum_id: curriculum_id, unit_id: nil, year_id: nil, query: params[:query], ids: params[:ids])
        return
      end

      if invalid_unit
        # clear unit as this cant be selected on this year
        redirect_to new_from_templates_school_lesson_editing_index_path(curriculum_id: curriculum_id, year_id: year_id, unit_id: nil, query: params[:query], ids: params[:ids])
        return
      end

      @units = units
               .joins(:lesson_templates, year: :curriculum)
               .group(:id)
               .where(new_library_curricula: { country_id: country_id })
               .order(weight: :asc, name: :asc)
               .pluck_to_hash(
                 :id,
                 :name,
                 year: 'FIRST(new_library_years.name)',
                 curriculum: 'FIRST(new_library_curricula.name)'
               )

      @years = years
               .joins(:lesson_templates, :curriculum)
               .group(:id)
               .where(new_library_curricula: { country_id: country_id })
               .order(name: :asc)
               .pluck_to_hash(
                 :id,
                 :name,
                 curriculum: 'FIRST(new_library_curricula.name)'
               )

      @curricula = curricula
                   .joins(years: :lesson_templates)
                   .group(:id)
                   .where(country_id: country_id)
                   .order(name: :asc)
                   .pluck_to_hash(:id, :name)

      lessons = lessons.where('lesson_templates.name ILIKE :query OR new_library_units.name ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      @lessons = lessons.limit(30).order(name: :asc)

      @selected_lessons = Lesson::Template.accessible_by(current_ability).where(id: params[:ids])
      @use_smart_combine = params[:use_smart_combine] == 'true'
    end

    def accept_copy
      unless current_user.can_create_lessons?
        redirect_to school_lesson_editing_index_path, alert: 'Unauthorized'
        return
      end

      # support updates if development && update_id is present
      update_lesson_template = Lesson::Template.accessible_by(current_ability).find(params[:update_id]) if params[:update_id].present? && Rails.env.development?

      @lesson_data = JSON.parse(params[:lesson_data])
      service = TemplateJsonService.new
      begin
        if update_lesson_template
          service.template = update_lesson_template
          service.update_from_json(
            @lesson_data,
            TemplateJsonService::UPDATABLE_SECTIONS, # all sections
            no_validate: true # don't validate this template create against the schema
          )
        else
          service.create_from_json(
            @lesson_data,
            user: current_user,
            no_validate: true # don't validate this template create against the schema
          )
        end
        lesson_template = service.template
      rescue => e
        Rails.logger.error("Error mutating template: #{e.message}\n#{e.backtrace.join("\n")}")
        ErrorLog.create(
          error: {
            errorData: e.message,
            backtrace: 'lesson-editing-controller',
            user: current_user&.id,
          }.to_json
        )
        flash[:alert] = 'An error occurred duplicating the lesson templates.'
        render :preview
        return
      end

      unless lesson_template&.persisted?
        flash[:alert] = 'Error creating lesson.'
        render :preview
        return
      end

      redirect_to show_details_school_lesson_editing_path(lesson_template), notice: update_lesson_template.present? ? 'Lesson updated.' : 'Lesson created.'
    end

    def create
      unless current_user.can_create_lessons?
        redirect_to school_lesson_editing_index_path, alert: 'Unauthorized'
        return
      end

      data = params.require(:lesson_template).permit(:name)
      prev_id = Lesson::Template.last.id
      machine_name = "ug_#{prev_id + 1}_#{current_user.id}"

      # Setting default author to the relevant author profile when creating a new template.
      author_record = current_user.employer? ? current_user.organisation&.author : current_user.author

      @template = Lesson::Template.new(
        name: data[:name],
        fileboy_image_id: data[:fileboy_image_id],
        machine_name: machine_name,
        user: current_user,
        organisation: current_user.organisation,
        user_generated: true,
        use_new_presentation: true,
        author_ids: author_record ? [author_record.id] : []
      )

      authorize! :create, @template

      if @template.save
        begin
          # generate a default presentation with no slides for the template
          # if this fails, user can still create a presentation manually on the edit page
          generate_default_presentation(@template)
        rescue => e
          Rails.logger.error("Error creating default presentation: #{e.message}\n#{e.backtrace.join("\n")}")
        end

        if current_user.beta_feature_enabled?(:new_lesson_editor)
          redirect_to show_details_school_lesson_editing_path(@template), notice: 'Lesson created.'
        else
          redirect_to "/s/lesson-templates/#{@template.id}", notice: 'Lesson created.'
        end
      else
        Rails.logger.error("Error creating lesson: #{@template.errors.full_messages}")
        flash[:alert] = 'An error occurred creating the lesson.'
        render :new_from_scratch
      end
    rescue => e
      Rails.logger.error("Error creating lesson: #{e.message}\n#{e.backtrace.join("\n")}")
      flash[:alert] = 'An error occurred creating the lesson.'
      render :new_from_scratch
    end

    def update_details
      if @lesson_template.update(lesson_template_details_params)
        redirect_to show_details_school_lesson_editing_path(@lesson_template), notice: 'Template updated'
      else
        render :edit
      end
    rescue => e
      Rails.logger.error e
      render :show_details, alert: 'An error occurred updating the template details.'
    end

    def update_lesson_plan
      if @lesson_template.update(lesson_plan_params)
        redirect_to show_lesson_plan_school_lesson_editing_path(@lesson_template), notice: 'Lesson plan updated'
      else
        Rails.logger.error("Error updating lesson plan: #{@lesson_template.errors.full_messages}")
        render :show_lesson_plan, alert: 'An error occurred updating the lesson plan.'
      end
    rescue => e
      Rails.logger.error("Error updating lesson plan: #{e.message}\n#{e.backtrace.join("\n")}")
      render :show_lesson_plan, alert: 'An error occurred updating the lesson plan.'
    end

    # FOLDER MANAGEMENT
    def manage_folder
      data = params.permit(:id, :name, :parent_folder_id)

      if data[:id].present?
        update_folder(data) if data[:name].present?
      else
        create_folder(data)
      end
      redirect_to school_lesson_editing_index_path
    rescue => e
      Rails.logger.error("Error managing folder: #{e.message}\n#{e.backtrace.join("\n")}")
      flash[:alert] = 'An error occurred managing the folder.'
      redirect_to school_lesson_editing_index_path
    end

    def move_templates
      folder_id = params.require(:id)
      folder = LessonTemplateFolder.accessible_by(current_ability).find(folder_id)
      template_ids = params.require(:lesson_template_ids).split(',')

      current_user.user_lesson_templates.where(id: template_ids).update_all(lesson_template_folder_id: folder.id)

      redirect_to school_lesson_editing_index_path, notice: 'Lessons moved.'
    end

    def clear_folder
      template_ids = params.require(:lesson_template_ids).split(',')
      current_user.user_lesson_templates.where(id: template_ids).update_all(lesson_template_folder_id: nil)
      render json: { saved: true } and return
    rescue => e
      Rails.logger.error("Error clearing folder: #{e.message}\n#{e.backtrace.join("\n")}")
      render json: { saved: false, error: e.message }
    end

    def delete_folder
      folder = LessonTemplateFolder.accessible_by(current_ability).find_by(id: params[:id])
      authorize! :destroy, folder

      render json: { saved: true } and return if folder.destroy
      render json: { saved: false, error: folder.errors.full_messages.join(', ') }
    rescue => e
      Rails.logger.error("Error deleting folder: #{e.message}\n#{e.backtrace.join("\n")}")
      render json: { saved: false, error: e.message }
    end

    def show_details
      @scientific_enquiry_types = ScientificEnquiryType.all.order(:title)
    end

    def show_lesson_plan; end

    def show_presentation
      @slides = @lesson_template.slides.ordered.includes(%i[video tour quip_question template]).where.not(slide_type: %w[intro outro])
    end

    def show_quiz
      @quiz = @lesson_template.quip_quiz || QuipQuiz.new(lesson_template: @lesson_template)
    end

    def show_keywords
      @keywords = @lesson_template.keywords.includes([:quiz_question])
      @slides = @lesson_template.slides.map do |slide|
        name = slide.body.present? ? "#{slide.user_friendly_slide_type}: #{slide.body}" : slide.slide_type
        ["#{slide.weight} - #{name.truncate(40)}", slide.id]
      end
    end

    def show_documents; end

    def ai_debug_update
      raw = params[:json_data]
      json = JSON.parse(raw) rescue nil
      raise 'Invalid JSON' unless json
      prompt = params[:prompt]
      raise 'Prompt is required' if prompt.blank?
      new_json = LessonModificationService.new.modify_lesson(json, prompt)
      render json: new_json
    end

    def show_delete; end

    # DELETE /admin/lesson-templates/:id
    def destroy
      if @lesson_template.lessons.exists?
        redirect_to show_delete_school_lesson_editing_path(@lesson_template), alert: 'Cannot delete template with associated lessons'
      else
        @lesson_template.destroy
        redirect_to school_lesson_editing_index_path, notice: 'Template deleted'
      end
    end

    def convert_new_presentation_to_slides
      PresentationConverterService.new(@lesson_template.id).convert!
      redirect_to show_presentation_school_lesson_editing_path(@lesson_template), notice: 'Presentation Converted'
    rescue => e
      Rails.logger.error("Error converting presentation: #{e.message}\n#{e.backtrace.join("\n")}")
      ErrorLog.create(error: { errorData: e, backtrace: e.backtrace, lesson_template_id: @lesson_template.id }.to_json)

      redirect_to show_presentation_school_lesson_editing_path(@lesson_template), alert: 'We were unable to convert this presentation. If this issue persists, please contact support.'
    end

    def toggle_published
      can_publish = @lesson_template.user_can_publish?

      unless can_publish
        flash[:error] = 'Cannot published due to incomplete tasks'
        redirect_to show_details_school_lesson_editing_path(@lesson_template)
        return
      end

      new_state = !@lesson_template.available
      if @lesson_template.update(available: new_state)
        flash[:notice] = "Lesson #{new_state ? '' : 'un'}published"
        redirect_to show_details_school_lesson_editing_path(@lesson_template)
        return
      end

      flash[:error] = @lesson_template.errors.full_messages
      redirect_to show_details_school_lesson_editing_path(@lesson_template)
    end

    def combine_status
      cache_key = params[:cache_key]
      if cache_key.blank?
        render json: { success: true, status: 'error', error: 'There was an error retrieving the status. Please try again.' }, status: :bad_request
        return
      end
      unless Rails.cache.exist?(cache_key)
        render json: { success: true, status: 'error', error: 'No data found for the provided cache key.' }, status: :not_found
        return
      end

      result = JSON.parse(Rails.cache.read(cache_key))
      status = result['status']

      if status == 'error'
        render json: { success: true, status: 'error', error: result['errors']&.join(', ') }
        return
      end

      render json: {
        success: true,
        cache_key: cache_key,
        status: status || 'error',
        is_loading: status == 'processing',
      }
    end

    def preview
      ids = params[:ids] || []
      primary_template_id = params[:primary_template_id] || ids.first
      @lesson_template = Lesson::Template.accessible_by(current_ability).find(primary_template_id)
      other = Lesson::Template.accessible_by(current_ability).where(id: ids).where.not(id: primary_template_id)

      smart_combine = params[:use_smart_combine] == 'true'
      if smart_combine
        ids = [primary_template_id] + other.pluck(:id)
        @cache_key = "user_#{current_user.id}_lesson_combine_job_#{ids.map(&:to_i).sort.join('_')}"
        if Rails.cache.exist?(@cache_key)
          result = JSON.parse(Rails.cache.read(@cache_key))
          status = result['status']
          @is_loading = status == 'processing'
          if status == 'error'
            Rails.logger.error "Error combining lessons: #{result['errors']&.join(', ')}"
            redirect_to new_from_templates_school_lesson_editing_index_path(ids: params[:ids], smart_combine: params[:smart_combine]), alert: 'An error occurred combining the lessons'
            return
          end
          @lesson_data = result['content'] if result['status'] == 'completed'
        else
          @is_loading = true
          LessonCombineJob.perform_later(@cache_key, ids)
        end
      else
        @lesson_data = TemplateJsonService.new(@lesson_template.id).to_json
        other.each do |template|
          partial = TemplateJsonService.new(template.id).to_json
          (partial.dig('presentation', 'slides') || []).each do |slide|
            slide['weight'] = @lesson_data['presentation']['slides'].length + 1
            @lesson_data['presentation']['slides'] << slide
          end
          @lesson_data['documents'] += partial['documents'] || []
        end

        @lesson_data.deep_transform_keys!(&:to_s)
      end

      if request.format.json?
        render json: @lesson_data
      else
        render :preview
      end
    end

    def generate_with_ai; end

    def show_json_update
      return unless params[:cache_key].present?
      @lesson_data = Rails.cache.read(params[:cache_key])
      Rails.cache.delete(params[:cache_key]) if @lesson_data
    end

    def preview_modifications
      unless params[:cache_key].present?
        flash[:alert] = 'No cache key provided for preview.'
        redirect_to school_lesson_editing_index_path and return
      end
      cache_data = Rails.cache.read(params[:cache_key])
      # parse to json for the preview page
      @lesson_data = JSON.parse(cache_data) if cache_data.is_a?(String)
      # Rails.cache.delete(params[:cache_key]) if @lesson_data
      @update_id = @lesson_template.id
      render :preview
    end

    def update_from_json
      @lesson_data = params[:lesson_data]
      @disable_validations = params[:disable_validations] == '1'

      if @lesson_data.blank?
        flash[:alert] = 'No JSON data provided.'
        render :show_json_update and return
      end

      begin
        json_data = JSON.parse(@lesson_data)
        enabled_sections = TemplateJsonService::UPDATABLE_SECTIONS.select { |section| json_data.key?(section.to_s) }
        # If given a partial, do the update then render the first updated section page
        partial = enabled_sections.count < TemplateJsonService::UPDATABLE_SECTIONS.count
        if partial
          service = TemplateJsonService.new(@lesson_template.id)
          service.update_from_json(json_data, enabled_sections, no_validate: @disable_validations)
          # redirect to the first of the updated sections
          section_url = {
            lesson: show_details_school_lesson_editing_path,
            lesson_plan: show_lesson_plan_school_lesson_editing_path,
            keywords: show_keywords_school_lesson_editing_path,
            documents: show_documents_school_lesson_editing_path,
            presentation: show_presentation_school_lesson_editing_path,
            quiz: show_quiz_school_lesson_editing_path,
          }[enabled_sections.first.to_sym]

          redirect_to section_url, notice: 'Template updated from JSON'
          return
        end

        # if given an entire template, render the preview page with the template data
        @lesson_data = json_data
        @update_id = @lesson_template.id
        render :preview and return
      rescue JSON::ParserError => e
        Rails.logger.error("JSON parsing error: #{e.message}\n#{e.backtrace.join("\n")}")
        @validation_error = e.message
        flash[:alert] = 'Invalid JSON format.'
        render :show_json_update and return
      rescue => e
        Rails.logger.error("Error updating template from JSON: #{e.message}\n#{e.backtrace.join("\n")}")
        @validation_error = e.message
        flash[:alert] = 'An error occurred updating the template from JSON.'
        render :show_json_update and return
      end
    end

    def modify_with_ai
      prompt = params[:prompt]&.strip
      ai_scope = params[:ai_scope]

      if prompt.blank?
        if request.xhr?
          render json: { success: false, error: 'Please provide a modification request.' }
        else
          flash[:alert] = 'Please provide a modification request.'
          redirect_back(fallback_location: show_details_school_lesson_editing_path(@lesson_template))
        end
        return
      end

      begin
        # Get the current lesson JSON
        lesson_json = TemplateJsonService.new(@lesson_template.id).to_json

        # Initialize the modification service
        modification_service = LessonModificationService.new

        if ai_scope == 'whole_lesson'
          # Process the entire lesson with explicit scope
          result = modification_service.modify_lesson(lesson_json, prompt, scope: 'whole_lesson')

          if request.xhr?
            # Return the full lesson JSON for preview
            render json: {
              success: true,
              message: "#{result[:summary]}",
              json: result[:lesson],
              accept_url: apply_ai_changes_school_lesson_editing_path(@lesson_template),
              is_whole_lesson: true
            }
          else
            @new_json = result[:lesson].to_json
            @ai_success_message = result[:summary]
            render :show_json_update
          end
        else
          # Process only the specified section
          section_name = ai_scope
          result = modification_service.modify_lesson_section(lesson_json, prompt, section_name)

          if request.xhr?
            # Return the section JSON for preview
            render json: {
              success: true,
              message: "#{result[:summary]}",
              json: result[:lesson],
              accept_url: apply_ai_changes_school_lesson_editing_path(@lesson_template),
              is_whole_lesson: false
            }
          else
            @new_json = result[:lesson].to_json
            @ai_success_message = result[:summary]
            render :show_json_update
          end
        end

        Rails.logger.info("AI modification completed for lesson #{@lesson_template.id}, scope: #{ai_scope}")
      rescue => e
        Rails.logger.error("Error in AI modification: #{e.message}\n#{e.backtrace.join("\n")}")

        if request.xhr?
          render json: { success: false, error: e.message }
        else
          @ai_error_message = e.message
          render :show_json_update
        end
      end
    end

    def apply_ai_changes
      lesson_data = params[:lesson_data]
      is_whole_lesson = params[:is_whole_lesson] == 'true'

      if lesson_data.blank?
        if request.xhr?
          render json: { success: false, error: 'No lesson data provided.' }
        else
          flash[:alert] = 'No lesson data provided.'
          redirect_back(fallback_location: show_details_school_lesson_editing_path(@lesson_template))
        end

        return
      end

      begin
        cache_key = "ai_lesson_#{@lesson_template.id}_#{SecureRandom.hex(8)}"
        Rails.cache.write(cache_key, lesson_data, expires_in: 1.hour)

        if is_whole_lesson
          redirect_url = preview_modifications_school_lesson_editing_path(@lesson_template, cache_key: cache_key)
        else
          json_data = JSON.parse(lesson_data)
          enabled_sections = TemplateJsonService::UPDATABLE_SECTIONS.select { |section| json_data.key?(section.to_s) }
          # If given a partial, do the update then render the first updated section page
          service = TemplateJsonService.new(@lesson_template.id)
          service.update_from_json(json_data, enabled_sections, no_validate: true)
          # redirect to the first of the updated sections
          section_url = {
            lesson: show_details_school_lesson_editing_path,
            lesson_plan: show_lesson_plan_school_lesson_editing_path,
            keywords: show_keywords_school_lesson_editing_path,
            documents: show_documents_school_lesson_editing_path,
            presentation: show_presentation_school_lesson_editing_path,
            quiz: show_quiz_school_lesson_editing_path,
          }[enabled_sections.first.to_sym]

          redirect_url = section_url
        end

        if request.xhr?
          render json: {
            success: true,
            redirect_url: redirect_url
          }
        else
          redirect_to redirect_url
        end
      rescue => e
        Rails.logger.error("Error applying AI changes: #{e.message}\n#{e.backtrace.join("\n")}")
        if request.xhr?
          render json: { success: false, error: e.message }
        else
          flash[:alert] = 'An error occurred applying the changes.'
          redirect_back(fallback_location: show_details_school_lesson_editing_path(@lesson_template))
        end
      end
    end

    def show_careers
      query = params[:query].presence || ''
      @careers = @lesson_template.career_paths.where('career_name ILIKE ?', "%#{query}%")
    end

    def add_career
      query = params[:query].presence || ''
      @careers = CareerPath.completed.where.not(id: @lesson_template.career_paths.ids).where('career_name ILIKE ?', "%#{query}%").order(:career_name)
      @careers = safe_paginate(@careers, per_page: 50, page: params[:page])
    end

    def add_career_path
      kwargs = params.permit(:query)
      career_path = CareerPath.find_by(id: params[:career_path_id])
      redirect_to add_career_school_lesson_editing_path(@lesson_template, **kwargs), alert: 'Career path not found' and return if career_path.nil?
      @lesson_template.career_paths << career_path unless @lesson_template.career_paths.include?(career_path)
      redirect_to add_career_school_lesson_editing_path(@lesson_template, **kwargs), notice: 'Career path added'
    rescue => e
      Rails.logger.error("Error adding career path: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to add_career_school_lesson_editing_path(@lesson_template, **kwargs), alert: 'Error adding career path'
    end

    def remove_career_path
      kwargs = params.permit(:query)
      career_path = CareerPath.find_by(id: params[:career_path_id])
      redirect_to show_careers_school_lesson_editing_path(@lesson_template, **kwargs), alert: 'Career path not found' and return if career_path.nil?
      @lesson_template.career_paths.delete(career_path)
      redirect_to show_careers_school_lesson_editing_path(@lesson_template, **kwargs), notice: 'Career path removed'
    rescue => e
      Rails.logger.error("Error removing career path: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to show_careers_school_lesson_editing_path(@lesson_template, **kwargs), alert: 'Error removing career path'
    end

    private

    def set_template_editing_data
      @lesson_template = Lesson::Template.accessible_by(current_ability).find(params[:id])
    end

    def create_folder(data)
      folder = LessonTemplateFolder.new(user: @current_user, name: data[:name], parent_folder_id: data[:parent_folder_id].presence || nil)
      authorize! :create, folder

      if folder.save
        flash[:notice] = 'Folder created.'
      else
        Rails.logger.error("Error creating folder: #{folder.errors.full_messages}")
        flash[:alert] = folder.errors.full_messages.join(', ')
      end

      folder
    end

    def update_folder(data)
      folder = LessonTemplateFolder.accessible_by(current_ability).find(data[:id])
      authorize! :update, folder

      if folder.update(name: data[:name], parent_folder_id: data[:parent_folder_id].presence || nil)
        flash[:notice] = 'Folder updated.'
      else
        Rails.logger.error("Error updating folder: #{folder.errors.full_messages}")
        flash[:alert] = folder.errors.full_messages.join(', ')
      end

      folder
    end

    def generate_default_presentation(template)
      presentation = NewPresentation.new(
        name: template.name,
        user_id: template.user.id,
        settings: { "font": '', "backgroundColor": '', "presentationBackground": { "type": 'imageUrl', "value": '' } },
        lesson_template_id: template.id
      )
      presentation.save!
      presentation
    end

    def lesson_template_details_params
      params.require(:lesson_template).permit(:name, :fileboy_image_id, objectives: [], scientific_enquiry_type_ids: [])
    end

    def lesson_plan_params
      params.require(:lesson_template).permit(
        :lesson_plan_layout,
        :intent,
        :new_lesson_plan_resources,
        :implementation,
        :impact_assessment,
        :specification,
        :ks4_learning_outcomes,
        :core_knowledge,
        :analogies_models,
        :ks4_teacher_mastery
      )
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb "Lesson Editing", school_lesson_editing_index_path
    end

    def set_show_breadcrumbs
      add_breadcrumb @lesson_template.name, show_details_school_lesson_editing_path(@lesson_template)
      add_breadcrumb action_name.titleize.gsub("Show ", "")
    end

    def set_new_breadcrumbs
      add_breadcrumb "Create Lesson", new_school_lesson_editing_path
      
      if action_name == "new_from_scratch"
        add_breadcrumb "From Scratch"
      elsif action_name == "new_from_templates"
        add_breadcrumb "From Template"
      end
    end
  end
end
