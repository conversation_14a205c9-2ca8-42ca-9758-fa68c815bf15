module StaticSchool
  class WeeklyDigestController < StaticSchoolController
    def index
      stats = DigestService.new(current_user).personal
      @past_week_stats = stats.past_week_stats
      @upcoming_week_lessons = stats.upcoming_week_lessons
    end

    def school_activity
      redirect_to school_weekly_digest_index_path unless @current_user.is_school_admin?
      stats = DigestService.new(current_user).school
      @teacher_stats = stats.teacher_stats
      @pupil_stats = stats.pupil_stats
    end

    def send_digest
      SchoolMailer.weekly_digest(@current_user).deliver_now
      redirect_to school_weekly_digest_index_path, notice: 'Weekly digest email sent successfully.'
    end
  end
end
