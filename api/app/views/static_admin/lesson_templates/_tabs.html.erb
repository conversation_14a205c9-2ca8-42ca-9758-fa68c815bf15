<div class="pl-4 text-white">
  <%= render PageTabsComponent.new(tabs: [
    { name: "Edit", path: edit_admin_lesson_template_path, active: active_tab == :edit },
    { name: "Lesson Plan", path: lesson_plan_admin_lesson_template_path, active: active_tab == :lesson_plan },
    { name: "Quiz", path: quiz_admin_lesson_template_path, active: active_tab == :quiz },
    { name: "Keywords", path: keywords_admin_lesson_template_path, active: active_tab == :keywords },
    { name: "Documents", path: documents_admin_lesson_template_path, active: active_tab == :documents },
    { name: "Presentation", path: presentation_admin_lesson_template_path, active: active_tab == :presentation },
    { name: "Feedback", path: feedback_admin_lesson_template_path, active: active_tab == :feedback },
    { name: "AI Proof", path: proof_admin_lesson_template_path, active: active_tab == :proof },
    @current_user.beta_feature_enabled?(:september_1) ? { name: "Careers", path: careers_admin_lesson_template_path, active: active_tab == :careers } : nil,
    { name: "Delete", path: delete_admin_lesson_template_path, active: active_tab == :delete },
].compact) %>
</div>
