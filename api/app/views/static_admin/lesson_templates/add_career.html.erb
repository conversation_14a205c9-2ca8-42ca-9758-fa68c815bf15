<% content_for :title, "Careers | #{@lesson_template.name} | Edit Lesson Template" %>
<%= render "shared/form_errors", model: @lesson_template %>
<%= render AdminPageBannerComponent.new(
  title: "Edit #{@lesson_template.name}",
  back_to: { path: admin_lesson_templates_path, text: "All lesson templates" }
) %>
<div class="text-white">
  <%= render "tabs", active_tab: :careers %>
</div>
<%= render 'careers_table', 
  lesson_template: @lesson_template, 
  careers: @careers,
  add_path: ->(lesson_template) { add_career_admin_lesson_template_path(lesson_template) },
  edit_path: ->(lesson_template) { careers_admin_lesson_template_path(lesson_template) },
  add_to_template_path: ->(lesson_template, career_path) { add_career_path_admin_lesson_template_path(lesson_template, career_path_id: career_path.id, **params.permit(:query, :page)) },
  remove_from_template_path: ->(lesson_template, career_path) { remove_career_path_admin_lesson_template_path(lesson_template, career_path_id: career_path.id, **params.permit(:query, :page)) },
  mode: :search
%>
