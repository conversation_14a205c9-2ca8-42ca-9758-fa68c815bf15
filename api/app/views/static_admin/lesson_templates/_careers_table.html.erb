
<%# Careers Table %>
<% search_path = mode == :search ? add_path.call(lesson_template) : edit_path.call(lesson_template) %>
<div id="careers-table" class="admin-table">
  <div class="flex justify-between items-center p-4 border-b sticky top-0 bg-white rounded-t-lg z-10">
    <% if mode == :search %> 
      <%= link_to edit_path.call(lesson_template), class: "btn btn-base btn-cyan" do %>
        <i class="fa-solid fa-chevron-left mr-2"></i>Careers
      <% end %>
    <% else %>
      <%= link_to add_path.call(lesson_template), class: "btn btn-base btn-cyan" do %>
        <i class="fa-solid fa-plus mr-2"></i>Add Career
      <% end %>
      <%= link_to index_v2_career_builder_index_path, class: "btn btn-base btn-cyan", target: "_blank" do %>
        <i class="fa-solid fa-wand-magic-sparkles mr-2"></i>Generate New Career
      <% end %>
    <% end %>
  </div>
  <div class="admin-table-filters">
    <div class="flex items-center justify-between flex-wrap w-full">
      <%= render AdminPageSearchComponent.new(index_path: search_path) %>
      <% if mode == :search %>
        <%= render AdminPaginateComponent.new(items: @careers) %>
      <% end %>
    </div>
  </div>

  <table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th scope="col" class="admin-table-header">Career Name</th>
        <th scope="col" class="admin-table-header hidden md:table-cell">Level</th>
        <th scope="col" class="admin-table-header hidden md:table-cell">Family</th>
        <th scope="col" class="admin-table-header hidden sm:table-cell">Created</th>
        <th></th>
      </tr>
    </thead>
    <% if careers.empty? %>
      <%# Empty State Placeholder %>
      <tbody class="bg-white">
        <tr>
          <td colspan="5" class="px-6 py-16 text-center">
            <% if mode == :search %>
              <div class="flex flex-col items-center justify-center space-y-4">
                <%# Icon %>
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fa-solid fa-briefcase text-2xl text-gray-400"></i>
                </div>
                
                <%# Main message %>
                <div class="space-y-2">
                  <% if params[:query].present? %>
                    <%= link_to index_v2_career_builder_index_path(name: params[:query], education_level: 'post-16'), class: "btn btn-base btn-cyan", target: "_blank" do %>
                      <i class="fa-solid fa-wand-magic-sparkles mr-2"></i>Generate Career for <%= params[:query] %>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% else %>
              <div class="flex flex-col items-center justify-center space-y-4">
                <%# Icon %>
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fa-solid fa-briefcase text-2xl text-gray-400"></i>
                </div>
                
                <%# Main message %>
                <div class="space-y-2">
                  <h3 class="text-lg font-medium text-gray-900">No careers yet</h3>
                </div>
              </div>
            <% end %>
          </td>
        </tr>
      </tbody>
    <% end %>
    <tbody id="careers-table-body" class="bg-white divide-y divide-gray-200">
      <% careers.includes(:job_family).each do |career_path| %>
        <tr class="hover:bg-gray-50">
          <td class="px-6 py-4">
            <%= render AvatarComponent.new(
              text: career_path["career_name"], 
              fileboy_image_id: nil, 
              include_text: true, 
              sub_text: "ID: #{career_path["id"]}",
              image_url: career_path.career_path.present? && career_path.career_path["image"].present? ? career_path["career_path"]["image"] : nil
            ) %>
          </td>
          <td class="px-6 py-4 text-sm text-gray-900 hidden md:table-cell">
            <%= career_path.education_level_short %>
          </td>
          <td class="px-6 py-4 text-sm text-gray-900 hidden md:table-cell">
            <%= career_path&.job_family&.name %>
          </td>
          <td class="px-6 py-4 text-sm text-gray-500 hidden sm:table-cell">
            <%= career_path.created_at.strftime("%b %d, %Y") %>
            <div class="text-xs text-gray-400">
              <%= time_ago_in_words(career_path.created_at) %> ago
            </div>
          </td>
          <td>
            <% if mode == :search %>
              <%= link_to add_to_template_path.call(lesson_template, career_path), method: :post, class: "btn btn-sm btn-cyan hover:bg-cyan-700" do %>
                <i class="fa-solid fa-plus mr-2"></i>Add
              <% end %>
            <% else %>
              <%= link_to remove_from_template_path.call(lesson_template, career_path), method: :post, data: { confirm: 'Are you sure you want to remove this career?' }, class: "btn btn-sm btn-flat-red" do %>
                <i class="fa-solid fa-trash mr-2"></i>Remove
              <% end %>
            <% end %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>
