<% 
  f = local_assigns.fetch(:form)
  quiz = local_assigns.fetch(:quiz, nil)
  is_new = local_assigns.fetch(:is_new, true)

  record_name = local_assigns.fetch(:record_name, '')
  field_type = local_assigns.fetch(:field_type, 'quip_question')
  record_type = local_assigns.fetch(:record_type, 'flow_step')

  uniq_id = SecureRandom.hex(4)
  field_id = "#{form.object_name}_quip_question_fields_#{uniq_id}".gsub(/__+/, '_')
%>

<div data-uniq-id="<%= uniq_id %>" class="field-<%= field_type %>">
  <%- if @current_user.admin? %>
    <div class="bg-blue-50 p-4 rounded-md">
      <p class="text-sm text-blue-800">
        <i class="fa-solid fa-info-circle mr-1"></i>
        To insert a question, create a quiz and <br />
        copy in the <b>Question ID</b>.
      </p>
    </div>
    <div class="border border-gray-400 p-2 rounded-md mt-2">
      <div>
        <%= f.label :quiz_id, "Quiz ID", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_field :quiz_id, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "Enter Quiz ID", onchange: "handleLoadQuipQuizLink(this);" %>
      </div>

      <div id="quiz-id-link" class="text-wrap" style=<%= quiz.present? ? '' : 'display: none' %>>
        <% if quiz.present? %>
          <a target="_blank" href="<%= edit_quiz_path(quiz) %>" class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            View Quiz
          </a>
        <% end %>
      </div>

      <div id="generate-quiz-panel" class="mt-4" style="<%= quiz.present? ? 'display: none;' : '' %>">
        <p class="font-bold">OR</p>
        <%= f.label :quiz_question_type, "Create a quiz", class: "block text-sm font-medium text-gray-700 mt-2" %>
        
        <button 
          data-uniq-id="<%= uniq_id %>" 
          type="button" 
          onclick="handleCreateNewQuiz(this)"
          class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Generate new quiz
        </button>

        <p class="text-xs text-wrap">
          <i>
            This will create a new quiz and question. This quiz will automatically be assigned to the step and the step will be saved.
            <br>
            A new browser tab will open so you can edit the quiz and question.
            <br>
            If you want to edit the quiz later, edit the step and click 'view quiz'.
          </i>
        </p>
      </div>
    </div>
  <%- else %>
    <div class="border border-gray-400 p-2 rounded-md mt-2">
      <div class="hidden">
        <%= f.label :quiz_id, "Quiz ID", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_field :quiz_id, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "Enter Quiz ID", onchange: "handleLoadQuipQuizLink(this);" %>
      </div>

      <div id="quiz-id-link" class="text-wrap" style=<%= quiz.present? ? '' : 'display: none' %>>
        <% if quiz.present? %>
          <a target="_blank" href="<%= edit_quiz_path(quiz) %>" class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            View Quiz
          </a>
        <% end %>
      </div>

      <div id="generate-quiz-panel" class="mt-4" style="<%= quiz.present? ? 'display: none;' : '' %>">
        <button 
          data-uniq-id="<%= uniq_id %>" 
          type="button" 
          onclick="handleCreateNewQuiz(this)"
          class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Generate new quiz
        </button>

        <p class="text-xs text-wrap">
          <i>
            This will create a new quiz and question. This quiz will automatically be assigned to the step and the step will be saved.
            <br>
            A new browser tab will open so you can edit the quiz and question.
            <br>
            If you want to edit the quiz later, edit the step and click 'view quiz'.
          </i>
        </p>
      </div>
    </div>
  <%- end %>
</div>

<script>
  async function handleLoadQuipQuizLink(field) {
    const quizId = field.value;
    const tableRow = field.closest("tr");

    if (!quizId) {
      console.warn("quiz_id field has no value");
      setQuizLinkNodeVisible(field, false);
      setGenerateQuizPanelVisible(field, true);
      return;
    }

    setGenerateQuizPanelVisible(field, false);

    console.log("Looking up quiz by question id", quizId);
    const url = "<%= quiz_details_path %>";
    const params = new URLSearchParams({ id: quizId });
    const request = await fetch(url + "?" + params.toString(), {
      method: "GET",
      headers: { "content-type": "application/json" },
    });
    
    if (!request.ok) {
      console.error("Error fetching url", request);
      return;
    }
    
    const resultData = await request.json();
    if (!resultData) {
      console.error("Quiz not found");
      setQuizLinkNodeVisible(field, false);
      setGenerateQuizPanelVisible(field, true);
      return;
    }

    const quizLinkNode = findChildNodeInParentTree(field, '#quiz-id-link');
    quizLinkNode.innerHTML = '';

    if (resultData.id) {
      const info = document.createElement("div");

      const typeNode = document.createElement('p');
      typeNode.classList.add('font-bold', 'text-sm');
      typeNode.textContent = resultData.name;
      info.appendChild(typeNode);

      const textNode = document.createElement('p');
      textNode.textContent = `${resultData.question_count} question${resultData.question_count !== 1 ? 's' : ''}`;
      info.appendChild(textNode);

      info.classList.add('p-2', 'rounded-md', 'bg-gray-100', 'mt-1', 'mb-1');
      quizLinkNode.appendChild(info);
    }

    let href = "";
    if (resultData.id) {
      const link = document.createElement("a");
      editing_name = encodeURIComponent("Editing <%== record_name %>");
      href = `/quiz-builder/${resultData.id}/edit?return_to_name=${editing_name}&return_to_url=${encodeURIComponent(window.location.pathname)}`;
      link.href = href;
      link.target = "_blank";
      link.textContent = 'View Quiz';
      link.classList.add(...("mt-2 inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500".split(" ")));
      quizLinkNode.appendChild(link);
    }
    
    setQuizLinkNodeVisible(field, true);
    return href;
  }
  function setQuizLinkNodeVisible(fromNode, visible) {
    const quizLinkNode = findChildNodeInParentTree(fromNode, '#quiz-id-link');
    quizLinkNode.style.display = visible ? 'block' : 'none';
    return quizLinkNode;
  }
  function setGenerateQuizPanelVisible(fromNode, visible) {
    const generatePanelNode = findChildNodeInParentTree(fromNode, '#generate-quiz-panel');
    generatePanelNode.style.display = visible ? 'block' : 'none';
    return generatePanelNode;
  }
  async function handleCreateNewQuiz(button) {
    const uid = button.getAttribute('data-uniq-id');
    const quizIdField = findChildNodeInParentTree(button, 'input[name="<%= record_type %>[quiz_id]"]');
    if (!uid) {
      console.error("handleCreateNewQuiz invoked by invalid caller missing uniq-id");
      return;
    }

    const form = button.closest('form');
    const requiredFields = form.querySelectorAll('input[required], select[required]');
    for (const field of requiredFields) {
      if (!field.reportValidity()) {
        return;
      }
    }

    const parent = button.parentElement;
    const url = "<%= quiz_builder_build_placeholder_quiz_path %>";
    const recordName = "<%= record_name %>"
    const request = await fetch(url, {
      method: "POST",
      headers: { "content-type": "application/json" },
      body: JSON.stringify({ record_name: recordName }),
    });
    
    if (!request.ok) {
      console.error("Error fetching url", request);
      return;
    }
    
    const body = await request.json();
    if (body.error) {
      console.error("Error in response", body);
      return;
    }

    const quiz_id = body?.id;
    if (!quiz_id) {
      console.error("Expected a question id but didn't receive one!?", body);
      return;
    }

    quizIdField.value = quiz_id;
    quizIdField.dispatchEvent(new Event('change', { bubbles: true }));

    const parentPanel = findChildNodeInParentTree(button, '#generate-quiz-panel');
    parentPanel.style.display = "none";

    handleLoadQuipQuizLink(quizIdField).then(href => {
      if (href) {
        // Safari blocks window.open in async code
        setTimeout(() => {
          window.open(href, '_blank');
        })
      }
      const form = button.closest('form');
      addParamToFormAction(form, 'reopen_draw', 'true');
      form.submit();
    });
  }
  /**
   * Traverses up the DOM tree to find a child element with the specified selector
   * @param {HTMLElement} node - Starting node to traverse from
   * @param {string} selector - CSS selector to search for
   * @returns {HTMLElement|null} - Found element or null if not found
   */
  function findChildNodeInParentTree(node, selector) {
    let parent = node;
    let depth = 0;
    let selected = null;
    
    while (!selected && depth < 4) {
      parent = parent.parentElement;
      selected = parent.querySelector(selector);
      depth++;
    }
    
    if (!selected) {
      console.warn("Could not find a parent element with selector: " + selector);
    }
    
    return selected;
  }

  function addParamToFormAction(form, name, value) {
    const currentAction = form.action;
    const url = new URL(currentAction);
    url.searchParams.set(name, value);
    form.action = url.toString();
  }
</script>
