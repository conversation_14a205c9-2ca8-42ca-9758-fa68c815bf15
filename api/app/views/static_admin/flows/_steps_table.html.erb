<%# Include Sortable.js from CDN directly in the view %>
<%= javascript_include_tag 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js' %>

<%# Helper for step type specific styling %>
<%
def step_type_styles(step_type)
  case step_type.to_s
  when 'video'
    { tag_class: "bg-red-100 text-red-800", thumb_bg: "bg-red-50", icon_class: "fa-solid fa-video text-red-400" }
  when 'rich_text'
    { tag_class: "bg-blue-100 text-blue-800", thumb_bg: "bg-blue-50", icon_class: "fa-solid fa-align-left text-blue-400" }
  when 'quiz'
    { tag_class: "bg-purple-100 text-purple-800", thumb_bg: "bg-purple-50", icon_class: "fa-solid fa-question-circle text-purple-400" }
  else
    { tag_class: "bg-gray-100 text-gray-800", thumb_bg: "bg-gray-100", icon_class: "fa-regular fa-circle text-gray-400" }
  end
end
%>

<div
    id="steps-container"
    data-flow-id="<%= @flow.id %>"
  >
  <div class="flex justify-between items-center gap-2 p-4 border-b sticky top-0 bg-white rounded-t-lg z-10 flex-wrap">
    <div class="flex gap-4 items-center flex-wrap">
      <!-- Future: Add AI generation button here if needed -->
    </div>
    <%# New Step Button (triggers drawer) %>
    <%= render(DrawerComponent.new(title: "Add New Step", id: "new-step-drawer", position: "right")) do |drawer| %>
      <% drawer.with_trigger do %>
        <button class="btn btn-base btn-cyan">
          <i class="fa-solid fa-plus mr-2"></i>Add Step
        </button>
      <% end %>
      <%# New Step Form %>
      <%- step = FlowStep.new %>
      <%= form_with(
            model: step,
            url: new_step_path.call(@flow),
            local: true,
            scope: :flow_step,
            id: "new-step-form"
          ) do |f| %>
        <div class="space-y-4">
          <div class="field-base">
            <%= f.label :step_type, class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :step_type,
                  options_for_select([
                    ['Video', 'video'],
                    ['Rich Text', 'rich_text'],
                    ['Quiz', 'quiz']
                  ]),
                  { include_blank: "Select a step type" },
                  { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 step-type-select", required: true }
                %>
          </div>
          
          <div class="field-base">
            <%= f.label :name, class: "block text-sm font-medium text-gray-700", required: true %>
            <%= f.text_field :name, required: true, placeholder: "Enter step name...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
          </div>
          
          <div class="field-base">
            <%= f.label :description, class: "block text-sm font-medium text-gray-700" %>
            <%= f.text_area :description, rows: 3, placeholder: "Describe what this step covers...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
          </div>
          
          <%# Rich Text Content Field %>
          <div class="field-rich_text">
            <%= f.label :rich_text_content, "Content", class: "block text-sm font-medium text-gray-700" %>
            <%= f.text_area :rich_text_content, rows: 6, placeholder: "Enter the rich text content for this step...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 ckeditor-custom" %>
          </div>
          
          <%# Video Field %>
          <div class="field-video">
            <%= render 'shared/form_fields/video_select_field', 
                form: f, 
                allowed_video_types: ['fileboy'], 
                field_name: :video_id,
                label: "Select Video",
                uniq_id: "new_step_video" %>
          </div>
          
          <%# Quiz Field %>
          <div class="field-quiz">
            <%= render partial: 'quiz_field', locals: { form: f, quiz: step.quiz, is_new: true, record_name: @flow.name } %>
          </div>
          
          <div class="field-base">
            <%= f.label :estimated_duration_minutes, "Duration (minutes)", class: "block text-sm font-medium text-gray-700" %>
            <%= f.number_field :estimated_duration_minutes, min: 0, placeholder: "e.g. 5", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
          </div>
          
          <%= render "shared/form_fields/image_field",
              form: f,
              field_name: :fileboy_image_id,
              current_image_id: nil,
              label: "Step Thumbnail", 
              preview_size: "300x_",
              image_style: "cover",
              uniq_id: "new_step" 
          %>
          
          <div class="field-base">
            <%= f.label :required, class: "inline-flex items-center" %>
            <%= f.check_box :required, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 mr-2" %>
            <span class="text-sm font-medium text-gray-700">Required step</span>
          </div>
          
          <div class="flex justify-end space-x-2 pb-16">
            <button type="button" class="px-4 py-2 border rounded text-gray-700" data-action="drawer#close">
              Cancel
            </button>
            <%= f.submit "Save", class: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" %>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
  
  <%# Steps Table %>
  <div id="steps-table">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Order</th>
          <th class="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thumbnail</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Step</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody id="steps-table-body" class="bg-white divide-y divide-gray-200">
        <% if @flow_steps.empty? %>
          <tr>
            <td colspan="6" class="px-6 py-16 text-center">
              <div class="flex flex-col items-center justify-center space-y-4">
                <%# Icon %>
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <i class="fa-solid fa-list text-2xl text-gray-400"></i>
                </div>
                <div class="space-y-2">
                  <h3 class="text-lg font-medium text-gray-900">No steps yet</h3>
                  <p class="text-sm text-gray-500 max-w-sm">
                    Get started by adding your first step to this flow.
                  </p>
                </div>
              </div>
            </td>
          </tr>
        <% else %>
          <% @flow_steps.each_with_index do |step, index| %>
            <% styles = step_type_styles(step.step_type) %>
            <tr data-step-id="<%= step.id %>">
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span class="step-order font-medium"><%= index + 1 %></span>
                  <i class="fa-solid fa-grip-vertical ml-2 text-gray-400 handle"></i>
                </div>
              </td>
              <td class="py-4 whitespace-nowrap w-1">
                <span class="px-2 py-1 text-xs font-medium rounded-full <%= styles[:tag_class] %>">
                  <%= step.step_type.humanize %>
                </span>
              </td>
              <td class="px-4 py-4 whitespace-nowrap w-36">
                <% if step.fileboy_image_id.present? %>
                  <img src="<%= fileboy_image_url(step.fileboy_image_id) %>" alt="Step thumbnail" class="h-16 w-24 object-cover rounded border border-gray-200">
                <% elsif step.video && step.video.thumbnail_url %>
                <img src="<%= step.video.thumbnail_url %>" alt="Step thumbnail" class="h-16 w-24 object-cover rounded border border-gray-200">
                <% else %>
                  <div class="h-16 w-24 <%= styles[:thumb_bg] %> flex items-center justify-center rounded border <%= styles[:thumb_bg].gsub('50', '200').gsub('bg-', 'border-') %>">
                    <i class="<%= styles[:icon_class] %> text-xl"></i>
                  </div>
                <% end %>
              </td>
              <td class="px-4 py-4">
                <div class="text-sm text-gray-900 max-w-2xl overflow-hidden line-clamp-2">
                  <p class="font-semibold"><%= step.name %></p>
                  <% if step.description.present? %>
                    <p class="text-gray-600"><%= step.description.truncate(100) %></p>
                  <% end %>
                  
                  <% case step.step_type %>
                  <% when 'video' %>
                    <% if step.video.present? %>
                      <div class="text-xs text-gray-500 mt-1">
                        <i class="fa-solid fa-video mr-1"></i><%= step.video.name %>
                      </div>
                    <% end %>
                  <% when 'rich_text' %>
                    <% if step.rich_text_content.present? %>
                      <div class="text-xs text-gray-500 mt-1">
                        <%= strip_tags(step.rich_text_content).truncate(50) %>
                      </div>
                    <% end %>
                  <% when 'quiz' %>
                    <% if step.quiz.present? %>
                      <div class="text-xs text-gray-500 mt-1">
                        <i class="fa-solid fa-question-circle mr-1"></i><%= step.quiz.name %>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              </td>
              <td class="px-4 py-4 text-sm text-gray-900">
                <% if step.estimated_duration_minutes.present? && step.estimated_duration_minutes > 0 %>
                  <%= step.estimated_duration_minutes %> min
                <% else %>
                  <span class="text-gray-400">-</span>
                <% end %>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <%# Edit Step Drawer %>
                  <%= render(DrawerComponent.new(title: "Edit Step", id: "edit-step-#{step.id}-drawer", position: "right")) do |drawer| %>
                    <% drawer.with_trigger do %>
                      <button class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fa-solid fa-pen-to-square mr-1"></i>Edit
                      </button>
                    <% end %>
                    <%# Edit Step Form %>
                    <%= form_with(
                          model: step,
                          url: edit_step_path.call(@flow, step),
                          method: :patch,
                          local: true,
                          scope: :flow_step,
                          id: "edit-step-form-#{step.id}"
                        ) do |f| %>
                      <div class="space-y-4">
                        <div class="field-base">
                          <%= f.label :step_type, class: "block text-sm font-medium text-gray-700" %>
                          <%= f.select :step_type,
                                options_for_select([
                                  ['Video', 'video'],
                                  ['Rich Text', 'rich_text'],
                                  ['Quiz', 'quiz']
                                ], step.step_type),
                                {},
                                { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 step-type-select", required: true }
                              %>
                        </div>
                        
                        <div class="field-base">
                          <%= f.label :name, class: "block text-sm font-medium text-gray-700", required: true %>
                          <%= f.text_field :name, required: true, placeholder: "Enter step name...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
                        </div>
                        
                        <div class="field-base">
                          <%= f.label :description, class: "block text-sm font-medium text-gray-700" %>
                          <%= f.text_area :description, rows: 3, placeholder: "Describe what this step covers...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
                        </div>
                        
                        <%# Rich Text Content Field %>
                        <div class="field-rich_text">
                          <%= f.label :rich_text_content, "Content", class: "block text-sm font-medium text-gray-700" %>
                          <%= f.text_area :rich_text_content, rows: 6, placeholder: "Enter the rich text content for this step...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 ckeditor-custom" %>
                        </div>
                        
                        <%# Video Field %>
                        <div class="field-video">
                          <%= render 'shared/form_fields/video_select_field', 
                              form: f, 
                              allowed_video_types: ['fileboy'],
                              field_name: :video_id,
                              current_video_id: step.video_id,
                              label: "Select Video",
                              uniq_id: "edit_step_#{step.id}_video" %>
                        </div>
                        
                        <%# Quiz Field %>
                        <div class="field-quiz">
                          <%= render partial: 'quiz_field', locals: { form: f, quiz: step.quiz, is_new: false, record_name: @flow.name } %>
                        </div>
                        
                        <div class="field-base">
                          <%= f.label :estimated_duration_minutes, "Duration (minutes)", class: "block text-sm font-medium text-gray-700" %>
                          <%= f.number_field :estimated_duration_minutes, min: 0, placeholder: "e.g. 5", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
                        </div>
                        
                        <%= render "shared/form_fields/image_field",
                            form: f,
                            field_name: :fileboy_image_id,
                            current_image_id: step.fileboy_image_id,
                            label: "Step Thumbnail", 
                            preview_size: "300x_",
                            image_style: "cover",
                            uniq_id: "edit_step_#{step.id}" 
                        %>
                        
                        <div class="flex justify-end space-x-2 pb-16">
                          <button type="button" class="px-4 py-2 border rounded text-gray-700" data-action="drawer#close">
                            Cancel
                          </button>
                          <%= f.submit "Update", class: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" %>
                        </div>
                      </div>
                    <% end %>
                  <% end %>
                  <%# Delete Button %>
                  <%= button_to delete_step_path.call(@flow, step),
                        method: :delete,
                        data: { confirm: "Are you sure you want to delete this step?" },
                        class: "inline-flex items-center px-2.5 py-1.5 border border-red-300 text-sm font-medium rounded text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" do %>
                    <i class="fa-solid fa-trash-alt mr-1"></i>Delete
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        <% end %>
      </tbody>
    </table>
  </div>
</div>

<style>
  /* Styles for the dragging state */
  tr.dragging {
    background-color: #edf5ff !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    opacity: 0.9;
  }

  /* Hide browser's default drag ghost image */
  [data-sortable-ghost] {
    opacity: 0;
  }

  /* Highlight the row when sorting */
  tr.sortable-chosen {
    background-color: #f0f9ff !important;
  }

  /* Make the handle more visually clickable */
  .handle {
    cursor: grab;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .handle:hover {
    background-color: #e5e7eb;
  }

  .handle:active {
    cursor: grabbing;
  }

  #steps-table {
    overflow-x: auto;
  }

  /* Prevent default browser drag behavior on table rows */
  #steps-table-body tr {
    -webkit-user-drag: none;
    user-drag: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Additional fixes for Chrome's globe icon */
  .sortable-drag {
    opacity: 0 !important;
  }

  /* Line clamp for truncating body text */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>

<script>
  // Initialize Sortable when the DOM is fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    initSortable();
    preventDefaultDrag();
    initStepTypeForms();
    loadCkeEditors();

    const urlParams = new URLSearchParams(window.location.search);
    const stepId = urlParams.get('flow_step_id');
    
    if (stepId) {
      const row = document.querySelector(`tr[data-step-id="${stepId}"]`);
      
      if (row) {
        const editTrigger = row.querySelector('button[class*="border-blue-300"]');
        
        if (editTrigger) {
          setTimeout(() => {
            editTrigger.click();
            
            // Remove the flow_step_id parameter from URL after opening
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('flow_step_id');
            window.history.replaceState({}, '', newUrl);
          }, 100);
        } else {
          console.warn(`[autodrawopen] Edit button for step ${slideId} not found`);
        }
      } else {
        console.warn(`[autodrawopen] Step with ID ${slideId} not found`);
      }
    }
  });

  // Prevent default drag behavior for table rows
  function preventDefaultDrag() {
    const rows = document.querySelectorAll('#steps-table-body tr');
    rows.forEach(row => {
      row.addEventListener('dragstart', function(e) {
        if (!e.target.closest('.handle')) {
          e.preventDefault();
        }
      });
      row.setAttribute('draggable', 'false');
    });
  }

  function initSortable() {
    const stepsTableBody = document.getElementById('steps-table-body');
    const stepsContainer = document.getElementById('steps-container');

    if (!stepsTableBody || !stepsContainer) {
      return;
    }

    if (typeof Sortable === 'undefined') {
      console.error('Sortable.js is not loaded');
      return;
    }

    // Remove any existing sortable instance to avoid duplicates
    if (stepsTableBody.sortableInstance) {
      stepsTableBody.sortableInstance.destroy();
    }

    // Initialize Sortable
    stepsTableBody.sortableInstance = Sortable.create(stepsTableBody, {
      animation: 150,
      handle: '.handle',
      ghostClass: 'bg-blue-100',
      dragClass: 'sortable-drag',
      chosenClass: 'sortable-chosen',
      forceFallback: true,
      fallbackClass: 'sortable-fallback',

      onChoose: function(evt) {
        evt.item.classList.add('dragging');
      },

      onUnchoose: function(evt) {
        evt.item.classList.remove('dragging');
      },

      onEnd: function(evt) {
        // Get all step IDs in the new order
        const stepIds = Array.from(stepsTableBody.querySelectorAll('tr[data-step-id]'))
          .map(el => el.getAttribute('data-step-id'));

        // Update the visual order numbers
        Array.from(stepsTableBody.querySelectorAll('tr .step-order')).forEach((el, index) => {
          el.textContent = index + 1;
        });

        // Get the flow ID
        const flowId = stepsContainer.getAttribute('data-flow-id');

        // Send the new order to the server
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        if (!csrfToken) {
          console.error('CSRF token not found');
          return;
        }

        fetch(`/admin/flows/${flowId}/steps/reorder`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken,
            'Accept': 'application/json'
          },
          body: JSON.stringify({ step_ids: stepIds })
        })
        .then(response => {
          if (!response.ok) {
            response.json().then(data => console.error('Failed to update step order:', data.error || 'Unknown error'));
          }
        })
        .catch(error => {
          console.error('Error updating step order:', error);
        });
      }
    });
  }

  /**
   * Initializes step type form handlers
   */
  function initStepTypeForms() {
    const stepTypeSelects = document.querySelectorAll('.step-type-select');

    stepTypeSelects.forEach(select => {
      handleStepTypeChange(select);
      select.addEventListener('change', function() {
        handleStepTypeChange(this);
      });
    });
  }

  /**
   * Handles step type changes and shows/hides appropriate form fields
   */
  function handleStepTypeChange(selectElement) {
    const stepType = selectElement.value;
    const formContainer = selectElement.closest('form');

    if (!formContainer) return;

    // Hide all type-specific fields initially
    const allTypeFields = formContainer.querySelectorAll('.field-video, .field-rich_text, .field-quiz');
    allTypeFields.forEach(field => {
      field.style.display = 'none';
    });

    // Show fields for the selected step type
    if (stepType) {
      const fieldsToShow = formContainer.querySelectorAll(`.field-${stepType}`);
      fieldsToShow.forEach(field => field.style.display = 'block');
    }
  }

  document.addEventListener('drawer:opened', (event) => {
    const node = event.detail.target;
    
    // Trigger quiz link loading
    const questionIdField = node.querySelector('[name="flow_step[quiz_id]"]');
    handleLoadQuipQuizLink(questionIdField);
  })
  
  function loadCkeEditors() {
    document.querySelectorAll('.ckeditor-custom').forEach(function (element) {
      window.editorInstances = window.editorInstances || {};
      if(window.editorInstances[element.id]) {
        return; // Skip if editor already initialized
      }
      ClassicEditor
        .create(element, {
          math: {
            engine: 'mathjax',
            outputType: 'script',
            forceOutputType: false,
            enablePreview: true
          },
          extraPlugins: [FileboyUploadAdapterPlugin],
        })
        
        .then(editor => {
          window.editorInstances[element.id] = editor;
        })
        .catch(error => {
          console.error(error);
        });
    });
  }
</script>
