<% content_for :title, "Questions | #{@questionnaire.name} | Questionnaire" %>
<%= render AdminPageBannerComponent.new(
  title: "Edit #{@questionnaire.name}",
  back_to: { path: admin_questionnaires_path, text: "All questionnaires" }
) %>
<div class="text-white">
  <%= render "tabs", active_tab: :questions %>
</div>
<%= render "questions_table", 
  questionnaire: @questionnaire,
  edit_question_path: ->(questionnaire, question) { edit_admin_questionnaire_question_path(questionnaire, question) },
  delete_question_path: ->(questionnaire, question) { admin_questionnaire_question_path(questionnaire, question) },
  new_question_path: ->(questionnaire) { new_admin_questionnaire_question_path(questionnaire) },
  reorder_questions_path: ->(questionnaire) { admin_questionnaire_path(questionnaire) }
%>
