<%= form_with model: [:admin, @questionnaire], url: (admin_questionnaires_path unless @questionnaire.id), local: true, multipart: true do |form| %>
  <% if @questionnaire.errors.any? %>
    <div class="form-errors">
      <ul>
        <% @questionnaire.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Two Column Layout -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Left Column - Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Basic Details Card -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Basic Details</h3>
          <p class="mt-1 text-sm text-gray-500">Essential questionnaire information</p>
        </div>
        <div class="px-4 py-5 sm:p-6 space-y-6">
          <div>
            <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :name, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <!-- Library Association Card -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Library Association</h3>
          <p class="mt-1 text-sm text-gray-500">Link this questionnaire to curriculum content</p>
        </div>
        <div class="px-4 py-5 sm:p-6 space-y-6">
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <%= label_tag "curriculum", "Curriculum", class: "block text-sm font-medium text-gray-700" %>
              <%= select_tag "curriculum", options_from_collection_for_select(NewLibrary::Curriculum.joins(:years).order('name asc').distinct, 'id', 'name', []), { prompt: 'Select a curriculum...', multiple: false, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", id: "curriculum-select" } %>
            </div>
            <div>
              <%= label_tag "year", "Year", class: "block text-sm font-medium text-gray-700" %>
              <%= select_tag "year", options_from_collection_for_select(NewLibrary::Year.order('name asc'), 'id', 'name', []), { prompt: 'Select a year...', multiple: false, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", id: "year-select" } %>
            </div>
          </div>
          <div>
            <%= form.label :new_library_unit_id, "Unit", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :new_library_unit_id, options_from_collection_for_select(NewLibrary::Unit.order('name asc'), 'id', 'name', @questionnaire.new_library_unit_id), {}, { prompt: 'Select a unit...', multiple: false, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", id: "unit-select" } %>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Settings -->
    <div class="lg:col-span-1 space-y-6">
      <!-- Settings Card -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Settings</h3>
        </div>
        <div class="px-4 py-5 sm:p-6 space-y-6">
          <!-- Demographics Questions -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <%= form.check_box :include_demographics_questions, class: "focus:ring-de-brand h-4 w-4 text-de-brand border-gray-300 rounded" %>
            </div>
            <div class="ml-3 text-sm">
              <%= form.label :include_demographics_questions, "Include Demographics Questions", class: "font-medium text-gray-700" %>
              <p class="text-gray-500">Include questions like date of birth, career interests, etc. This will not display for anonymous users.</p>
            </div>
          </div>

          <!-- Onboarding Questionnaire -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <%= form.check_box :is_onboarding_questionnaire, class: "focus:ring-de-brand h-4 w-4 text-de-brand border-gray-300 rounded" %>
            </div>
            <div class="ml-3 text-sm">
              <%= form.label :is_onboarding_questionnaire, "Is Onboarding Questionnaire", class: "font-medium text-gray-700" %>
              <p class="text-gray-500">The default questionnaire for new users. Changing this will reset the taken state for all users.</p>
            </div>
          </div>

          <!-- Published -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <%= form.check_box :published, class: "focus:ring-de-brand h-4 w-4 text-de-brand border-gray-300 rounded" %>
            </div>
            <div class="ml-3 text-sm">
              <%= form.label :published, "Published", class: "font-medium text-gray-700" %>
              <p class="text-gray-500">Unpublished questionnaires will not be available to users.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Card -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="space-y-3">
            <%= form.submit "Save Changes", class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-de-brand hover:bg-de-brand-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" %>
            
            <%= link_to statistics_admin_questionnaire_path(@questionnaire), class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
              <i class="fa-duotone fa-chart-bar mr-2"></i>
              View Statistics
            <% end %>

            <%= link_to admin_questionnaire_path(@questionnaire), method: :delete, data: { confirm: 'Are you sure you want to delete this questionnaire? This action cannot be undone.' }, class: "w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" do %>
              <i class="fa-duotone fa-trash mr-2"></i>
              Delete Questionnaire
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    const elements = document.querySelectorAll('.field-select');
    const choicesArray = {};
    elements.forEach(element => {
        const choices = new Choices(element, {
            searchEnabled: true,
            searchResultLimit: 5,
            shouldSort: false
        });
        choicesArray[element.id] = choices;
    });

    document.getElementById("curriculum-select").addEventListener("change", function() {
      const curriculumId = this.value;
      fetchYears(curriculumId);
    });

    document.getElementById("year-select").addEventListener("change", function() {
      const curriculumId = document.getElementById("curriculum-select").value;
      const yearId = this.value;
      fetchUnits(curriculumId, yearId);
    });

    function fetchYears(curriculumId) {
      if (!curriculumId) return;

      fetch(`/admin/library-years/filter?curriculum_id=${curriculumId}`)
        .then(response => response.json())
        .then(data => {
          updateSelect("year-select", data);
          fetchUnits(curriculumId, null);
        });
    }

    function fetchUnits(curriculumId, yearId) {
      const url = new URL("/admin/library-units/filter", window.location.origin);
      if (curriculumId) url.searchParams.append("curriculum_id", curriculumId);
      if (yearId) url.searchParams.append("year_id", yearId);

      fetch(url)
        .then(response => response.json())
        .then(data => {
          updateSelect("unit-select", data);
        });
    }

    function updateSelect(selectId, data) {
      const select = document.getElementById(selectId);

      var options = [];
      data.forEach(item => {
        options.push(new Option(item.name, item.id));
      });

      choicesArray[selectId].clearChoices();
      choicesArray[selectId].setChoices(options);
    }
  });
</script>
