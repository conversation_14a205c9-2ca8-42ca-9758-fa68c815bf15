<%= javascript_include_tag 'scripts/password-guidance' %>
<div class="flex-1 min-w-[300px]">
  <%= render "shared/form_errors", model: @school %>
  <%= form_with model: [:admin, @school], method: @school.id ? :put : :post, url: @school.id ? admin_static_school_path(@school.id) : admin_static_schools_path, multipart: true, local: true, id: 'school-form' do |form| %>
    <div class="flex-1 min-w-[300px]">
      <%= render AdminFormSectionComponent.new do %>
        <div>
          <%= form.label :uk_school_id, "UK School" %>
          <%= form.select :uk_school_id,
            options_for_select(UkSchool.where(id: @school.uk_school_id).pluck(:id, :name, :postcode).map { |id, name, postcode| [
                postcode.present? ? "#{name} (#{postcode})" : name,
                id,
                { data: { main: name, sub: postcode } }
              ] }, @school.uk_school_id),
            { include_blank: "Select UK school" },
            { class: "form-control", data: { 
                nice_select: true,
                ajax_url: "/uk_schools/search",
                min_search_length: 2
              }   }
          %>
        </div>
        <div class="field field-image sm:col-span-2">
          <div>
            <%= form.label :fileboy_image, "Logo" %>
            <%= file_field_tag :fileboy_image %>
          </div>
          <%- if @school.fileboy_image_id %>
            <img src="https://www.developingexperts.com/file-cdn/images/get/<%= @school.fileboy_image_id %>?transform=resize:200x200"/>
          <% end %>
        </div>
        <div class="sm:col-span-2 uk_school_toggle">
          <%= form.label :name %>
          <%= form.text_field :name %>
        </div>

        <div class="sm:col-span-2 uk_school_toggle">
          <%= form.label :postcode %>
          <%= form.text_field :postcode %>
        </div>

        <div class="sm:col-span-2 uk_school_toggle">
          <%= form.label :telephone %>
          <%= form.text_field :telephone %>
        </div>

        <div class="sm:col-span-2 uk_school_toggle">
          <%= form.label 'School Type' %>
          <%= form.select :category, options_for_select(School.categories_for_select, selected: @school.category) %>
        </div>

        <div>
          <%= form.label :country_id %>
          <%= form.select :country_id, options_for_select([['Select a country', nil]] + @countries, @school.country_id), class: "field" %>
        </div>

        <div>
          <%= form.label :actual_country_id %>
          <%= form.select :actual_country_id, options_for_select([['Select a country', nil]] + @all_countries, @school.actual_country_id), class: "field" %>
        </div>

        <div>
          <%= form.label :uk_region, 'UK Region' %>
          <%= form.select :uk_region, options_for_select([['Select a region', nil]] + School::UK_REGION_OPTIONS, @school.uk_region), class: "field" %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label :sponsor_ids, 'Sponsors' %>
          <%= form.select :sponsor_ids, options_from_collection_for_select(Sponsor.all, 'id', 'name', @school.sponsors.map(&:id)), {}, { multiple: true, class: "field field-select", id: "sponsor-select" } %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label 'Where did you hear about us?' %>
          <%= form.select :lead_source, options_for_select([['Event or Exhibition', 'tradeshow'], ['Search Engine', 'search_engine'], ['Social Media', 'social_media'], ['Referral or Recommendation', 'referral'], ['Other', 'other']], selected: @school.lead_source), {}, { id: 'lead_source' } %>
        </div>

        <div class="sm:col-span-2" id="sign_up_event_container" style="display: none;">
          <%= form.label 'Select an event' %>
          <%= form.select :sign_up_event_id, options_for_select([['Select an event', nil]] + @sign_up_events, @school.sign_up_event_id), class: "field" %>
        </div>

        <div class="sm:col-span-2" id="lead_source_other_container" style="display: none;">
          <%= form.label 'Where did you hear about us?' %>
          <%= form.text_field :lead_source_other, id: 'lead_source_other' %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label :term_length %>
          <%= form.number_field :term_length %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label 'White Label Organisation' %>
          <%= form.select :organisation_id, options_for_select([['Select an organisation', nil]] + @white_label_orgs, @school.organisation_id), class: "field" %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label :science_leader_ids, 'Science Leaders' %>
          <%= form.select :science_leader_ids, options_from_collection_for_select(@school.teachers, 'id', 'email', @school.science_leaders.map(&:id)), {}, { multiple: true, class: "field field-select", id: "science-leader-select" } %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label :new_library_curriculum_id, 'School Curriculum', required: true %>
          <%= form.select :new_library_curriculum_id, options_for_select(@curricula.map { |c| [c.name, c.id] }, @school.new_library_curriculum_id), {}, class: "field", required: true %>
        </div>

        <div class="sm:col-span-2">
          <%= form.label :show_lesson_plan_pupil_area do %>
            <%= form.check_box :show_lesson_plan_pupil_area %>
            <span class="label">Show Lesson Plan Pupil Area</span>
          <% end %>
        </div>
        <% if @school.id %>
          <div>
            <%= render ButtonComponent::Submit.new(text: "Save Changes") %>
          </div>
        <% end %>
      <% end %>

      <% if !@school.id %>
        <%= render "shared/form_errors", model: @teacher %>
        <%= render AdminFormSectionComponent.new(title: "Science Leader") do %>
          <div class="sm:col-span-2">
            <%= form.label :teacher_name, "Name", required: true %>
            <%= form.text_field :teacher_name, required: true %>
          </div>
          <div class="sm:col-span-2">
            <%= form.label :teacher_email, "Email", required: true %>
            <%= form.text_field :teacher_email, required: true %>
          </div>
          <div class="flex flex-col sm:col-span-2">
            <%= form.label :teacher_password, "Password", required: true %>
            <div class="relative">
              <%= form.password_field :teacher_password, id: 'password', required: true %>
              <span data-password-id="password" class="absolute text-gray-600 inset-y-0 right-0 flex items-center mr-1 px-2 cursor-pointer" onclick="togglePasswordVisibility(this)">
                <i class="fa-solid fa-eye"></i>
              </span>
            </div>
            <div id="pwdReqs" class="bg-white p-4 text-gray-500 rounded-xl" style="display: none;">
                <p><i id="pwdLen" class="mr-1 fa fa-xmark text-red-500"></i>Contains at least eight characters</p>
                <p><i id="pwdNum" class="mr-1 fa fa-xmark text-red-500"></i>Contains a number</p>
                <p><i id="pwdLetter" class="mr-1 fa fa-xmark text-red-500"></i>Contains a letter</p>
                <p><i id="pwdSpecial" class="mr-1 fa fa-xmark text-red-500"></i>Contains a special character</p>
            </div>
          </div>
          <div>
            <%= render ButtonComponent::Submit.new(text: "Save Changes") %>
          </div>
        <% end %>
      <% end %>

      <% if @school.id %>
        <%= render AdminFormSectionComponent.new(title: "Archive") do %>
          <div class="col-span-2">
            <div class="flex gap-2 items-center [&>label]:m-0">
              <%= form.check_box :annually_archive %>
              <%= form.label :year_start, "Annually Archive Data" %>
            </div>
            <p class="m-0 text-sm font-light">You can restore all archived data by un-checking this field at any
              time.</p>
          </div>
          <div id="year_start_wrapper" class="sm:col-span-2">
            <div>
              <%= form.label :year_start, "Archive Data" %>
              <p class="text-gray-400 m-0 -mt-2 text-sm font-light">
                <i>Archive all data before this date, and then every year after this date.</i></p>
              <%= form.date_field :year_start %>
              <p class="text-gray-400 m-0 text-sm font-light">e.g a date set to 01/01/2012 means that all data before
                01/01/2024 will be archived.</p>
            </div>
            <p class="m-0 text-sm font-light col-span-2">Leave this field blank to disable auto archiving of data</p>
          </div>
          <div class="col-span-2">
            <%= render ButtonComponent::Submit.new(text: "Save Changes") %>
          </div>
        <% end %>

        <%= render AdminFormSectionComponent.new(title: "Settings") do %>
          <div class="sm:col-span-2">
            <%= form.label :approved_domains %>
            <%= form.text_field :approved_domains %>
            <p class="text-sm">Enter a comma separated list of domains (e.g gmail.com)</p>
          </div>

          <div class="sm:col-span-2">
            <%= form.label 'Enter a memo that will appear to school teachers when they login.' %>
            <%= form.text_area :memo %>
            <p class="text-sm">Leave this field blank to stop displaying a message</p>
          </div>

          <div>
            <%= render ButtonComponent::Submit.new(text: "Save Changes") %>
          </div>
        <% end %>

        <%= render AdminFormSectionComponent.new(title: "Billing Address") do %>
            <div class="sm:col-span-2">
            <%= form.label :default_billing_address_line_1, "Address Line 1" %>
            <%= form.text_field :default_billing_address_line_1 %>
            </div>

            <div class="sm:col-span-2">
            <%= form.label :default_billing_address_line_2, "Address Line 2" %>
            <%= form.text_field :default_billing_address_line_2 %>
            </div>

            <div class="sm:col-span-2">
            <%= form.label :default_billing_city, "City" %>
            <%= form.text_field :default_billing_city %>
            </div>

            <div class="sm:col-span-2">
            <%= form.label :default_billing_postal_code, "Postal Code" %>
            <%= form.text_field :default_billing_postal_code %>
            </div>

            <div class="sm:col-span-2">
            <%= form.label :default_billing_state, "State/Province" %>
            <%= form.text_field :default_billing_state %>
            </div>

            <div class="sm:col-span-2">
            <%= form.label :default_billing_country_id, "Country" %>
            <%= form.select :default_billing_country_id, options_for_select([['Select a country', nil]] + @all_countries, @school.default_billing_country_id), class: "field" %>
            </div>

          <div>
            <%= render ButtonComponent::Submit.new(text: "Save Changes") %>
          </div>
        <% end %>
      <% end %>
    </div>
  <% end %>
</div>

<script>
  function setField(field, newValue) {
    const input = document.querySelector(`[name='school[${field}]']`)
    if(newValue) {
      if(input) {
        input.value = newValue
        input.disabled = true
        if(input.tagName == 'SELECT') {
          const existingInput = document.querySelector(`input[type='hidden'][name='${input.name}']`)
          const hiddenInput = existingInput || document.createElement('input');
          hiddenInput.type = 'hidden';
          hiddenInput.name = input.name;
          hiddenInput.value = newValue;
          if(!existingInput) {
            input.parentNode.appendChild(hiddenInput);
          }
        }
      }
      return input
    } else {
      if (input.tagName == 'SELECT') {
        const hiddenInputs = document.querySelectorAll(`input[type='hidden'][name='${input.name}']`);
        hiddenInputs.forEach(hiddenInput => hiddenInput.remove());
      }
      input.removeAttribute("disabled")
    }
  }
  function lockUkFields() {
    const name = "<%= @school.uk_school&.name %>"
    const postcode = "<%= @school.uk_school&.postcode %>"
    const telephone = "<%= @school.uk_school&.phone %>"
    const category = "<%= @school.uk_school&.category %>"
    const ukRegion = "<%= @school.uk_school&.region %>"
    setField("name", name)
    setField("postcode", postcode)
    setField("telephone", telephone)
    setField("category", category)
    setField("uk_region", ukRegion)
    setField("country_id", name ? "1" : "")
    setField("actual_country_id", name ? "1" : "")
  }
  lockUkFields()
  async function loadUkSchoolData(event) {
    const selected = event.target.value
    if(selected) {
      const response = await fetch(
        `/admin/uk_schools/${selected}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        }
      )
      if(response.status !== 200) {
        console.error(response)
        alert("Error finding uk school")
        return
      }
      const data = await response.json()
      console.log(data)
      if(confirm("Merge UK school data onto this school? This will overwrite name, postcode, telephone, category, and country on the school.")) {
        setField("name", data.name)
        setField("postcode", data.postcode)
        setField("telephone", data.phone)
        setField("category", data.category)
        setField("uk_region", data.region)
        setField("country_id", 1)
        setField("actual_country_id", 1)
      }
    }
  }
  document.addEventListener("DOMContentLoaded", () => {
    const input = document.querySelector("[name='school[uk_school_id]']")
    if(!input) {
      return
    }
    input.addEventListener('change', loadUkSchoolData)
  })
</script>

<script>
    const elements = document.querySelectorAll('.field-select');
    elements.forEach(element => {
        const choices = new Choices(element, {
            removeItemButton: true, // enables the removal of selected items
            searchEnabled: true, // enables the search feature
            searchResultLimit: 5, // limits the number of search results displayed
        });
    })

    function initArchivedState() {
        //Toggle year start visibility based on if enabled
        const yearStart = document.querySelector('[name="school[year_start]"]')
        const yearStartWrapper = document.getElementById('year_start_wrapper')
        const annuallyArchive = document.querySelector('[type="checkbox"][name="school[annually_archive]"]')
        const state = <%= @school.year_start.present? %>
            console.log("ONLOADED", state)
        if (state) {
            annuallyArchive.checked = true
            yearStartWrapper.style.display = 'block'
        } else {
            annuallyArchive.checked = false
            yearStart.value = ""
            yearStartWrapper.style.display = 'none'
        }
        annuallyArchive.addEventListener('change', (e) => {
            if (e.target.checked) {
                yearStartWrapper.style.display = 'block'
            } else {
                yearStart.value = ""
                yearStartWrapper.style.display = 'none'
            }
        })
    }

    document.addEventListener("DOMContentLoaded", () => {
      initArchivedState()

      const schoolForm = document.getElementById('school-form');
      if (schoolForm) {
        schoolForm.addEventListener('keydown', function (e) {
          if (e.key === 'Enter') {
            e.preventDefault();
          }
        });
      }
    });
</script>
