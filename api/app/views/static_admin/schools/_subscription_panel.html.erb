<%= render AdminFormSectionComponent.new do %>
  <% if @subscription.present? %>
    <% status_class = case @subscribable.subscriber.subscription_status
      when 'active' then 'bg-green-50 text-green-800 border-green-300'
      when 'past_due' then 'bg-yellow-50 text-yellow-800 border-yellow-300'
      when 'unpaid' then 'bg-orange-50 text-orange-800 border-orange-300'
      when 'canceled' then 'bg-gray-50 text-gray-800 border-gray-300'
      else 'bg-gray-50 text-gray-800 border-gray-300'
    end
    %>
    <div class="rounded-md border <%= status_class %> px-4 py-3 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <% case @subscribable.subscriber.subscription_status %>
          <% when 'active' %>
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          <% when 'past_due' %>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          <% when 'unpaid', 'canceled' %>
            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          <% else %>
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
          <% end %>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium">
            Subscription Status: <%= @subscribable.subscriber.subscription_status&.humanize || 'Unknown' %>
          </h3>
          <div class="mt-2 text-sm">
            <p>
              <% case @subscribable.subscriber.subscription_status %>
              <% when 'active' %>
                This subscription is active and in good standing.
              <% when 'past_due' %>
                This subscription has overdue payments.
              <% when 'unpaid' %>
                This subscription has unpaid invoices and is at risk of cancellation.
              <% when 'canceled' %>
                This subscription has been cancelled.
                <% if @subscribable.subscriber.cancellation_date.present? %>
                  <span class="font-semibold">Cancelled on: <%= @subscribable.subscriber.cancellation_date.strftime("%B %d, %Y") %></span>
                <% end %>
              <% else %>
                The status of this subscription is unknown.
              <% end %>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="grid gap-y-2 gap-x-8 mb-4" style="grid-template-columns: max-content 1fr">
      <% [
          { label: "Subscriber Type", value: @subscribable.class.name },
          { label: "Created", value: Time.at(@subscription.created).strftime("%B %d, %Y") },
          { label: "Current Period Ends", value: Time.at(@subscription.current_period_end).strftime("%B %d, %Y") },
        ].each do |item| %>
          <div class="font-bold min-w-16">
            <%= item[:label] %>
          </div>
          <div>
            <%= item[:value] || "-" %>
          </div>
      <% end %>
    </div>
    
    <% paid_products = @subscription.items.data.map { |item| item.price.product.name } %>
    <% free_products = @subscribable.subscriber.free_subscription_options.select(&:enabled) %>
    
    <% if paid_products.any? %>
      <h5>Paid Subscription Items</h5>
      <ul class="space-y-2 mb-4">
        <% @subscription.items.data.each do |item| %>
          <li class="flex justify-between items-center py-2 px-3 bg-green-50 bg-opacity-40 rounded border-l-4 border-green-400">
            <div>
              <p><%= item.price.product.name %> <span class="text-sm text-green-600 font-medium">(Paid)</span></p>
            </div>
            <span>£<%= sprintf('%.2f', item.price.unit_amount / 100.0) %></span>
          </li>
        <% end %>
      </ul>
    <% end %>
    
    <% if free_products.any? %>
      <h5>Free Subscription Items</h5>
      <ul class="space-y-2 mb-4">
        <% free_products.each do |product| %>
          <li class="flex justify-between items-center py-2 px-3 <%= product.expired? ? 'bg-red-50 bg-opacity-40 border-l-4 border-red-400' : 'bg-blue-50 bg-opacity-40 border-l-4 border-blue-400' %> rounded">
            <div>
              <p>
                <%= product.label %> 
                <span class="text-sm <%= product.expired? ? 'text-red-600' : 'text-blue-600' %> font-medium">
                  (<%= product.expired? ? 'Expired Free' : 'Free' %>)
                </span>
              </p>
              <% if product.body.present? %>
                <p class="text-xs text-gray-600 mt-1">Reason: <%= product.body %></p>
              <% end %>
              <% if product.expires_at.present? %>
                <p class="text-xs <%= product.expired? ? 'text-red-600' : 'text-gray-600' %> mt-1">
                  <%= product.expired? ? 'Expired' : 'Expires' %>: <%= product.expires_at.strftime("%B %d, %Y") %>
                </p>
              <% end %>
            </div>
            <span class="<%= product.expired? ? 'text-red-600' : 'text-blue-600' %> font-medium">
              <%= product.expired? ? 'EXPIRED' : 'FREE' %>
            </span>
          </li>
        <% end %>
      </ul>
    <% end %>
    
    <div class="mb-4">
      <%= link_to 'Manage subscription', admin_subscription_dashboard_path(@subscribable.subscriber), class: "btn btn-base btn-cyan" %>
    </div>
    
    <!-- Always show free subscription management for active subscribers -->
    <%= form_with url: @subscribable.class == School ? manage_subscriber_admin_static_school_path(@subscribable) : manage_subscriber_admin_user_path(@subscribable),
        method: :post,
        local: true do |form|
    %>
      <%= hidden_field_tag :return_page, :stripe_subscription %>
      <%= render "shared/form_errors", model: @subscribable.subscriber %>
      
      <div class="border-t pt-4">
        <h4 class="mb-2">Manage Free Subscriptions</h4>
        <p class="text-sm text-gray-600 mb-4">Free subscriptions can be added alongside paid subscriptions to provide additional access.</p>
        
        <%= form.fields_for :subscriber, @subscribable.subscriber do |subscriber| %>
          <% free_sub_data = @subscribable.subscriber.free_subscription_for_form %>
          <%= subscriber.fields_for :free_subscription, OpenStruct.new(free_sub_data) do |free_sub_fields| %>
            <% 
              # Check which products they have paid subscriptions for
              paid_ai = paid_products.any? { |p| p.downcase.include?('ai') }
              paid_science = paid_products.any? { |p| p.downcase.include?('science') }
              paid_geography = paid_products.any? { |p| p.downcase.include?('geography') }
            %>
            
            <!-- AI Subscription -->
            <% if @current_user&.beta_feature_enabled?(:aug_4) %>
              <div>
                <%= free_sub_fields.label :bypass_ai, class: "flex gap-2 items-center font-medium text-lg" do %>
                  <%= free_sub_fields.check_box :bypass_ai, class: "rounded" %>
                  <span class="text-gray-900">Bypass AI Subscription</span>
                <% end %>
              </div>
            <% end %>
            <div class="border rounded-lg p-4 mb-4 <%= paid_ai ? 'bg-gray-50 border-gray-300' : 'bg-white border-gray-200' %>">
              <div class="flex items-center justify-between mb-3">
                <%= free_sub_fields.label :ai, class: "flex gap-2 items-center font-medium text-lg" do %>
                  <%= free_sub_fields.check_box :ai, disabled: paid_ai, class: "rounded" %>
                  <span class="<%= paid_ai ? 'text-gray-500' : 'text-gray-900' %>">Free AI Subscription</span>
                <% end %>
                <% if paid_ai %>
                  <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">Already has paid AI</span>
                <% end %>
              </div>
              
              <div class="grid md:grid-cols-2 gap-4">
                <div class="field">
                  <%= free_sub_fields.label :ai_body, "Reason for free subscription", class: "block text-sm font-medium text-gray-700 mb-1 #{'required' unless paid_ai}" %>
                  <%= free_sub_fields.text_field :ai_body, 
                      placeholder: "Enter reason for granting free AI access", 
                      required: !paid_ai,
                      disabled: paid_ai,
                      class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_ai ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                </div>
                
                <div>
                  <div class="field mb-2">
                    <%= free_sub_fields.label :ai_trial_start, "Trial Start (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                    <%= free_sub_fields.date_field :ai_trial_start, 
                        disabled: paid_ai,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_ai ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                  </div>
                  <div class="field">
                    <%= free_sub_fields.label :ai_expires_at, "Expiry date (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                    <%= free_sub_fields.date_field :ai_expires_at, 
                        disabled: paid_ai,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_ai ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                    <small class="text-gray-500 text-xs">Leave blank for no expiry</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Science Subscription -->
            <% if @current_user&.beta_feature_enabled?(:aug_4) %>
              <div>
                <%= free_sub_fields.label :bypass_science, class: "flex gap-2 items-center font-medium text-lg" do %>
                  <%= free_sub_fields.check_box :bypass_science, class: "rounded" %>
                  <span class="text-gray-900">Bypass Science Subscription</span>
                <% end %>
              </div>
            <% end %>
            <div class="border rounded-lg p-4 mb-4 <%= paid_science ? 'bg-gray-50 border-gray-300' : 'bg-white border-gray-200' %>">
              <div class="flex items-center justify-between mb-3">
                <%= free_sub_fields.label :science, class: "flex gap-2 items-center font-medium text-lg" do %>
                  <%= free_sub_fields.check_box :science, disabled: paid_science, class: "rounded" %>
                  <span class="<%= paid_science ? 'text-gray-500' : 'text-gray-900' %>">Free Science Subscription</span>
                <% end %>
                <% if paid_science %>
                  <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">Already has paid Science</span>
                <% end %>
              </div>
              
              <div class="grid md:grid-cols-2 gap-4">
                <div class="field">
                  <%= free_sub_fields.label :science_body, "Reason for free subscription", class: "block text-sm font-medium text-gray-700 mb-1 #{'required' unless paid_science}" %>
                  <%= free_sub_fields.text_field :science_body, 
                      placeholder: "Enter reason for granting free Science access", 
                      required: !paid_science,
                      disabled: paid_science,
                      class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_science ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                </div>
                
                <div>
                  <div class="field mb-2">
                    <%= free_sub_fields.label :science_trial_start, "Trial Start (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                    <%= free_sub_fields.date_field :science_trial_start, 
                        disabled: paid_science,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_science ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                  </div>
                  <div class="field">
                    <%= free_sub_fields.label :science_expires_at, "Expiry date (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                    <%= free_sub_fields.date_field :science_expires_at, 
                        disabled: paid_science,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_science ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                    <small class="text-gray-500 text-xs">Leave blank for no expiry</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Geography Subscription -->
            <% if @current_user&.beta_feature_enabled?(:aug_4) %>
              <div>
                <%= free_sub_fields.label :bypass_geography, class: "flex gap-2 items-center font-medium text-lg" do %>
                  <%= free_sub_fields.check_box :bypass_geography, class: "rounded" %>
                  <span class="text-gray-900">Bypass Geography Subscription</span>
                <% end %>
              </div>
            <% end %>
            <div class="border rounded-lg p-4 mb-4 <%= paid_geography ? 'bg-gray-50 border-gray-300' : 'bg-white border-gray-200' %>">
              <div class="flex items-center justify-between mb-3">
                <%= free_sub_fields.label :geography, class: "flex gap-2 items-center font-medium text-lg" do %>
                  <%= free_sub_fields.check_box :geography, disabled: paid_geography, class: "rounded" %>
                  <span class="<%= paid_geography ? 'text-gray-500' : 'text-gray-900' %>">Free Geography Subscription</span>
                <% end %>
                <% if paid_geography %>
                  <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">Already has paid Geography</span>
                <% end %>
              </div>
              
              <div class="grid md:grid-cols-2 gap-4">
                <div class="field">
                  <%= free_sub_fields.label :geography_body, "Reason for free subscription", class: "block text-sm font-medium text-gray-700 mb-1 #{'required' unless paid_geography}" %>
                  <%= free_sub_fields.text_field :geography_body, 
                      placeholder: "Enter reason for granting free Geography access", 
                      required: !paid_geography,
                      disabled: paid_geography,
                      class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_geography ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                </div>
                <div>
                  <div class="field mb-2">
                    <%= free_sub_fields.label :geography_trial_start, "Trial Start (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                    <%= free_sub_fields.date_field :geography_trial_start, 
                        disabled: paid_geography,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_geography ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                  </div>
                  <div class="field">
                    <%= free_sub_fields.label :geography_expires_at, "Expiry date (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                    <%= free_sub_fields.date_field :geography_expires_at, 
                        disabled: paid_geography,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 #{paid_geography ? 'bg-gray-100 text-gray-500' : 'bg-white'}" %>
                    <small class="text-gray-500 text-xs">Leave blank for no expiry</small>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>
        <div class="mt-4">
          <%= form.submit 'Save changes', data: { disable_with: 'Processing...' }, class: "admin-btn" %>
        </div>
      </div>
    <% end %>
    
  <% else %>
    <%= form_with url: @subscribable.class == School ? manage_subscriber_admin_static_school_path(@subscribable) : manage_subscriber_admin_user_path(@subscribable),
        method: :post,
        local: true do |form|
    %>
      <%= hidden_field_tag :return_page, :stripe_subscription %>
      <%= render "shared/form_errors", model: @subscribable.subscriber %>
      <% if @has_school_subscription %>
        <p>This user has a subscription via their <a class="text-de-brand underline" href="<%= edit_admin_static_school_path(@user.school) %>">school</a></p>
      <% else %>
        <div>
          <p class="mb-4">This <%= @subscribable.class.name %> does not have an active subscription.</p>
          <% if !@subscribable&.subscriber %>

            <!-- Create Subscriber with Stripe Details Form -->
            <div class="border rounded-lg p-4 mb-4 bg-blue-50 border-blue-200">
              <h4 class="text-lg font-medium text-blue-900 mb-3">Create Subscriber with Stripe Details</h4>
              <p class="text-sm text-blue-700 mb-4">Create a subscriber record with Stripe customer and subscription IDs to set up a paid subscription.</p>

              <%= form_with url: create_stripe_subscriber_admin_static_school_path(@subscribable),
                  method: :post,
                  local: true,
                  class: "space-y-4" do |stripe_form| %>
                <%= hidden_field_tag :return_page, :stripe_subscription %>

                <div class="grid md:grid-cols-2 gap-4">
                  <div class="field">
                    <%= stripe_form.label :stripe_customer_id, "Stripe Customer ID", class: "block text-sm font-medium text-gray-700 mb-1 required" %>
                    <%= stripe_form.text_field :stripe_customer_id,
                        placeholder: "cus_...",
                        required: true,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                    <small class="text-gray-500 text-xs">The Stripe customer ID (starts with 'cus_')</small>
                  </div>

                  <div class="field">
                    <%= stripe_form.label :stripe_subscription_id, "Stripe Subscription ID", class: "block text-sm font-medium text-gray-700 mb-1 required" %>
                    <%= stripe_form.text_field :stripe_subscription_id,
                        placeholder: "sub_...",
                        required: true,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                    <small class="text-gray-500 text-xs">The Stripe subscription ID (starts with 'sub_')</small>
                  </div>
                </div>

                <div class="border-t pt-3">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Billing Address (copied from school)</h5>
                  <div class="text-xs text-gray-600 space-y-1">
                    <p><strong>Address:</strong> <%= @subscribable.default_billing_address_line_1 || 'Not set' %></p>
                    <% if @subscribable.default_billing_address_line_2.present? %>
                      <p><strong>Address Line 2:</strong> <%= @subscribable.default_billing_address_line_2 %></p>
                    <% end %>
                    <p><strong>City:</strong> <%= @subscribable.default_billing_city || 'Not set' %></p>
                    <p><strong>State:</strong> <%= @subscribable.default_billing_state || 'Not set' %></p>
                    <p><strong>Postal Code:</strong> <%= @subscribable.default_billing_postal_code || 'Not set' %></p>
                    <p><strong>Country:</strong> <%= @subscribable.default_billing_country&.name || 'Not set' %></p>
                  </div>
                </div>

                <div class="flex gap-2">
                  <%= stripe_form.submit 'Create Subscriber with Stripe Details',
                      data: { disable_with: 'Creating...' },
                      class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
                </div>
              <% end %>
            </div>

            <!-- OR divider -->
            <div class="flex items-center my-4">
              <div class="flex-grow border-t border-gray-300"></div>
              <span class="px-3 text-sm text-gray-500 bg-white">OR</span>
              <div class="flex-grow border-t border-gray-300"></div>
            </div>

            <!-- Original create subscriber button for free subscriptions -->
            <p>To manage free subscription only
              <%= render ButtonComponent::Base.new(
                text: "Create a subscriber.",
                variant: :flat_white,
                classes: "text-de-brand underline",
                onclick: "this.closest('form').submit()"
              ) %>
            </p>
          <% else %>
            <h4 class="mb-2">Manage this <%= @subscribable.class.name %>'s free subscription</h4>
            <% free_sub_data = @subscribable.subscriber.free_subscription_for_form %>
            <%= form.fields_for :subscriber, @subscribable.subscriber do |subscriber| %>
              <%= subscriber.fields_for :free_subscription, OpenStruct.new(free_sub_data) do |free_sub_fields| %>
                <!-- AI Subscription -->
                <div class="border rounded-lg p-4 mb-4 bg-white border-gray-200">
                  <div class="flex items-center gap-2 mb-3">
                    <%= free_sub_fields.label :ai, class: "flex gap-2 items-center font-medium text-lg" do %>
                      <%= free_sub_fields.check_box :ai, class: "rounded" %>
                      <span class="text-gray-900">Free AI Subscription</span>
                    <% end %>
                  </div>
                  
                  <div class="grid md:grid-cols-2 gap-4">
                    <div class="field">
                      <%= free_sub_fields.label :ai_body, "Reason for free subscription", class: "block text-sm font-medium text-gray-700 mb-1 required" %>
                      <%= free_sub_fields.text_field :ai_body, 
                          placeholder: "Enter reason for granting free AI access", 
                          required: true,
                          class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                    </div>
                    
                    <div>
                      <div class="field mb-2">
                        <%= free_sub_fields.label :ai_trial_start, "Trial Start (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                        <%= free_sub_fields.date_field :ai_trial_start, 
                            class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                      </div>
                      <div class="field">
                        <%= free_sub_fields.label :ai_expires_at, "Expiry date (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                        <%= free_sub_fields.date_field :ai_expires_at, 
                            class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                        <small class="text-gray-500 text-xs">Leave blank for no expiry</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Science Subscription -->
                <div class="border rounded-lg p-4 mb-4 bg-white border-gray-200">
                  <div class="flex items-center gap-2 mb-3">
                    <%= free_sub_fields.label :science, class: "flex gap-2 items-center font-medium text-lg" do %>
                      <%= free_sub_fields.check_box :science, class: "rounded" %>
                      <span class="text-gray-900">Free Science Subscription</span>
                    <% end %>
                  </div>
                  
                  <div class="grid md:grid-cols-2 gap-4">
                    <div class="field">
                      <%= free_sub_fields.label :science_body, "Reason for free subscription", class: "block text-sm font-medium text-gray-700 mb-1 required" %>
                      <%= free_sub_fields.text_field :science_body, 
                          placeholder: "Enter reason for granting free Science access", 
                          required: true,
                          class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                    </div>
                    
                    <div>
                      <div class="field mb-2">
                        <%= free_sub_fields.label :science_trial_start, "Trial Start (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                        <%= free_sub_fields.date_field :science_trial_start, 
                            class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                      </div>
                      <div class="field">
                        <%= free_sub_fields.label :science_expires_at, "Expiry date (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                        <%= free_sub_fields.date_field :science_expires_at, 
                            class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                        <small class="text-gray-500 text-xs">Leave blank for no expiry</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Geography Subscription -->
                <div class="border rounded-lg p-4 mb-4 bg-white border-gray-200">
                  <div class="flex items-center gap-2 mb-3">
                    <%= free_sub_fields.label :geography, class: "flex gap-2 items-center font-medium text-lg" do %>
                      <%= free_sub_fields.check_box :geography, class: "rounded" %>
                      <span class="text-gray-900">Free Geography Subscription</span>
                    <% end %>
                  </div>
                  
                  <div class="grid md:grid-cols-2 gap-4">
                    <div class="field">
                      <%= free_sub_fields.label :geography_body, "Reason for free subscription", class: "block text-sm font-medium text-gray-700 mb-1 required" %>
                      <%= free_sub_fields.text_field :geography_body, 
                          placeholder: "Enter reason for granting free Geography access", 
                          required: true,
                          class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                    </div>
                    
                    <div>
                      <div class="field mb-2">
                        <%= free_sub_fields.label :geography_trial_start, "Trial Start (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                        <%= free_sub_fields.date_field :geography_trial_start, 
                            class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                      </div>
                      <div class="field">
                        <%= free_sub_fields.label :geography_expires_at, "Expiry date (optional)", class: "block text-sm font-medium text-gray-700 mb-1" %>
                        <%= free_sub_fields.date_field :geography_expires_at, 
                            class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white" %>
                        <small class="text-gray-500 text-xs">Leave blank for no expiry</small>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            <% end %>
            <div class="mt-4">
              <%= form.submit 'Save changes', data: { disable_with: 'Processing...' }, class: "admin-btn" %>
            </div>
          <% end %>
        </div>
      <% end %>
    <% end %>
    <% if @subscribable.subscriber.present? %>
      <div>
        <%= render ButtonComponent::Base.new(
            left_icon: 'fas fa-trash', 
            text: 'Delete Subscriber', 
            method: :delete, 
            url: delete_subscriber_path.call(@subscribable),
            variant: :flat_red,
            data: { confirm: "Are you sure?" },
        ) %>
      </div>
    <% end %>
  <% end %>
<% end %>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const options = <%= Subscriber::FREE_SUBSCRIPTION_OPTIONS.map {|opt| opt[:key] }.to_json.html_safe %>
    for(const field of options) {
      const checkbox = document.querySelector(`input[type='checkbox'][name='[subscriber][free_subscription][${field}]']`)
      const body = document.querySelector(`input[name='[subscriber][free_subscription][${field}_body]']`)
      requireFreeSubscriptionBody(checkbox, body)
      checkbox.addEventListener('change', () => requireFreeSubscriptionBody(checkbox, body))
    }
  })

  function requireFreeSubscriptionBody(checkbox, body) {
    const label = body.parentElement.querySelector("label")
    console.log("CHANGE FREE", checkbox, body, label)
    if(checkbox.checked) {
      body.setAttribute('required', true)
      label.setAttribute('required', true)
      label.classList.add('required')
    } else {
      body.removeAttribute('required')
      label.removeAttribute('required')
      label.classList.remove('required')
    }
  }
</script>
