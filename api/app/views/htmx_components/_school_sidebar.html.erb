<%- if @current_user&.present? %>
  <aside class="w-64 bg-white px-2 py-4 text-black text-sm top-0 h-full space-y-4 flex flex-col" aria-label="Sidebar" data-controller="sidebar">
    <%= link_to root_path do %>
      <% if ai_controllers.include? controller_name %>
        <%= image_tag('deai/logo_ai.svg', alt: "Developing Experts Logo", class: "w-44") %>
      <% else %>
        <%= render partial: 'shared/de_icon_svg_partial' %>
      <% end %>
    <% end %>
    <div class="p-2 border-2 border border-dashed border-slate-300 rounded-md w-fit font-semibold text-[#313131] transition-all duration-300 ease-in-out hover:text-[#aadffb] cursor-pointer">
      <div class="h-full space-y-4" id="referral-trigger">
        <p>Click here to refer a friend & get rewards</p>
      </div>
      <dialog id="referral-dialog" class="bg-dark-blue max-w-xl p-4">
        <div class="text-white space-y-4">
          <h2 class="text-3xl">Refer someone today</h2>
          <p class="font-light">
            Boost Your Learning and Your Wallet: Refer a Friend to Developing Experts Today! Share the gift of knowledge
            with a friend or colleague and you'll both receive a £10 Amazon voucher when they subscribe.
          </p>
          <p class="font-light">
            Click below to copy your code or link and if they use it when they sign up, we'll be in touch with your
            reward.
          </p>
          <p class="bg-cyan-500 text-white py-2 px-6 rounded-full shadow-lg border border-b-2 border-cyan-500 hover:bg-cyan-400 cursor-pointer inline-block duration-300 font-bold mt-4" onclick="copyToClipboard(event, this, '<%= @current_user.referral_code %>')">
            <i class="fa-solid fa-copy"></i>
            <span class="font-sans"><%= @current_user.referral_code %></span>
          </p>
          <p class="bg-cyan-500 text-white py-2 px-6 rounded-full shadow-lg border border-b-2 border-cyan-500 hover:bg-cyan-400 cursor-pointer inline-block duration-300 font-bold" onclick="copyToClipboard(event, this, 'https://www.developingexperts.com/accounts/new?referral=<%= j @current_user.referral_code %>')">
            <i class="fa-solid fa-copy"></i>
            <span class="font-sans">https://www.developingexperts.com/accounts/new?referral=<%= @current_user.referral_code %></span>
          </p>
          <div class="flex gap-x-2 mt-8">
            <button class="bg-gray-100 py-2 px-6 rounded-full shadow-lg border border-b-2 border-gray-100 hover:bg-gray-200 cursor-pointer inline-block duration-300 font-bold text-black" id="close-modal">
              <i class="fa-solid fa-xmark"></i>
              Close
            </button>
          </div>
        </div>
      </dialog>
    </div>

    <div class="sidebar-scroll-section space-y-6 flex-1 overflow-y-auto" data-sidebar-target="section">
      <ul class="m-0 p-0">
        <%= render './htmx_components/sidebar_link', label: 'Dashboard', path: '/s', path_controller_name: 'dashboard' %>
      </ul>

      <div>
        <p class="text-de-brand mb-2 font-semibold px-2 uppercase">Curriculum</p>
        <ul class="m-0 p-0">
          <%- if @current_user.has_school_feature_access? %>
            <%= render './htmx_components/sidebar_link', label: 'Units & Lessons', path: unit_library_path, path_controller_name: 'static_library' %>
            <%= render './htmx_components/sidebar_link', label: 'Curriculum Documents', path: school_curriculum_documents_path, path_controller_name: 'curriculum_documents' %>
            <%- if @current_user&.can_create_lessons? && @current_user.has_school_feature_access? %>
              <% if @current_user.beta_feature_enabled?(:new_lesson_editor) %>
                <%= render './htmx_components/sidebar_link', label: 'Lesson Editing Tools', path: '/school/lesson-editing', path_controller_name: 'lesson_editing' %>
              <% else %>
                <%= render './htmx_components/sidebar_link', label: 'Lesson Editing Tools', path: '/s/lesson-templates', path_controller_name: 'lesson_templates' %>
              <% end %>
            <% end %>
            <%= render './htmx_components/sidebar_link', label: 'School Lesson Library', path: '/s/library/community', path_controller_name: 'library' %>
          <% end %>

          <%= render './htmx_components/sidebar_link', label: 'AI Lesson Builder', path: '/ai', path_controller_name: 'ai' %>
          <%= render './htmx_components/sidebar_link', label: 'AI Career Builder', path: '/career-builder', path_controller_name: 'career_builder' %>
          <%= render './htmx_components/sidebar_link', label: 'Glossary', path: '/glossary', path_controller_name: 'glossary' %>
          <% if @current_user.beta_feature_enabled?(:september_1) %>
            <%= render './htmx_components/sidebar_link', label: 'Careers Zone', path: careers_index_path, path_controller_name: 'careers' %>
          <% end %>
          <% if @current_user.beta_feature_enabled?(:aug_4) %>
            <%= render './htmx_components/sidebar_link', label: 'DE Live', path: '/school/youtube', path_controller_name: 'live_lessons' %>
          <% else %>
            <%= render './htmx_components/sidebar_link', label: 'Live Lesson', path: '/school/youtube', path_controller_name: 'live_lessons' %>
          <% end %>
        </ul>
      </div>

      <%- if @current_user.has_school_feature_access? %>
        <div>
          <p class="text-de-brand mb-2 font-semibold px-2 uppercase">Classes and Assessment</p>
          <ul class="m-0 p-0">
            <% if @current_user&.school&.show_mark_book %>
              <%= render './htmx_components/sidebar_link', label: 'Mark Book', path: school_mark_book_index_path, path_controller_name: 'mark_books' %>
            <% end %>
            <%= render './htmx_components/sidebar_link', label: 'Independent Learning', path: '/s/homework', path_controller_name: 'homework' %>
            <%= render './htmx_components/sidebar_link', label: 'My Classes', path: '/school/classes', path_controller_name: !params[:all] && 'forms' %>
            <%= render './htmx_components/sidebar_link', label: 'My Pupils', path: '/school/pupils', path_controller_name: !params[:all] && 'pupils' %>
            <%= render './htmx_components/sidebar_link', label: 'Exemplar Work', path: school_exemplar_works_path, path_controller_name: 'exemplar_works' %>
          </ul>
        </div>
      <% end %>

      <% if @current_user.is_school_admin %>
        <div>
          <div class="flex items-center mb-2 px-2">
            <p class="text-de-brand font-semibold uppercase">School Account</p>
            <%= link_to school_settings_path, class: "ml-2 text-dark-blue hover:text-de-brand", title: "School Settings" do %>
              <i class="fa-regular fa-cog"></i>
            <% end %>
          </div>
          <ul class="m-0 p-0">
            <%- if @current_user.has_school_feature_access? %>
              <%= render './htmx_components/sidebar_link', label: 'Manage Teachers', path: '/school/teachers', path_controller_name: 'teachers' %>
            <% end %>
            <%= render './htmx_components/sidebar_link', label: 'Manage Subscription', path: lesson_subscription_path, path_controller_name: 'pages' %>
            <%- if @current_user.has_school_feature_access? %>
              <%= render './htmx_components/sidebar_link', label: 'Manage Classes', path: '/school/classes?all=true', path_controller_name: params[:all] && 'forms' %>
              <%= render './htmx_components/sidebar_link', label: 'Manage Pupils', path: '/school/pupils?all=true', path_controller_name: params[:all] && 'pupils' %>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div>
        <p class="text-de-brand mb-2 font-semibold px-2 uppercase">Help and Support</p>
        <ul class="m-0 p-0">
          <%= render './htmx_components/sidebar_link', label: 'Frequently Asked Questions', path: '/knowledge', path_controller_name: 'knowledge' %>
          <%= render './htmx_components/sidebar_link', label: 'Contact us', path: '/enquiry', path_controller_name: 'enquiry' %>
          <%= render './htmx_components/sidebar_link', label: 'Welcome Pack', path: 'https://www.developingexperts.com/file-cdn/files/get/92ff4093-f09c-4ab0-9851-cf76204f019c+welcome_pack?download', path_controller_name: '' %>
        </ul>
      </div>
    </div>

  </aside>
  <dialog id="subscribe-ai-dialog" style="max-width: 64rem; width: 100%; padding: 2rem; background: linear-gradient(to right, #ebf8ff, #ffffff, #ebf8ff);">
    <h2 style="font-size: 1.875rem; font-weight: 700; color: #1a202c; margin-bottom: 1rem;">Unlock the Power of AI in
      Your
      Lessons!</h2>
    <p style="color: #4a5568; margin-bottom: 1rem;">
      Subscribe today and get exclusive access to AI-powered lesson plans, assessments, and feedback tools.
      Tailor your teaching and boost student success effortlessly.
    </p>
    <%- if Rails.env.production? %>
      <p style="margin-bottom: 16px"><b>Coming soon, look out for updates</b></p>
      <button style="background-color: #f7fafc; padding: 0.5rem 1.5rem; border-radius: 0.375rem; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); border: 1px solid #f7fafc; border-bottom-width: 2px; cursor: pointer; display: inline-block; transition: background-color 0.3s; font-weight: bold;" onmouseover="this.style.backgroundColor='#edf2f7'" onmouseout="this.style.backgroundColor='#f7fafc'" onclick="this.parentElement.close()">Close</button>
    <%- else %>
      <div style="display: flex; gap: 0.5rem;">
        <%= link_to 'Unlock AI', subscriptions_user_stripe_subscriptions_path, style: "background-color: #06b6d4; color: white; padding: 0.5rem 1.5rem; border-radius: 0.375rem; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); border: 1px solid #06b6d4; border-bottom-width: 2px; cursor: pointer; display: inline-block; transition: background-color 0.3s;", onmouseover: "this.style.backgroundColor='#0891b2'", onmouseout: "this.style.backgroundColor='#06b6d4'", class: "font-bold" %>
        <button style="background-color: #f7fafc; padding: 0.5rem 1.5rem; border-radius: 0.375rem; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); border: 1px solid #f7fafc; border-bottom-width: 2px; cursor: pointer; display: inline-block; transition: background-color 0.3s; font-weight: bold;" onmouseover="this.style.backgroundColor='#edf2f7'" onmouseout="this.style.backgroundColor='#f7fafc'" onclick="this.parentElement.parentElement.close()">Close</button>
      </div>
    <%- end %>
  </dialog>
  <script>
      function handleOpenAiSubscribeDialog() {
          const dialog = document.getElementById('subscribe-ai-dialog');
          dialog.showModal();
      }

      window.handleOpenAiSubscribeDialog = handleOpenAiSubscribeDialog
  </script>
  <script>
      const referralBox = document.getElementById('referral-trigger');
      const referralDialog = document.getElementById('referral-dialog');

      referralBox.addEventListener('click', function () {
          referralDialog.showModal();
      });

      const closeModalBtn = document.getElementById('close-modal');

      closeModalBtn.addEventListener('click', function () {
          referralDialog.close();
      });

      function copyToClipboard(e, el, name) {
          e.preventDefault();

          navigator.clipboard.writeText(name).then(() => {
              // Find the <i> tag inside the element
              const icon = el.querySelector('i');

              if (icon.classList.contains("fa-copy")) {
                  icon.classList.remove("fa-copy");
                  icon.classList.add("fa-check");

                  // Reset back to the copy icon after 2 seconds
                  setTimeout(() => {
                      icon.classList.remove("fa-check");
                      icon.classList.add("fa-copy");
                  }, 2000); // Adjust the timeout as needed
              }
          }, () => {
              console.error(`Failed to copy ${name}`);
          });
      }
  </script>
<%- else %>
  <!-- Not signed in, redirect to the static logout page -->
  <script>
      window.location.href = '<%= static_logout_path %>';
  </script>
<% end %>
