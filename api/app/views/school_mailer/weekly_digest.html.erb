<!-- Header -->
<div style="text-align: center; margin-bottom: 30px;">
  <h1 style="color: #333333; margin: 0; font-size: 28px; font-weight: bold;">
    📊 Your Weekly Teaching Digest
  </h1>
  <p style="margin: 10px 0 0 0; color: #666666; font-size: 16px;">
    Your teaching activity summary and upcoming lessons
  </p>
</div>

<!-- Stats Cards -->
<table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
  <tr>
    <td width="25%" style="padding: 0 5px;">
      <div style="background-color: #3b82f6; border-radius: 8px; text-align: center; padding: 15px; color: #ffffff;">
        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;"><%= @past_week_stats.taught_lessons.count %></div>
        <div style="font-size: 12px; opacity: 0.9;">Lessons Taught</div>
      </div>
    </td>
    <td width="25%" style="padding: 0 5px;">
      <div style="background-color: #10b981; border-radius: 8px; text-align: center; padding: 15px; color: #ffffff;">
        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;"><%= @past_week_stats.taught_units.count %></div>
        <div style="font-size: 12px; opacity: 0.9;">Units Taught</div>
      </div>
    </td>
    <td width="25%" style="padding: 0 5px;">
      <div style="background-color: #8b5cf6; border-radius: 8px; text-align: center; padding: 15px; color: #ffffff;">
        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;"><%= @past_week_stats.active_pupil_count %></div>
        <div style="font-size: 12px; opacity: 0.9;">Pupils Active</div>
      </div>
    </td>
    <td width="25%" style="padding: 0 5px;">
      <div style="background-color: #f59e0b; border-radius: 8px; text-align: center; padding: 15px; color: #ffffff;">
        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;"><%= @past_week_stats.quiz_submission_count %></div>
        <div style="font-size: 12px; opacity: 0.9;">Quizzes Taken</div>
      </div>
    </td>
  </tr>
</table>

<!-- Most Active Pupils Leaderboard -->
<% if @past_week_stats.top_active_pupils.any? %>
<div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 8px; margin: 25px 0;">
  <h3 style="margin: 0 0 15px 0; color: #333333; font-size: 18px; font-weight: bold;">
    🏆 Most Active Pupils This Week
  </h3>
  <% @past_week_stats.top_active_pupils.first(3).each_with_index do |pupil, index| %>
    <% 
      colors = [
        { bg: '#fef3c7', border: '#f59e0b', badge: '#d97706' },
        { bg: '#f3f4f6', border: '#9ca3af', badge: '#6b7280' },
        { bg: '#fed7aa', border: '#ea580c', badge: '#c2410c' }
      ]
      color = colors[index] || { bg: '#dbeafe', border: '#3b82f6', badge: '#1d4ed8' }
    %>
    <div style="background-color: <%= color[:bg] %>; border: 1px solid <%= color[:border] %>; border-radius: 6px; padding: 12px; margin-bottom: 10px; display: flex; align-items: center; justify-content: space-between;">
      <div style="display: flex; align-items: center;">
        <div style="width: 32px; height: 32px; background-color: <%= color[:badge] %>; color: #ffffff; border-radius: 50%; text-align: center; line-height: 32px; font-weight: bold; font-size: 14px; margin-right: 12px;">
          <%= index + 1 %>
        </div>
        <div style="font-weight: bold; color: #333333; font-size: 16px;"><%= pupil.name %></div>
      </div>
      <div style="background-color: <%= color[:badge] %>; color: #ffffff; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold;">
        <%= pupil.activity_score %> pts
      </div>
    </div>
  <% end %>
</div>
<% end %>

<!-- This Week's Lessons -->
<div style="margin: 30px 0;">
  <div style="border-left: 4px solid #3b82f6; padding-left: 15px; margin-bottom: 20px;">
    <h2 style="margin: 0; color: #333333; font-size: 22px; font-weight: bold;">
      📚 This Week's Lessons
    </h2>
  </div>
  
  <% @teacher.forms.where(lessons: @past_week_stats.taught_lessons).each do |form| %>
    <div style="margin-bottom: 25px;">
      <h3 style="margin: 0 0 15px 0; color: #333333; font-size: 18px; border-bottom: 2px solid #007bff; padding-bottom: 8px;">
        <%= form.name %>
      </h3>
      
      <% @past_week_stats.taught_lessons.where(form: form).each do |lesson| %>
        <% form_unit = @teacher.lessons.find(lesson.id).form_unit %>
        
        <div style="background-color: #ffffff; border: 1px solid #dee2e6; border-radius: 8px; margin: 15px 0; padding: 20px;">
          <h4 style="margin: 0 0 10px 0; color: #007bff; font-size: 18px; font-weight: bold;">
            <%= lesson.name %>
          </h4>
          
          <p style="margin: 5px 0; color: #666; font-size: 14px;">
            <strong>Unit:</strong> <%= form_unit.name %>
          </p>
          
          <% if lesson.objectives.any? %>
            <p style="margin: 10px 0 5px 0; color: #333; font-size: 14px;"><strong>Learning Objectives:</strong></p>
            <ul style="margin: 0; padding-left: 20px; color: #666;">
              <% lesson.objectives.each do |objective| %>
                <li style="margin-bottom: 3px; font-size: 13px;"><%= objective %></li>
              <% end %>
            </ul>
          <% end %>
          
          <!-- Homework Section -->
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e9ecef;">
            <p style="margin: 0 0 10px 0; color: #333; font-size: 14px;"><strong>📝 Homework:</strong></p>
            <% if lesson.homeworks.empty? %>
              <div style="background-color: #f9fafb; padding: 10px; border-radius: 6px; border-left: 3px solid #e5e7eb;">
                <p style="margin: 0; color: #666; font-style: italic; font-size: 13px;">No homework set for this lesson</p>
              </div>
            <% else %>
              <% lesson.homeworks.where(lesson: lesson).each do |homework| %>
                <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; padding: 12px; border-radius: 6px; margin-bottom: 8px;">
                  <div style="font-weight: bold; color: #0c4a6e; font-size: 14px; margin-bottom: 5px;"><%= homework.title %></div>
                  <div style="color: #075985; font-size: 12px;">
                    Tasks: <%= homework.tasks_count || 0 %> | Submissions: <%= homework.submissions.count %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  <% end %>
</div>

<!-- Upcoming Week -->
<div style="margin: 30px 0;">
  <div style="border-left: 4px solid #10b981; padding-left: 15px; margin-bottom: 20px;">
    <h2 style="margin: 0; color: #333333; font-size: 22px; font-weight: bold;">
      📅 Next Week's Lessons
    </h2>
  </div>
  
  <% if @upcoming_week_lessons.empty? %>
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 30px; border-radius: 8px; text-align: center;">
      <div style="font-size: 32px; margin-bottom: 15px;">📅</div>
      <h3 style="margin: 0 0 10px 0; color: #333333; font-size: 18px;">No Upcoming Lessons Scheduled</h3>
      <p style="margin: 0; color: #666; font-size: 14px;">This space will show all your lessons planned for the next 7 days.</p>
    </div>
  <% else %>
    <% @teacher.forms.where(lessons: @upcoming_week_lessons).each do |form| %>
      <div style="margin: 20px 0;">
        <h3 style="margin: 0 0 15px 0; color: #333333; font-size: 18px; border-bottom: 2px solid #28a745; padding-bottom: 8px;">
          <%= form.name %>
        </h3>
        
        <% @upcoming_week_lessons.where(form: form).order(time: :asc).each do |lesson| %>
          <% form_unit = @teacher.lessons.find(lesson.id).form_unit %>
          
          <div style="background-color: #ffffff; border: 1px solid #dee2e6; border-radius: 8px; margin: 15px 0; padding: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #28a745; font-size: 18px; font-weight: bold;">
              <%= lesson.name %>
            </h4>
            
            <p style="margin: 5px 0; color: #666; font-size: 14px;">
              <strong>Unit:</strong> <%= form_unit.name %>
            </p>
            
            <% if lesson.time %>
              <p style="margin: 5px 0; color: #666; font-size: 14px;">
                <strong>Scheduled:</strong> <%= lesson.time.strftime("%A, %B %d at %I:%M %p") %>
              </p>
            <% end %>
            
            <% if lesson.objectives.any? %>
              <p style="margin: 10px 0 5px 0; color: #333; font-size: 14px;"><strong>Learning Objectives:</strong></p>
              <ul style="margin: 0; padding-left: 20px; color: #666;">
                <% lesson.objectives.first(2).each do |objective| %>
                  <li style="margin-bottom: 3px; font-size: 13px;"><%= objective %></li>
                <% end %>
              </ul>
            <% end %>
            
            <!-- Homework Status -->
            <div style="margin-top: 15px; padding: 10px; background-color: #f9fafb; border-radius: 6px;">
              <% if lesson.homeworks.empty? %>
                <span style="color: #333; font-size: 13px;"><strong>Homework:</strong> No homework currently set</span>
              <% else %>
                <span style="color: #333; font-size: 13px;"><strong>Homework:</strong> <%= lesson.homeworks.count %> assignment<%= 's' if lesson.homeworks.count > 1 %> set</span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  <% end %>
</div>

<hr style="border: none; border-top: 1px solid #eeeeee; margin: 30px 0;">

<p style="color: #333333; line-height: 1.6; margin-bottom: 20px; text-align: center; font-weight: bold;">
  Keep up the excellent work with your teaching! 🌟
</p>

<p style="color: #666666; font-size: 12px; line-height: 1.4; margin: 0;">
  This weekly digest shows your teaching activity from the previous Monday-Sunday period.
</p>
<p style="color: #666666; font-size: 12px; line-height: 1.4; margin: 10px 0 0 0;">
  This is an automated weekly summary from Developing Experts.
</p>