<% content_for :title do %>
  School Weekly Digest - Developing Experts
<% end %>

<div style="text-align: center; margin-bottom: 30px;">
  <h1 style="color: #333333; margin: 0; font-size: 24px;">
    School Weekly Digest
  </h1>
</div>

<!-- Teacher Stats -->
<div style="margin: 30px 0;">
  <h2 style="margin: 0 0 15px 0; color: #333333; font-size: 18px;">
    Teacher Activity Summary
  </h2>
  
  <% if @teacher_stats.present? %>
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; margin: 25px 0;">
      <% @teacher_stats.each do |stat| %>
        <div style="background-color: #ffffff; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; padding: 15px;">
          <h4 style="margin: 0 0 15px 0; color: #333; font-size: 18px; font-weight: bold;">
            <%= stat.teacher.name %>
          </h4>
          
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
            <div style="text-align: center; background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">LESSONS TAUGHT</p>
              <p style="margin: 0; color: #007bff; font-size: 20px; font-weight: bold;"><%= stat.lessons_taught_count %></p>
            </div>
            <div style="text-align: center; background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">QUIZZES SET</p>
              <p style="margin: 0; color: #28a745; font-size: 20px; font-weight: bold;"><%= stat.quizzes_set_count %></p>
            </div>
            <div style="text-align: center; background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">MARKS DONE</p>
              <p style="margin: 0; color: #ffc107; font-size: 20px; font-weight: bold;"><%= stat.marks_done_count %></p>
            </div>
          </div>
          
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
            <div style="text-align: center; background-color: #e9ecef; padding: 10px; border-radius: 3px;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">LOGINS</p>
              <p style="margin: 0; color: #6f42c1; font-size: 20px; font-weight: bold;"><%= stat.login_count %></p>
            </div>
            <div style="text-align: center; background-color: #e9ecef; padding: 10px; border-radius: 3px;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">PUPIL VIEWS</p>
              <p style="margin: 0; color: #17a2b8; font-size: 20px; font-weight: bold;"><%= stat.presentation_view_count %></p>
            </div>
            <div style="text-align: center; background-color: #e9ecef; padding: 10px; border-radius: 3px;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">PUPIL QUIZZES</p>
              <p style="margin: 0; color: #fd7e14; font-size: 20px; font-weight: bold;"><%= stat.quizzes_taken_count %></p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; text-align: center;">
      <p style="margin: 0; color: #666; font-size: 16px;">👨‍🏫 No Teacher Activity Found</p>
      <p style="margin: 10px 0 0 0; color: #999; font-size: 14px;">Stats for the past week, including lessons taught and quizzes set, will automatically appear here once teachers become active.</p>
    </div>
  <% end %>
</div>

<!-- Pupil Stats -->
<div style="margin: 30px 0;">
  <h2 style="margin: 0 0 15px 0; color: #333333; font-size: 18px;">
    Top 20 Most Active Pupils
  </h2>
  
  <% if @pupil_stats.present? %>
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; margin: 25px 0;">
      <% @pupil_stats.each_with_index do |stat, index| %>
        <div style="background-color: #ffffff; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; padding: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h4 style="margin: 0; color: #333; font-size: 16px; font-weight: bold;">
              #<%= index + 1 %> <%= stat.pupil.name %>
            </h4>
            <div style="background-color: #007bff; color: white; padding: 5px 10px; border-radius: 15px; font-size: 14px; font-weight: bold;">
              <%= stat.activity_score %> activities
            </div>
          </div>
          
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 10px;">
            <div>
              <p style="margin: 0 0 5px 0; color: #666; font-size: 12px; font-weight: bold;">CLASSES:</p>
              <p style="margin: 0; color: #333; font-size: 14px;"><%= stat.class_names %></p>
            </div>
            <div>
              <p style="margin: 0 0 5px 0; color: #666; font-size: 12px; font-weight: bold;">TEACHERS:</p>
              <p style="margin: 0; color: #333; font-size: 14px;"><%= stat.teacher_names %></p>
            </div>
          </div>
          
          <div style="display: flex; justify-content: space-around; background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
            <div style="text-align: center;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">PRESENTATIONS</p>
              <p style="margin: 0; color: #007bff; font-size: 18px; font-weight: bold;"><%= stat.presentation_views %></p>
            </div>
            <div style="text-align: center;">
              <p style="margin: 0; color: #666; font-size: 12px; font-weight: bold;">QUIZZES</p>
              <p style="margin: 0; color: #28a745; font-size: 18px; font-weight: bold;"><%= stat.quiz_submissions %></p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; text-align: center;">
      <p style="margin: 0; color: #666; font-size: 16px;">📈 No Pupil Activity to Show</p>
      <p style="margin: 10px 0 0 0; color: #999; font-size: 14px;">When pupils view presentations or complete quizzes, the top 20 most active pupils from the past week will be listed here.</p>
    </div>
  <% end %>
</div>

<hr style="border: none; border-top: 1px solid #eeeeee; margin: 30px 0;">

<p style="color: #333333; line-height: 1.6; margin-bottom: 20px;">
  This summary shows your school's teaching and learning activity from the previous Monday-Sunday period.
</p>

<p style="color: #666666; font-size: 12px; line-height: 1.4; margin: 0;">
  Keep up the excellent work with your school administration!
</p>
<p style="color: #666666; font-size: 12px; line-height: 1.4; margin: 10px 0 0 0;">
  This is an automated weekly school summary from Developing Experts.
</p>
