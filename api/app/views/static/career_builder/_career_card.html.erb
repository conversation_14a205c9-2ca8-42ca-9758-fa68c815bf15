<% is_favourite = local_assigns.fetch(:is_favourite, false) %>
<% horiztontal = local_assigns.fetch(:horiztontal, false) %>

<% if horiztontal %>
  <%= render HorizontalCardComponent.new(
    title: career.career_name,
    pre_title: career.education_level.present? ? career.education_level_short : "All Levels",
    body: career.career&.body.present? ? truncate(career.career.body, length: 100) : 
          (career.career_path.present? && career.career_path["description"].present? ? truncate(career.career_path["description"], length: 100) : nil),
    image_background_img: career.career_path.present? && career.career_path["image"].present? ? career.career_path["image"] : nil,
    path: show_v2_career_builder_path(career),
    tags: [is_favourite ? "Favourite" : nil, career.job_family&.name].compact
  ) %>
<% else %>
  <%= render CardComponent.new(
    title: career.career_name,
    pre_title: career.education_level.present? ? career.education_level_short : "All Levels",
    body: career.career&.body.present? ? truncate(career.career.body, length: 100) : 
          (career.career_path.present? && career.career_path["description"].present? ? truncate(career.career_path["description"], length: 100) : nil),
    image_background_img: career.career_path.present? && career.career_path["image"].present? ? career.career_path["image"] : nil,
    path: show_v2_career_builder_path(career),
    tags: [is_favourite ? "Favourite" : nil, career.job_family&.name].compact
  ) %>
<% end %>