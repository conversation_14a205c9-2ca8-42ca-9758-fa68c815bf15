<%= javascript_include_tag 'scripts/quip_quiz_controller.js' %>

<% 
  compact = local_assigns.fetch(:compact, false)
  quiz = local_assigns.fetch(:quiz, @quiz)
  submit_path = local_assigns.fetch(:submit_path, @submit_path)
%>

<div id="quizwrapper-<%= quiz['id'] %>" class="<%= local_assigns.fetch(:class_name, '') %>">
  <% if @results.present? %>
    <div>
      <p class="text-white mb-2">
        Score: <%= @results['score'] %> out of <%= @results['total'] %> 
      </p>
      <div id="quiz-questions" class="space-y-4 text-left">
        <% @results['data'].each do |result| %>
          <%= render QuizResultComponent.new(result, compact: compact) %>
          <pre style="display: none;"><%= result.to_json.html_safe %></pre>
        <% end %>
        <%- unless @results_only %>
          <div>
            <button type="button" onclick="window.location.reload()" class="btn btn-base btn-cyan hidden" data-reset-quiz>Restart Quiz</button>
          </div>
        <%- end %>
      </div>
    </div>
  <% else %>
    <div class="text-control-medium text-left">
      <div class="px-8 pt-8 pb-16 rounded-xl bg-secondary-dark-blue">
        <div id="start-quiz" class="text-center" data-start-quiz-panel>
          <h1 class="text-3xl text-white py-8 nova"><%= quiz["name"] %></h1>
          <button type="button" class="btn btn-lg btn-cyan" data-start-quiz>Start Quiz</button>
        </div>

        <div id="quiz-questions" class="question-container">
          <div id="quiz-questions" class="question-container">
            <% quiz["questions"].each do |question| %>
              <div id="<%= question['id'] %>" data-question="<%= question['id'] %>" style="display: none;">
                <% if question['type'] == 'multi-choice' %>
                  <%= render MultiChoiceQuestionComponent.new(question) %>
                <% elsif question['type'] == 'fill-in-blanks' %>
                  <%= render FillInBlanksQuestionComponent.new(question) %>
                <% elsif question['type'] == 'image-bucket' %>
                  <%= render ImageBucketQuestionComponent.new(question) %>
                <% elsif question['type'] == 'text-bucket' %>
                  <%= render TextBucketQuestionComponent.new(question) %>
                <% elsif question['type'] == 'sort-list' %>
                  <%= render SortListQuestionComponent.new(question) %>
                <% elsif question['type'] == 'free-text' %>
                  <%= render FreeTextQuestionComponent.new(question) %>
                <% else %>
                  <p class="text-white">Unknown template <%= question["type"] %> </p>
                <% end %>
                <pre style="display: none;"><%= question.to_json.html_safe %></pre>
              </div>
            <% end %>
            <div data-question-feedback style="display: none;">
              <div class="prompt mb-4">
                <div>
                  <div data-question-prompt class="header text-2xl text-white font-bold"></div>
                </div>
              </div>

              <h2 class="text-xl mb-2 nova text-white"><i class="fas fa-times"></i> Answer incorrect</h2>
              <p class="mb-2 text-white text-lg mb-4">Look at the slide below to work out the answer.</p>
              <div class="bg-black rounded-lg overflow-hidden">
                <div class="p-4 bg-white">
                  <h3 class="text-xl text-black nova" data-body></h3>
                </div>
                <div data-video style="display: none;">
                  <video></video>
                </div>
                <div class="h-[400px] w-full bg-no-repeat bg-center bg-cover bg-slate-500" data-image style="display: none;">
                </div>
              </div>
            </div>
            <div class="hidden flex gap-2 mt-8">
              <div class="hidden" data-back-button>
                <button type="button" class="btn btn-base btn-white-outline">
                  <i class="fas fa-chevron-left mr-4"></i> Back
                </button>
              </div>
              <button type="button" id="quiz-submit-button" class="btn btn-base btn-cyan" data-submit-button>Submit</button>
            </div>
          </div>
          <form id="submit-form-action" hx-post="<%= submit_path %>" class="hidden" hx-swap="outerHTML" hx-target="#quizwrapper-<%= quiz['id'] %>">
            <input name="results">
            <button type="submit">submit</button>
          </form>
        </div>
      </div>
    </div>
    <% if defined?(after_load) %>
      <script>
        document.addEventListener("DOMContentLoaded", () => {
            window.quiz_controllers = window.quiz_controllers || []

            function initQuiz(quiz) {
                // Check if quiz is already mounted
                // If it is, don't mount it again
                if (window.quiz_controllers.find(controller => controller.id == quiz.id)) {
                    console.log("Already mounted", quiz.id)
                    return
                }
                const quizId = `quizwrapper-${quiz.id}`
                const controller = new window.QuipQuizController(quiz, document.getElementById(quizId));
                window.quiz_controllers.push(controller)
            }

            function waitForScript(i) {
                if (!window.QuipQuizController) {
                    console.log("waiting for script to load...", i)
                    setTimeout(() => waitForScript(i), 100);
                } else {
                    initQuiz(<%= quiz.to_json.html_safe %>)
                }
            }

            waitForScript(0);
          })
      </script>
    <% else %>
      <script>
          document.addEventListener("htmx:afterSettle", () => {
              window.quiz_controllers = window.quiz_controllers || []

              function initQuiz(quiz) {
                  // Check if quiz is already mounted
                  // If it is, don't mount it again
                  if (window.quiz_controllers.find(controller => controller.id == quiz.id)) {
                      console.log("Already mounted", quiz.id)
                      return
                  }
                  const quizId = `quizwrapper-${quiz.id}`
                  const controller = new window.QuipQuizController(quiz, document.getElementById(quizId));
                  window.quiz_controllers.push(controller)
              }

              function waitForScript(i) {
                  if (!window.QuipQuizController) {
                      console.log("waiting for script to load...", i)
                      setTimeout(() => waitForScript(i), 100);
                  } else {
                      initQuiz(<%= quiz.to_json.html_safe %>)
                  }
              }

              waitForScript(0);
          })
      </script>
    <% end %>
  <% end %>
</div>
