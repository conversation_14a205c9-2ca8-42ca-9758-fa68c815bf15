<%# api/app/views/static/presentation/_slide.html.erb %>
<div id="presentation-slide" class="flex flex-col h-dvh" role="main" aria-label="Slide content">
  <% if slide[:top_text].present? %>
    <div id="slide-header" data-slide-section="header" class="bg-white">
      <p id="slide-header-text" data-slide-text="header" class="text-2xl sm:text-3xl md:text-5xl leading-tight sm:leading-normal font-bold p-4"><%= slide[:top_text] %></p>
    </div>
  <% end %>

  <div id="slide-content" data-slide-section="content" class="relative flex-grow overflow-hidden" style="background-color: <%= slide.dig(:background, :color) || '#FFFFFF' %>;">
    <% unless slide.dig(:background, :media).blank? %>
      <div class="absolute inset-0 top-0 left-0 grid gap-4
        <%= 'grid-cols-1' if slide[:background][:media].size == 1 %>
        <%= 'grid-cols-2' if slide[:background][:media].size == 2 %>
        <%= 'grid-cols-3' if slide[:background][:media].size == 3 %>
        <%= 'grid-cols-2 grid-rows-2' if slide[:background][:media].size == 4 %>"
      >
        <% slide[:background][:media].each do |media| %>
          <% if media[:type] == 'image' && media[:url].present? %>
            <div style="background-image: url('<%= media[:url] %>')" alt="Slide Image" class="w-full h-full bg-no-repeat bg-center bg-<%= media[:background_size].present? ? media[:background_size] : 'cover' %>" role="img" aria-label="Slide image"></div>
          <% elsif media[:type] == 'video' && media[:url].present? %>
            <video src="<%= media[:url] %>" class="w-full h-full object-cover" autoplay muted loop aria-label="Slide video"></video>
          <% end %>
        <% end %>
      </div>
    <% end %>
    <div class="relative h-full w-full">
      <% if slide[:type] == 'dynamic' %>
        <%= render 'dynamic_slide_content', slide_data: slide.dig(:content, :dynamic_data), presentation: presentation %>
      <% elsif ['homework'].include?(slide.dig(:content, :type)) %>
        <%= render 'homework_slide_content', slide: slide, homework: slide.dig(:content, :homework), homework_task: slide.dig(:content, :homework_task), presentation: presentation %>
      <% elsif ['text'].include?(slide.dig(:content, :type)) %>
        <%= render 'text', slide: slide %>
      <% elsif ['quip_question', 'quipQuiz'].include?(slide.dig(:content, :type)) %>
        <%= render 'quiz_slide_question', slide: slide, presentation: presentation, question: slide.dig(:content, :quiz_question) %>
      <% elsif ['complete_quiz'].include?(slide.dig(:content, :type)) %>
        <%= render 'quiz_slide_question', slide: slide, presentation: presentation, full_quiz: true %>
      <% elsif slide.dig(:content, :type) == "expertVideo" %>
        <div class="presentation-video-container !static" hx-trigger="load" hx-get="/video/<%= slide.dig(:content, :de_video_id) %>?autoPlay=1" hx-swap="outerHtml" hx-target="this"></div>
      <% elsif ["keyword", "keywords", "rocketWords"].include?(slide.dig(:content, :type)) %>
        <%= render 'keywords', slide: slide, presentation: presentation, keywords: slide.dig(:content, :keywords) %>
      <% elsif ["rocketWordQuiz", "quiz"].include?(slide.dig(:content, :type)) %>
        <%= render 'keywords_quiz', slide: slide, presentation: presentation, keywords: slide.dig(:content, :keywords) %>
      <% elsif ['phet', 'embed', 'tour', 'three60'].include?(slide.dig(:content, :type)) %>
        <iframe
          class="w-full h-full border-0"
          src="<%= slide.dig(:content, :iframe_src) %>"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
          title="Embedded slide content"
        ></iframe>
      <% elsif ['video', 'expert', 'mission_assignment', 'song', 'timer'].include?(slide.dig(:content, :type)) %>
        <%= render 'video', slide: slide, presentation: presentation %>
      <% end %>
    </div>
  </div>

  <% if slide[:bottom_text].present? %>
    <div id="slide-footer" data-slide-section="footer" class="bg-white">
      <p id="slide-footer-text" data-slide-text="footer" class="text-2xl sm:text-3xl md:text-5xl leading-tight sm:leading-normal font-bold p-4"><%= slide[:bottom_text] %></p>
    </div>
  <% end %>
  <% unless local_assigns[:no_controls] %>  
    <%= render 'slide_controls', slide: slide, presentation: presentation %>
  <% end %>
</div>
