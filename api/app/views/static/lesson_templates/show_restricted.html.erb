<% content_for :title, "#{@lesson_template.name}" %>
<% content_for :meta_description, "#{@lesson_template.name.gsub(/^\d+\.\s*/, '')} – a complete resource with clear objectives, interactive content, and built-in assessments to support effective teaching." %>
<%= javascript_include_tag 'scripts/printable.js' %>
<% set_seo_tags(static_missions_url(@lesson_template)) %>

<div>
  <!-- Sci Enquiry types are being portaled here to get out of overflow -->
  <!-- z-1000 above header bar (999) -->
  <div id="enq_portal" class="relative z-[1000]"></div>

  <div class="max-w-screen-xl mx-auto py-8 px-4">
    <div class="flex flex-wrap gap-8 justify-between mb-4">
      <div class="flex-[2] max-w-4xl min-w-[300px] space-y-4">
        <div class="max-w-7xl mx-auto w-full">
          <% if @return_path.present? %>
            <%= render ButtonComponent::Back.new(link: @return_path[:to], text: @return_path[:label]) %>
          <% end %>
        </div>
        <%= render "static/lesson_templates/curriculum_subject_banner", curriculum: @curriculum, subject: @lesson_template.subject %>
        <h1 class="text-white text-4xl mb-4 font-bold nova">
          <%= @lesson_template.name %>
        </h1>
        <div>
          <a class="btn btn-cyan btn-lg btn-disabled" disable>
            Start presentation
          </a>
        </div>
        <%= render "static/lesson_templates/mission_objectives", objectives: @lesson_template.objectives %>
      </div>
      <div  class="flex-[3] max-w-4xl min-w-[300px]">
        <div class="bg-no-repeat bg-center bg-cover min-h-48 md:min-h-96 h-full overflow-hidden rounded-2xl w-full relative z-2" style="background-image: url('<%= @lesson_template.banner_image_url %>')" aria-label="<%= @lesson_template.name%>">
          <!-- banner image -->
          <div class="absolute top-0 right-0 p-4">
            <%= render ScientificEnquiryTypePanelComponent.new(enquiry_types: @lesson_template.scientific_enquiry_types, portal_id: "enq_portal")%>
          </div>
        </div>
      </div>
    </div>

    <a href="<%= lesson_subscription_path %>" class="bg-mid-blue p-4 text-center rounded-xl text-white font-bold text-xl block w-full mb-4">
      <%= lesson_subscription_prompt_text %>
    </a>
    <!-- MARK: GAMES PANEL -->
    <%= render "static/lesson_templates/quiz_links" %>
    <div class="w-full grid md:grid-cols-[2fr_1fr] items-start gap-4">
      <!-- MARK: LEFT SIDE -->
      <div>
        <!-- LESSON RESOURCES -->
        <div class="border border-gray-700 p-4 rounded-xl mb-4">
          <h2 class="text-white mb-4 text-xl nova">Lesson Resources</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Lesson Plan -->
            <div class="bg-secondary-dark-blue p-4 rounded-xl">
              <h4 class="text-white text-lg nova">Lesson Plan</h4>
              <p class="text-white mb-2">View and print the lesson plan</p>
              <a class="btn btn-base btn-flat-white btn-disabled w-full" disabled>View</a>
            </div>

            <!-- Downloads -->
            <div class="bg-secondary-dark-blue p-4 rounded-xl">
              <h4 class="text-white text-lg nova">Downloads</h4>
              <p class="text-white mb-2">Save files related to this lesson</p>
              <div class="flex flex-col gap-2">
                <% @lesson_template.documents.each do |document| %>
                  <button class="btn btn-white btn-base w-full flex items-center gap-2 btn-disabled" disabled>
                    <i class="fa-duotone fa-solid fa-download"></i>
                    <%= document.name %>
                  </button>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded-xl p-6 border border-gray-700 mb-4">
          <!-- MARK: KEYWORDS -->
          <div>
            <div class="flex justify-between flex-wrap gap-2">
              <h2 class="text-white text-xl nova">Keywords</h2>
              <button onclick="handlePrintKeywords()" class="btn btn-flat-white btn-sm">Print</button>
            </div>
            <p class="text-white mb-6">Words and meanings to learn</p>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
              <% @lesson_template.keywords.order(weight: :asc).map do |keyword| %>
                <%= render CardComponent.new(
                  title: keyword.name,
                  body: keyword.body,
                  image_background_fileboy_id: keyword.fileboy_image_id)
                %>
              <%end%>
            </div>
          </div>
        </div>
        <% campaign_units = @lesson_template.campaign_units.where(show_on_unit_page: true) %>
        <% if campaign_units.any? %>
          <%= render CampaignUnitPanelComponent.new(campaign_units: campaign_units, user: @current_user)%>
          <div></div>
        <%end%>

        <%- if @lesson_template.authors.present? %>
          <div class="border border-gray-700 p-4 rounded-xl max-w-4xl mt-4">
            <h3 class="mb-4 text-xl text-white nova">This lesson has been written by</h3>
            <%= render AuthorsPanelComponent.new(authors: @lesson_template.authors) %>
          </div>
        <% end %>
        <!-- END OF LEFT SIDE -->
      </div>
      <!-- MARK: RIGHT SIDE -->
      <!-- REQUIRED RESOURCES -->
      <div>
          <%= render "static/lesson_templates/required_resources", lesson_plan_layout: @lesson_template.lesson_plan_layout, new_lesson_plan_resources: @lesson_template.new_lesson_plan_resources, plan_activities: @lesson_template.plan_activities %>
          <%= render "static/lesson_templates/links", links: @lesson_template.links, restricted: true %>
          <div class="space-y-4">
            <% @videos.map do |video| %>
            <%= render ModalComponent.new(title: lesson_subscription_prompt_text) do |modal| %>
              <% modal.with_trigger do %>
                <div class="cursor-pointer bg-white rounded-xl min-h-36 p-4" style="flex: 1;">
                  <div class="video-player bg-de-blue rounded-t-xl h-36 flex items-center justify-center mb-4">
                    <i class="fa-duotone fa-solid fa-play text-white text-4xl"></i>
                  </div>
                  <p class="text-lg font-semibold"><%= video[:name]%></p>
                </div>
              <% end %>

              <p class="mb-2">This lesson is only available to users with an active subscription.</p>

              <% modal.with_footer do %>
                <%= render ButtonComponent::Base.new( text: "Get Started", url: lesson_subscription_path ) %>
                <%= render ButtonComponent::Cancel.new( onclick: "this.closest('dialog').close();" ) %>
              <% end %>
            <% end %>
          <% end %>
        </div>
        <% if @career_paths.any? %>
          <div class="border border-gray-700 p-4 rounded-xl max-w-4xl mt-4">
            <h3 class="mb-4 text-xl text-white nova">Careers related to this lesson</h3>
            <%= render 'static/lesson_templates/career_paths', career_paths: @career_paths %>
          </div>
        <% end %>
      </div>
      <!-- END OF RIGHT SIDE -->
      </div>
  </div>
  <%= render "static/lesson_templates/keyword_printable" %>
</div>
