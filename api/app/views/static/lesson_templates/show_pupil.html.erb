<% content_for :title, "#{@lesson_template.name}" %>
<%= javascript_include_tag 'scripts/printable.js' %>
<% content_for :meta_description, "#{@lesson_template.name.gsub(/^\d+\.\s*/, '')} – a complete resource with clear objectives, interactive content, and built-in assessments to support effective teaching." %>
<div data-no-print>
  <!-- Progress tooltips are being portaled here -->
  <!-- z-1000 above header bar (999) -->
  <div id="progress_portal" class="relative z-[1000]"></div>

  <div class="max-w-screen-xl mx-auto py-8 px-4">
    <%- if @current_user %>
      <div class="pt-12"></div>
    <% end %>
    <div class="flex flex-wrap gap-8 justify-between mb-4">
      <div class="flex-[2] max-w-4xl min-w-[300px] space-y-4">
        <div class="max-w-7xl mx-auto w-full">
          <%= render ButtonComponent::Back.new(link: pupil_lessons_index_path, text: 'All lessons') %>
        </div>
        <%= render "static/lesson_templates/curriculum_subject_banner", curriculum: @curriculum, subject: @lesson_template.subject %>
        <div class="max-w-7xl mx-auto w-full">
          <span class="font-bold text-white text-lg"><%= @lesson.form.name %></span>
          <span class="font-bold ml-3 text-white text-lg"><%= @lesson.time.strftime('%d/%m/%Y') %></span>
        </div>
        <h1 class="text-white text-4xl mb-4 nova">
          <%= @lesson_template.name %>
        </h1>
        <div>
          <%= link_to 'Start presentation', static_presentation_path(@lesson_template, @lesson.id, user_id: @current_pupil.id, return_to: request.fullpath), class: 'btn btn-cyan btn-lg' %>
        </div>
        <%= render "static/lesson_templates/mission_objectives", objectives: @lesson_template.objectives %>
      </div>
      <div  class="flex-[3] max-w-4xl min-w-[300px]">
        <div class="bg-no-repeat bg-center bg-cover min-h-48 md:min-h-96 h-full rounded-2xl w-full relative z-2" style="background-image: url('<%= @lesson_template.banner_image_url %>')" aria-label="<%= @lesson_template.name%>">
          <!-- banner image -->
          <div class="absolute top-0 right-0 p-4">
          </div>
        </div>
      </div>
    </div>

    <%= render "static/lesson_templates/quiz_links",
        rocket_quiz_url: @lesson_template&.keywords.present? ? pupil_lesson_show_rocket_word_quiz_path(@lesson, user_id: @current_pupil.id, return_to: request.fullpath) : nil,
        assessment_quiz_url: @lesson_template&.quip_quiz.present? ? pupil_lesson_show_assessment_quiz_path(@lesson, user_id: @current_pupil.id, return_to: request.fullpath) : nil,
        local_word_search_url: @lesson_template&.keywords.present? ? pupil_lesson_show_word_search_path(@lesson, user_id: @current_pupil.id, return_to: request.fullpath) : nil,
        hide_when_empty: true
    %>
    <div class="w-full grid md:grid-cols-[2fr_1fr] items-start gap-4">
      <!-- MARK: LEFT SIDE -->
      <div>
        <!-- LESSON RESOURCES -->
        <div class="border border-gray-700 p-4 rounded-xl mb-4">
          <h2 class="text-white mb-4 text-xl nova">Lesson Resources</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <%if @current_pupil&.school&.show_lesson_plan_pupil_area %>
              <!-- Lesson Plan -->
              <div class="bg-secondary-dark-blue p-4 rounded-xl">
                <h4 class="text-white text-lg nova">Lesson Plan</h4>
                <p class="text-white mb-2">View and print the lesson plan</p>
                <%= render ButtonComponent::Base.new(text: "View", url: static_missions_plan_path(@lesson_template.id, {return_to: request.fullpath}), variant: :white) %>
              </div>
            <%end%>
            <!-- Downloads -->
            <div class="bg-secondary-dark-blue p-4 rounded-xl">
              <h4 class="text-white text-lg nova">Downloads</h4>
              <p class="text-white mb-2">Save files related to this lesson</p>
              <div class="flex flex-col gap-2">
                <% @lesson_template.documents.each do |document| %>
                  <%= link_to document.fileboy_url({ lesson_id: @lesson.id, user_id: @current_pupil.id, document_type: 'handout' }), class: "btn btn-white btn-base w-full flex items-center gap-2", target: "_blank", download: true do %>
                      <i class="fa-duotone fa-solid fa-download"></i>
                      <%= document.name %>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded-xl p-6 border border-gray-700 mb-4">
          <!-- MARK: KEYWORDS -->
          <div>
            <div class="flex justify-between flex-wrap gap-2">
              <h2 class="text-white text-xl nova">Keywords</h2>
              <button onclick="handlePrintKeywords()" class="btn btn-flat-white btn-sm">Print</button>
            </div>
            <p class="text-white mb-6">Words and meanings to learn</p>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
              <% @lesson_template.keywords.order(weight: :asc).map do |keyword| %>
                <%= render CardComponent.new(
                  title: keyword.name,
                  body: keyword.body,
                  image_background_fileboy_id: keyword.fileboy_image_id)
                %>
              <%end%>
            </div>
          </div>
        </div>
        <!-- END OF LEFT SIDE -->
        <% campaign_units = @lesson_template.campaign_units.where(show_on_unit_page: true) %>
        <% if campaign_units.any? %>
          <div>
            <%= render CampaignUnitPanelComponent.new(campaign_units: campaign_units, user: @current_pupil)%>
          </div>
        <%end%>
      </div>

      <!-- MARK: RIGHT SIDE -->
      <div>
        <div class="bg-secondary-dark-blue p-4 rounded-xl mb-4">
          <h2 class="text-white text-xl nova">Your progress</h2>
          <p class="mb-2">You've currently complete <%= sprintf("%.2f", @progress_pct) %>% of this lesson's tasks.</p>
          <div class="flex gap-4 items-center">
            <div class="grow">
              <div class="h-4 w-full bg-white border-2 border-dashed border-de-blue rounded-full overflow-hidden">
                <div class="h-full bg-de-blue" style="width: <%= [@progress_pct, 100].min %>%;"></div>
              </div>
            </div>
            <div class="flex gap-4 w-max text-lg">
              <% @progress.map do |item| %>
                <a
                  data-tooltip-id="<%= item[:key] %>"
                  href="<%= item[:path] %>"
                  class="<%= item[:complete] ? "text-de-blue" : "text-white" %>"
                >
                  <%if item[:key] == 'presentation_viewed'%>
                    <i class="fas fa-film"></i>
                  <%elsif item[:key] == 'rocket_word_quiz'%>
                    <i class="fab fa-rocketchat"></i>
                  <%elsif item[:key] == 'summative_quiz'%>
                    <i class="fas fa-poll"></i>
                  <%elsif item[:key] == 'tracking_word_search'%>
                    <i class="fas fa-search"></i>
                  <%end%>
                </a>
                <div data-tooltip-body="<%= item[:key] %>" class="fixed bg-white rounded-lg p-4 z-50 shadow-lg border border-gray-300 max-w-md" style="visibility: hidden;">
                  <p class="whitespace-pre-wrap text-black"><%= item[:body][item[:complete] ? :complete : :todo] %></p>
                </div>
              <% end %>
              <script type="module">
                import {
                  computePosition,
                  offset,
                  shift,
                } from "https://cdn.jsdelivr.net/npm/@floating-ui/dom@latest/+esm"

                function initializeTooltips() {
                  // build the popout elements
                  const portal = document.getElementById("progress_portal")

                  const progressItems = <%= @progress.to_json.html_safe %>
                  for(const el of progressItems) {
                    console.log(el)
                    // find the body node with the id of the enquiry type.key
                    const node = document.querySelector(`[data-tooltip-body="${el.key}"]`)
                    if(!node) {
                      console.error(`Could not find node with id ${el.uid}`)
                      continue
                    }
                    // copy the node
                    const clone = node.cloneNode(true)
                    // delete the original
                    node.remove()
                    // append the copy to the portal
                    portal.appendChild(clone)
                  }

                  // attach the tooltips
                  document.querySelectorAll("[data-tooltip-id]").forEach((button) => {
                    let isOpen = false
                    const id = button.getAttribute("data-tooltip-id")
                    const tooltip = document.querySelector(`[data-tooltip-body="${id}"]`)
                    console.log("TOOLTIP", tooltip)
                    tooltip.style.visibility = "hidden" // Ensure tooltip starts hidden
                    tooltip.style.opacity = "0" // Ensure tooltip starts fully transparent

                    function handleOpen() {
                      computePosition(button, tooltip, {
                        placement: "bottom",
                        middleware: [offset(10), shift({ padding: 5 })],
                        strategy: "fixed",
                      }).then(({ x, y }) => {
                        tooltip.style.left = `${x}px`
                        tooltip.style.top = `${y}px`
                        tooltip.style.visibility = "visible"
                        tooltip.style.opacity = "1" // Fade in
                      })
                      isOpen = true
                    }
                    function handleClose() {
                      tooltip.style.visibility = "hidden"
                      tooltip.style.opacity = "0" // Fade out
                      isOpen = false
                    }

                    // mobile touch click - open/close clicking icon
                    button.addEventListener("touchstart", (event) => {
                      event.preventDefault()
                      if(isOpen) {
                        handleClose()
                      } else {
                        handleOpen()
                      }
                    })

                    // click outside to close for mobile as they don't get enter/leave events
                    document.addEventListener("touchstart", (event) => {
                      if (isOpen && !button.contains(event.target)) {
                        handleClose()
                      }
                    })

                    // Mouse over / leave controller for desktop
                    button.addEventListener("mouseenter", (event) => {
                      handleOpen()
                    })

                    button.addEventListener("mouseleave", function (event) {
                      handleClose()
                    })
                  })
                }
                document.addEventListener("DOMContentLoaded", initializeTooltips)
              </script>
            </div>
          </div>
        </div>
        <%= render "static/lesson_templates/links", links: @lesson_template.links %>
        <div class="space-y-4">
          <% @videos.map do |video| %>
            <%= render "static/lesson_templates/video", video: video %>
          <% end %>
        </div>
        <% if @recommended_careers.any? %>
          <div class="border border-gray-700 p-4 rounded-xl max-w-4xl mt-4">
            <h3 class="mb-4 text-xl text-white nova">Careers related to this lesson</h3>
            <%= render 'static/lesson_templates/recommended_careers', careers: @recommended_careers %>
          </div>
        <% end %>
        <% if @career_paths.any? %>
          <div class="border border-gray-700 p-4 rounded-xl max-w-4xl mt-4">
            <h3 class="mb-4 text-xl text-white nova">Careers related to this lesson</h3>
            <%= render 'static/lesson_templates/career_paths', career_paths: @career_paths %>
          </div>
        <% end %>
      <!-- END OF RIGHT SIDE -->
      <% campaign_units = @lesson_template.campaign_units.where(show_on_unit_page: true) %>
      <% if campaign_units.any? %>
        <div>
          <%= render CampaignUnitPanelComponent.new(campaign_units: campaign_units, hide_links: true, user: @current_pupil)%>
        </div>
      <%end%>
    </div>
  </div>
</div>

<%= render "static/lesson_templates/keyword_printable" %>
