<% content_for :title, "#{@lesson_template.name}" %>
<%= javascript_include_tag 'scripts/printable.js' %>
<% content_for :meta_description, "#{@lesson_template.name.gsub(/^\d+\.\s*/, '')} – a complete resource with clear objectives, interactive content, and built-in assessments to support effective teaching." %>
<% set_seo_tags(static_missions_url(@lesson_template)) %>
<div data-no-print>
  <!-- Sci Enquiry types are being portaled here to get out of overflow -->
  <!-- z-1000 above header bar (999) -->
  <div id="enq_portal" class="relative z-[1000]"></div>

  <div class="max-w-screen-xl mx-auto py-8 px-4">
    <%- if @current_user %>
      <div class="pt-12"></div>
    <% end %>
    <div class="flex flex-wrap gap-8 justify-between mb-4">
      <div class="flex-[2] max-w-4xl min-w-[300px] space-y-4">
        <div class="max-w-7xl mx-auto w-full">
          <% if @return_path.present? %>
            <%= render ButtonComponent::Back.new(link: @return_path[:to], text: @return_path[:label]) %>
          <% end %>
        </div>
        <%= render "static/lesson_templates/curriculum_subject_banner", curriculum: @curriculum, subject: @lesson_template.subject %>
        <h1 class="text-white text-4xl mb-4 nova">
          <%= @lesson_template.name %>
        </h1>
        <div>
          <%= link_to 'Start presentation', static_presentation_path(@lesson_template, return_to: request.fullpath), class: 'btn btn-cyan btn-lg' %>
        </div>
        <%= render "static/lesson_templates/mission_objectives", objectives: @lesson_template.objectives %>
      </div>
      <div  class="flex-[3] max-w-4xl min-w-[300px]">
        <div class="bg-no-repeat bg-center bg-cover min-h-64 md:min-h-96 h-full rounded-2xl w-full relative z-2" style="background-image: url('<%= @lesson_template.banner_image_url %>')" aria-label="<%= @lesson_template.name%>">
          <!-- banner image -->
          <div class="absolute top-0 right-0 p-4">
            <%- if @current_user && !@lesson_template.viewable_only && !@lesson %>
              <div class="w-[280px]">
                <%= render AddToClassPanelComponent.new(year_id: @return_unit&.year&.id, template_ids: [@lesson_template.id]) %>
              </div>
            <% end %>
            <%= render ScientificEnquiryTypePanelComponent.new(enquiry_types: @lesson_template.attr_with_source_fallback(:scientific_enquiry_types), portal_id: "enq_portal")%>
          </div>
        </div>
      </div>
    </div>
    <!-- MARK: LESSON -->
    <%- if @lesson %>
      <div class="bg-secondary-dark-blue p-4 rounded-xl mb-4 flex flex-col md:flex-row gap-4 md:items-end border border-gray-300">
        <div class="mr-8">
          <p class="font-bold text-lg">Class: <%= @lesson.form.name %></p>
          <p>Scheduled for: <%= @lesson.time.strftime("%d %b %Y") %></p>
          <%= link_to "View mission", static_missions_path(@lesson_template.id), class: "underline text-light-blue text-sm" %>
        </div>

        <%= render ButtonComponent::Base.new(url: "/s/homework/new?lesson=#{@lesson.id}") do %>
          <div class="text-left">
            <p class="font-bold">Set independent work</p>
            <p class="text-sm">Create an assignment for this lesson</p>
          </div>
        <% end %>

        <% if @current_user&.school.show_mark_book %>
          <%= render ButtonComponent::Base.new(url: school_mark_book_path(@lesson.form.id, lesson_id: @lesson.id), variant: :purple) do %>
            <div class="text-left">
              <p class="font-bold">Mark Book</p>
              <p class="text-sm">View class marks for this lesson</p>
            </div>
          <% end %>
        <% end %>
      </div>
    <% end %>
    <!-- MARK: TEMPLATE VERSION -->
    <%- if @template_versions.present? && @current_user&.can_create_lessons? %>
      <div class="bg-secondary-dark-blue p-4 rounded-xl mb-4 flex gap-8 flex-wrap justify-between items-start">
        <%- if @lesson_template.user_generated || @lesson_template.source_template %>
          <div class="space-y-2 text-sm min-w-[250px] border border-gray-700 p-4 rounded-xl text-white">
            <%if @lesson_template.user_generated%>
              <div>
                <p>This is a custom lesson made within your school by <%= @lesson_template.user&.name || 'Developing Experts' %></p>
              </div>
            <%end%>
            <%if @lesson_template.source_template %>
              <div>
                <p>
                  This lesson was copied from an existing Developing Experts lesson.<br>
                  <%= link_to static_missions_path(@lesson_template.source_template, "no-redirect": 1) do %>
                    Click <u>here</u> to view the original lesson.
                  <% end %>
                </p>
              </div>
            <%end%>
            <%if @lesson_template.user_generated %>
              <div>
                <%if @lesson_template.available%>
                  <p>This lesson is published to users in your school</p>
                <%else%>
                  <p>This lesson is not published</p>
                <%end%>
              </div>
            <%end%>
          </div>
        <% end %>
        <div class="flex-1 min-w-[250px]">
          <% if @template_versions.size > 1 %>
            <div class="mb-4">
              <select onchange="handleViewSelectedLesson(event)">
                <% @template_versions.map do |template| %>
                  <option
                    value="<%= template[:id] %>"
                    <%= "selected" if template[:id] == @lesson_template.id %>
                  >
                    <%= template[:name] %> (<%= template.dig(:user, :name) || "Developing Experts" %>)
                  </option>
                <% end %>
              </select>
            </div>
          <%end%>
          <div class="flex gap-4 items-center justify-between text-white">
            <% if !@lesson_template.available %>
              <div>
                <h4 class="text-lg nova text-white">A lesson must be published before it can be set as the default.</h4>
              </div>
            <% elsif @lesson_template.is_default || @original_is_default %>
              <div>
                <h4 class="text-lg nova text-white">This is the default lesson version</h4>
              </div>
            <% else %>
              <button onclick="handleSetAsDefault(event)" class="btn btn-base btn-cyan">Set as the default lesson</button>
            <% end %>
            <% if !@lesson_template.user_generated && !@lesson_template.viewable_only %>
              <% if !@lesson_template.user_generated && !@lesson_template.viewable_only %>
                <%= render ModalComponent.new(title: "Create Lesson Copy") do |modal| %>
                  <% modal.with_trigger do %>
                    <button class="btn btn-base btn-white">Create an editable copy</button>
                  <% end %>
                  <p class="mb-4">You are about to create a copy of the original lesson.</p>
                  <p>Once copied, you can edit the lesson, updating the name, plan and presentation for it.</p>
                  <% modal.with_footer do %>
                    <% if @current_user.beta_feature_enabled?(:new_lesson_editor) %>
                      <%= render ButtonComponent::Base.new(
                        text: "Confirm",
                        url: new_from_templates_school_lesson_editing_index_path(ids: [@lesson_template.id]),
                      ) %>
                    <% else %>
                      <%= render ButtonComponent::Base.new(
                        text: "Confirm",
                        url: "/s/lesson-templates/copy?#{{ id: @lesson_template.id, query: @lesson_template.name }.to_query}",
                      ) %>
                    <% end %>
                    <%= render ButtonComponent::Cancel.new(
                      onclick: "this.closest('dialog').close();"
                    ) %>
                  <% end %>
                <% end %>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
    <%= render "static/lesson_templates/quiz_links",
        rocket_quiz_url: @lesson_template&.keywords.present? ? static_missions_rocket_word_quiz_path(@lesson_template, return_to: request.fullpath) : nil,
        assessment_quiz_url: @lesson_template&.quip_quiz.present? ? static_missions_assessment_quiz_path(@lesson_template, return_to: request.fullpath): nil,
        local_word_search_url: @lesson_template&.keywords.present? ? static_missions_word_search_path(@lesson_template, return_to: request.fullpath) : nil,
        hide_when_empty: true
    %>
    <div class="w-full grid md:grid-cols-[2fr_1fr] items-start gap-4">
      <!-- MARK: LEFT SIDE -->
      <div>
        <%- if @current_user && @current_user.subscribed_to_service?(:ai) %>
          <div class="mb-4"> 
            <%= render 'static/lesson_templates/ai_lesson_assistant' %>
          </div>
        <% end %>
        <!-- LESSON RESOURCES -->
        <div class="border border-gray-700 p-4 rounded-xl mb-4">
          <h2 class="text-white mb-4 text-xl nova">Lesson Resources</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Lesson Plan -->
            <div class="bg-secondary-dark-blue p-4 rounded-xl">
              <h4 class="text-white text-lg nova">Lesson Plan</h4>
              <p class="text-white mb-2">View and print the lesson plan</p>
              <%= render ButtonComponent::Base.new(text: "View", url: static_missions_plan_path(@lesson_template.id, {return_to: request.fullpath}), variant: :white) %>
            </div>

            <!-- Downloads -->
            <div class="bg-secondary-dark-blue p-4 rounded-xl">
              <h4 class="text-white text-lg nova">Downloads</h4>
              <p class="text-white mb-2">
                <%= @current_user.present? ? "Save files related to this lesson" : "Sign up to access lesson files" %>
              </p>
              <div class="flex flex-col gap-2">
                <% @lesson_template.documents.each do |document| %>
                  <% if @current_user.present? %>
                      <%= link_to document.fileboy_url({ user_id: @current_user&.id }), class: "btn btn-white btn-base w-full flex items-center gap-2", target: "_blank", download: true do %>
                          <i class="fa-duotone fa-solid fa-download"></i>
                          <%= document.name %>
                      <% end %>
                    <% else %>
                      <button class="btn btn-white btn-base w-full flex items-center gap-2 btn-disabled" disabled>
                        <i class="fa-duotone fa-solid fa-download"></i>
                        <%= document.name %>
                      </button>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="rounded-xl p-6 border border-gray-700 mb-4">
          <!-- MARK: KEYWORDS -->
          <div>
            <div class="flex justify-between flex-wrap gap-2">
              <h2 class="text-white text-xl nova">Keywords</h2>
              <button onclick="handlePrintKeywords()" class="btn btn-flat-white btn-sm">Print</button>
            </div>
            <p class="text-white mb-6">Words and meanings to learn</p>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
              <% @lesson_template.keywords.order(weight: :asc).map do |keyword| %>
                <%= render CardComponent.new(
                  title: keyword.name,
                  body: keyword.body,
                  image_background_fileboy_id: keyword.fileboy_image_id)
                %>
              <%end%>
            </div>
          </div>
        </div>
        <% campaign_units = @lesson_template.campaign_units.where(show_on_unit_page: true) %>
        <% if campaign_units.any? %>
          <div class="mb-4">
            <%= render CampaignUnitPanelComponent.new(campaign_units: campaign_units, user: @current_user)%>
          </div>
        <%end%>
        <%- if !@lesson_template.user_generated && @lesson_template.authors.present? %>
          <div class="border border-gray-700 p-4 rounded-xl max-w-4xl">
            <h3 class="mb-4 text-xl text-white nova">This lesson has been written by</h3>
            <%= render AuthorsPanelComponent.new(authors: @lesson_template.authors) %>
          </div>
        <% end %>
        <!-- END OF LEFT SIDE -->
      </div>

      <!-- MARK: RIGHT SIDE -->
      
      <!-- REQUIRED RESOURCES -->
      <div>

        <%= render "static/lesson_templates/required_resources", lesson_plan_layout: @lesson_template.lesson_plan_layout, new_lesson_plan_resources: @lesson_template.new_lesson_plan_resources, plan_activities: @lesson_template.plan_activities %>
        <%= render "static/lesson_templates/links", links: @lesson_template.links %>
        <div class="space-y-4">
          <% @videos.map do |video| %>
            <%= render "static/lesson_templates/video", video: video %>
          <% end %>
        </div>
        <% unless @current_user&.pupil? %>
          <!-- Exemplar Work -->
          <div class="text-white border border-color-white rounded-xl p-4 bg-secondary-dark-blue mt-4">
            <div class="mb-4">
              <h3 class="text-xl nova">Exemplar Work</h3>
              <p>View some great examples of student work from this unit</p>
            </div>
            <%- if @lesson_template.exemplar_works.approved.count > 0 %>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <%- @lesson_template.exemplar_works.approved.each do |exemplar_work| %>
                  <%= render ModalComponent.new(title: exemplar_work.display_name || exemplar_work.title) do |modal| %>
                    <% modal.with_trigger do %>
                      <div
                        class="cursor-pointer rounded-xl bg-no-repeat bg-center bg-contain w-full aspect-1 hover:shadow-lg"
                        style="background-image: url('<%= exemplar_work.fileboy_url %>')"
                      ></div>
                    <% end %>

                    <div class="flex flex-col gap-4">
                      <img src="<%= exemplar_work.fileboy_url %>" alt="<%= exemplar_work.display_name || exemplar_work.title %>" class="w-full mx-auto" />
                    </div>

                    <% modal.with_footer do %>
                      <%= render ButtonComponent::Base.new(
                        text: "Download",
                        url: exemplar_work.fileboy_url,
                        left_icon: "fa-duotone fa-solid fa-download",
                      ) %>
                      <%= render ButtonComponent::Cancel.new(
                        onclick: "this.closest('dialog').close();"
                      ) %>
                    <% end %>
                  <% end %>
                <% end %>
              </div>
            <%- else %>
              <p class="text-slate-300 italic">No exemplar works have been uploaded for this lesson yet.</p>
            <%- end %>
            <%if @current_user&.teacher?%>
              <%= link_to new_school_exemplar_work_path(lesson: @lesson_template.id), class: "btn btn-cyan btn-base gap-2 items-center mt-4" do %>
                <i class="fa-duotone fa-solid fa-upload"></i>
                Upload Exemplar Works
              <% end %>
            <%end%>
          </div>
        <%end%>
        <% if @career_paths.any? %>
          <div class="border border-gray-700 p-4 rounded-xl max-w-4xl mt-4">
            <h3 class="mb-4 text-xl text-white nova">Careers related to this lesson</h3>
            <%= render 'static/lesson_templates/career_paths', career_paths: @career_paths %>
          </div>
        <% end %>
      </div>
      <!-- END OF RIGHT SIDE -->
    </div>
  </div>
</div>

<%= render "static/lesson_templates/keyword_printable" %>

<script>
  // SELECT LIST - toggle between related lessons
  function handleViewSelectedLesson(event) {
    const currentId = "<%= @lesson_template.id %>"
    const selectedId = event.target.selectedOptions[0].value
    if(currentId === selectedId) {
      return
    }
    window.location.href = `<%= static_missions_path({id: ""}) %>${selectedId}?no-redirect=1`
  }
</script>
<script>
  // Template duplication & defaulting
  async function handleSetAsDefault() {
    const canSetAsDefault = <%= @lesson_template.available %>;
    if(!canSetAsDefault) {
      return;
    }
    const response = await fetch(
      "<%= set_as_default_v2_user_lesson_template_path(@lesson_template) %>",
      { headers: { "content-type": "application/json" }, method: "POST" }
    )
    window.location.reload()
  }
</script>
