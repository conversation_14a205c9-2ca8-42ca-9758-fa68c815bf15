<% content_for :title, "Careers | Developing Experts" %>

<div class="min-h-screen bg-gray-900">
  <!-- Banner Section -->
  <div class="relative overflow-hidden bg-gradient-to-br from-purple-900 via-blue-900 to-cyan-900 py-16">
    <!-- Subtle Background Elements -->
    <div class="absolute inset-0">
      <!-- Grid <PERSON> -->
      <div class="absolute inset-0 opacity-5">
        <div class="grid grid-cols-12 gap-4 h-full">
          <% (1..12).each do |i| %>
            <div class="border-r border-white/20"></div>
          <% end %>
        </div>
      </div>
      
      <!-- Static Floating Icons -->
      <div class="absolute top-16 left-1/3 text-cyan-300/30 text-2xl">
        <i class="fas fa-rocket"></i>
      </div>
      <div class="absolute bottom-24 right-1/3 text-purple-300/30 text-3xl">
        <i class="fas fa-brain"></i>
      </div>
      <div class="absolute top-1/2 right-16 text-blue-300/30 text-2xl">
        <i class="fas fa-star"></i>
      </div>
    </div>
    
    <div class="relative max-w-screen-xl mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- Left Content -->
        <div>
          <!-- Breadcrumbs -->
          <div class="mb-4">
            <%= render_breadcrumbs(dark_text: false) %>
          </div>
          <!-- Animated Title -->
          <div class="mb-6">
            <h1 class="text-5xl md:text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 animate-pulse">
              Your Future
            </h1>
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-2 transform transition-transform duration-300">
              Starts <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-400 animate-pulse">Here</span>
            </h2>
          </div>
          
          <!-- Subtitle -->
          <p class="text-xl text-gray-200 mb-8 leading-relaxed">
            🚀 Discover mind-blowing career paths • 🧠 Learn from industry experts • ✨ Plan your epic journey to success
          </p>
          
          <!-- Mega CTA -->
          <div class="mb-8">
            <%= link_to "/careers/generate", 
                class: "group relative inline-flex items-center px-10 py-5 bg-gradient-to-r from-cyan-500 via-blue-600 to-purple-600 hover:from-cyan-400 hover:via-blue-500 hover:to-purple-500 text-white text-lg font-black rounded-2xl transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/25 transform" do %>
              <!-- Glow Effect -->
              <div class="absolute inset-0 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl blur-lg opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
              
              <div class="relative flex items-center">
                <div class="mr-3 p-2 bg-white/20 rounded-full transition-transform duration-300">
                  <i class="fas fa-robot text-xl"></i>
                </div>
                <div class="text-left">
                  <div class="text-lg font-black">Use DE AI to find career info</div>
                  <div class="text-sm opacity-90 font-medium">Powered by next-gen AI 🤖</div>
                </div>
                <i class="fas fa-arrow-right ml-3 text-lg group-hover:translate-x-2 transition-transform duration-300"></i>
              </div>
            <% end %>
          </div>
          
          <!-- Secondary Actions -->
          <div class="flex flex-wrap gap-4">
            <%= link_to "/careers/job-families", 
                class: "group relative inline-flex items-center px-6 py-3 bg-white/5 backdrop-blur-md border-2 border-white/20 text-white font-bold rounded-xl hover:bg-white/10 hover:border-purple-400/50 transition-all duration-300 hover:scale-105" do %>
              <div class="absolute inset-0 bg-gradient-to-r from-purple-600/0 to-purple-600/0 group-hover:from-purple-600/10 group-hover:to-blue-600/10 rounded-xl transition-all duration-300"></div>
              <i class="fas fa-briefcase mr-2 text-purple-300 transition-transform duration-300"></i>
              <span class="relative">Browse Job Families</span>
            <% end %>
            
            <%= link_to "/careers/search", 
                class: "group relative inline-flex items-center px-6 py-3 bg-white/5 backdrop-blur-md border-2 border-white/20 text-white font-bold rounded-xl hover:bg-white/10 hover:border-cyan-400/50 transition-all duration-300 hover:scale-105" do %>
              <div class="absolute inset-0 bg-gradient-to-r from-cyan-600/0 to-cyan-600/0 group-hover:from-cyan-600/10 group-hover:to-blue-600/10 rounded-xl transition-all duration-300"></div>
              <i class="fas fa-search mr-2 text-cyan-300 group-hover:scale-110 transition-transform duration-300"></i>
              <span class="relative">Search Careers</span>
            <% end %>
          </div>
        </div>
        
        <!-- Right Content - Hero Image -->
        <div class="relative">
          <div class="relative group">
            <!-- Main Image -->
            <div class="relative overflow-hidden rounded-2xl transform group-hover:scale-105 transition-transform duration-500">
              <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop&auto=format" 
                   alt="Students collaborating on future careers" 
                   class="w-full h-80 object-cover">
              
              <!-- Gradient Overlay -->
              <div class="absolute inset-0 bg-gradient-to-tr from-purple-600/20 via-transparent to-cyan-400/20"></div>
              
              <!-- Floating Stats Cards -->
              <div class="absolute top-4 right-4 bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20">
                <div class="text-2xl font-black text-cyan-300 text-center"><i class="fas fa-infinity"></i></div>
                <div class="text-xs text-white font-semibold">Careers</div>
              </div>
              
              <div class="absolute bottom-4 left-4 bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20">
                <div class="text-2xl font-black text-purple-300">30+</div>
                <div class="text-xs text-white font-semibold">Job Families</div>
              </div>
            </div>
            
            <!-- Decorative Elements -->
            <div class="absolute -top-2 -right-2 w-6 h-6 bg-cyan-400 rounded-full animate-pulse"></div>
            <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-purple-400 rounded-full animate-pulse delay-500"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-screen-xl mx-auto px-4 py-12">
    
    <!-- Featured Job Families -->
    <% if @job_families&.any? %>
      <section class="mb-16">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-3xl font-bold text-white">Featured Job Families</h2>
          <%= link_to "/careers/job-families", 
              class: "inline-flex items-center px-4 py-2 bg-purple-600/20 hover:bg-purple-600/30 text-purple-400 hover:text-purple-300 font-medium rounded-lg transition-colors" do %>
            View All
            <i class="fas fa-arrow-right ml-2"></i>
          <% end %>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <% @job_families.limit(8).each do |job_family| %>
            <%= render 'static/job_families/job_family_card', job_family: job_family %>
          <% end %>
        </div>
      </section>
    <% end %>

    <!-- Favourite Careers (Only show if user is signed in and has favourites) -->
    <% if @current_user.present? && @current_user.user_career_paths.where(is_favourite: true).any? %>
      <section class="mb-16">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-3xl font-bold text-white">Your Favourite Careers</h2>
          <%= link_to "/careers/favourites", 
              class: "inline-flex items-center px-4 py-2 bg-pink-600/20 hover:bg-pink-600/30 text-pink-400 hover:text-pink-300 font-medium rounded-lg transition-colors" do %>
            View All
            <i class="fas fa-arrow-right ml-2"></i>
          <% end %>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <% @current_user.user_career_paths.where(is_favourite: true).includes(career_path: [:job_family, :career]).limit(8).each do |user_career_path| %>
            <% career = user_career_path.career_path %>
            <%= render 'static/career_builder/career_card', career: career, is_favourite: true %>
          <% end %>
        </div>
      </section>
    <% end %>

    <!-- Featured Careers -->
    <% if @careers&.any? %>
      <section class="mb-16">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-3xl font-bold text-white">Featured Careers</h2>
          <%= link_to "/careers/search", 
              class: "inline-flex items-center px-4 py-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 hover:text-blue-300 font-medium rounded-lg transition-colors" do %>
            View All
            <i class="fas fa-arrow-right ml-2"></i>
          <% end %>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <% @careers.last(8).each do |career| %>
            <%= render 'static/career_builder/career_card', career: career %>
          <% end %>
        </div>
      </section>
    <% end %>

    <!-- Call to Action Section -->
    <section class="text-center py-16">
      <div class="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600">
        <h2 class="text-3xl font-bold text-white mb-4">Ready to Explore Your Future?</h2>
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Use our AI-powered career discovery tool to find personalised career paths based on your interests and goals.
        </p>
        <%= link_to "/careers/generate", 
            class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white text-lg font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl" do %>
          <i class="fas fa-robot mr-3 text-xl"></i>
          Get Started with DE AI
        <% end %>
      </div>
    </section>

  </div>
</div>