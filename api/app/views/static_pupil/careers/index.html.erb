<% content_for :title, "Careers" %>
<div class="max-w-screen-lg mx-auto py-16 px-4 md:px-12">
  <div class="mb-16">
    <span class="flex items-end text-end  mb-2">
      <%= image_tag("pupil/career_page_icon.png", class: "h-16 w-16 my-auto mr-3") %>
      <h1 class="text-4xl pb-2">Careers</h1>
    </span>
    <p class="max-w-lg">Explore our STEM-related careers library for jobs in energy, digital skills,
    rail, construction, creative industries, life sciences, aerospace and much more...</p>
  </div>

  <% if @de_resources.present? %>
    <div class="mb-12">
      <h3 class="text-xl mb-4">Developing Experts career resources</h3>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <% if @current_user.beta_feature_enabled?(:september_1) %>
          <%= render CardComponent.new(
            title: "Career Zone",
            body: "🚀 Discover mind-blowing career paths • 🧠 Learn from industry experts • ✨ Plan your epic journey to success",
            image_background_img: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop&auto=format",
            path: careers_index_path,
          ) %>
        <% end %>
        <% @de_resources.each do |de_resource| %>
          <%= render CardComponent.new(
              title: de_resource[:title],
              body: de_resource[:body],
              image_fileboy_id: de_resource[:image_fileboy_id],
              onclick: de_resource[:path].starts_with?("http") ? "handleGenericExternalLinkConfirmation(this,'#{de_resource[:path]}')" : nil,
              path: !de_resource[:path].starts_with?("http") ? de_resource[:path] : nil
            ) %>
        <% end %>
      </div>
    </div>
  <% end %>

  <% if @partner_resources.present? %>
    <div>
      <h3 class="text-xl mb-4">Partner resources</h3>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <% @partner_resources.each do |partner_resource| %>
          <%= render CardComponent.new(
              title: partner_resource[:title],
              body: partner_resource[:body],
              image_fileboy_id: partner_resource[:image_fileboy_id],
              onclick: partner_resource[:path].starts_with?("http") ? "handleGenericExternalLinkConfirmation(this,'#{partner_resource[:path]}')" : nil,
              path: !partner_resource[:path].starts_with?("http") ? partner_resource[:path] : nil
            ) %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
<%= render 'shared/external_link_confirmation_modal' %>
