<%# DASHBOARD %>
<% content_for :title, "Dashboard" %>
<%= javascript_include_tag "scripts/field-select", type: "module" %>
<%= javascript_include_tag "nice-select", type: "module" %>

<div class="px-4 md:px-8 py-8 max-w-screen-xl mx-auto nova-headings">
  <!-- MARK: GREETING -->
  <div class="flex-col sm:flex-row flex gap-x-4 mb-4 justify-between">
    <h1 class="text-2xl font-bold flex items-center gap-2">
      <%= image_tag 'sun_svg_blog_banner.svg', class: 'inline-block w-12 h-12' %>
      <span class="hidden md:block"><%= "#{@greeting}, #{@current_user.name}" %></span>
      <span class="md:hidden"><%= "#{@greeting}" %></span>
    </h1>
    <div class="flex items-center gap-3">
      <% if @current_user.beta_feature_enabled?(:teacher_dashboard_tour) %>
        <%= render GuidedTourComponent.new(button_text: "Start Tour", page: "dashboard") %>
      <% end %>
      <%- if @whats_new.any? %>
        <button onclick="handleShowWhatsNew()" class="btn btn-base btn-purple btn-sm">
          <i class="fas fa-sparkles mr-1"></i> What's New
        </button>
      <%- end %>
      <%= render HelpButtonComponent.new(
        title: "Welcome to Developing Experts",
        body: 'Learn how to navigate through and use your school dashboard.',
        id: 5339
      ) %>
      <div data-details-panel-show class="hidden w-max">
        <button onclick="handleToggleDetailsPanel()" class="underline nova text-sm h-full">
          Subscription details
        </button>
      </div>
    </div>
  </div>
  <!-- MARK: DETAILS -->
  <% school = @current_user.school %>
  <div data-details-panel id="details-container" class="mb-8">
    <div class="bg-white bg-opacity-10 p-4 rounded-lg">
      <div class="flex gap-4 w-full mb-2">
        <div class="min-w-14 min-h-14 text-center">
          <% if school.fileboy_image_id.present? %>
            <img src="https://www.developingexperts.com/file-cdn/images/get/<%= school.fileboy_image_id %>?transform=resize:_x50;format:webp;quality:75" class="inline-block">
          <% end %>
        </div>
        <div class="grow self-center">
          <h3 class="font-bold text-xl"><%= school.name %></h3>
        </div>
        <button onclick="handleToggleDetailsPanel()" class="underline nova text-sm h-full">
          Hide panel
        </button>
      </div>
      <div class="grid gap-4" style="grid-template-columns: repeat(auto-fit, minmax(220px,1fr))">
        <div class="bg-white bg-opacity-10 p-4 rounded-lg">
          <div class="h-full flex justify-between gap-2">
            <div>
              <h3 class="mb-2">Science Leads</h3>
              <div class="flex justify flex-between items-end">
                <div>
                  <% school.science_leaders.map do |leader| %>
                    <p class="break-all"><%= leader.name %></p>
                  <% end %>
                  <%- if school.science_leaders.empty? %>
                    <p class="text-sm opacity-50">There are no geography leaders currently set.</p>
                  <%- end %>
                </div>
              </div>
            </div>
            <% if @current_user.is_school_admin %>
              <div class="h-full flex items-end">
                <%= render ModalComponent.new(title: "Manage Science Leaders") do |modal| %>
                  <% modal.with_trigger do %>
                    <button><i class="fas fa-pen-to-square opacity-80"></i></button>
                  <% end %>
                  <div>
                    <%= form_with model: @current_user.school, url: school_update_science_leaders_path(@current_user.school), method: :put, local: true do |form| %>
                      <div class="mb-4">
                        <%= form.label :science_leader_ids, "Science Leaders" %>
                        <%= form.select :science_leader_ids, options_for_select(@school.teachers.pluck(:email, :id), @school.science_leader_ids), {}, { multiple: true, class: "field field-select" } %>
                      </div>
                      <div class="flex gap-2">
                        <button class="btn btn-base btn-flat-white" type="button" data-action="click->modal#close">
                          Cancel
                        </button>
                        <%= form.submit "Save", data: { disable_with: 'Processing...' }, class: "btn btn-base btn-purple" %>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
        <div class="bg-white bg-opacity-10 p-4 rounded-lg">
          <div class="h-full flex justify-between gap-2">
            <div>
              <h3 class="mb-2">Geography Lead</h3>
              <div class="flex justify flex-between items-end">
                <div>
                  <% school.geography_leaders.map do |leader| %>
                    <p class="break-all"><%= leader.name %></p>
                  <% end %>
                  <%- if school.geography_leaders.empty? %>
                    <p class="text-sm opacity-50">There are no geography leaders currently set.</p>
                  <%- end %>
                </div>
              </div>
            </div>
            <% if @current_user.is_school_admin %>
              <div class="h-full flex items-end">
                <%= render ModalComponent.new(title: "Manage Geography Leaders") do |modal| %>
                  <% modal.with_trigger do %>
                    <button><i class="fas fa-pen-to-square opacity-80"></i></button>
                  <% end %>
                  <div>
                    <%= form_with model: @current_user.school, url: school_update_geography_leaders_path(@current_user.school), method: :put, local: true do |form| %>
                      <div class="mb-4">
                        <%= form.label :geography_leader_ids, "Geography Leaders" %>
                        <%= form.select :geography_leader_ids, options_for_select(@school.teachers.pluck(:email, :id), @school.geography_leader_ids), {}, { multiple: true, class: "field field-select" } %>
                      </div>
                      <div class="flex gap-2">
                        <button class="btn btn-base btn-flat-white" type="button" data-action="click->modal#close">
                          Cancel
                        </button>
                        <%= form.submit "Save", data: { disable_with: 'Processing...' }, class: "btn btn-base btn-purple" %>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
        <div class="bg-white bg-opacity-10 p-4 rounded-lg">
          <div class="h-full flex justify-between gap-2">
            <div>
              <h3 class="mb-2">Finance Contact</h3>
              <div class="flex justify flex-between items-end">
              <div>
                <% if school.finance_name.present? && school.finance_email.present? %>
                  <p class="text-sm break-all"><%= school.finance_name %></p>
                  <p class="text-sm break-all"><%= school.finance_email %></p>
                <% else %>
                  <p class="text-sm opacity-50">No finance contact has been set.</p>
                <% end %>
              </div>
              </div>
            </div>
            <% if @current_user.is_school_admin %>
              <div class="h-full flex items-end">
                <%= render ModalComponent.new(title: "Manage Finance Contact") do |modal| %>
                  <% modal.with_trigger do %>
                    <button><i class="fas fa-pen-to-square opacity-80"></i></button>
                  <% end %>
                  <div>
                    <%= form_with model: @current_user.school, url: school_update_finance_contact_path(@current_user.school), method: :put, local: true do |form| %>
                      <div class="mb-2">
                        <%= form.label :finance_name, "Name" %>
                        <%= form.text_field :finance_name %>
                      </div>
                      <div class="mb-4">
                        <%= form.label :finance_email, "Email" %>
                        <%= form.email_field :finance_email %>
                      </div>
                      <div class="flex gap-2">
                        <button class="btn btn-base btn-flat-white" type="button" data-action="click->modal#close">
                          Cancel
                        </button>
                        <%= form.submit "Save", data: { disable_with: 'Processing...' }, class: "btn btn-base btn-purple" %>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
        <div id="subscription-details" class="bg-white bg-opacity-10 p-4 rounded-lg relative">
          <% if @current_user.school&.in_trial? %>
            <div class="gap-2 mb-4 w-full">
              <p class="text-white text-blue-100 font-bold mb-4">
                Your school is currently in a trial period. Upgrade to continue access to all features.
              </p>
              <a href="<%= lesson_subscription_path %>" class="btn btn-base btn-cyan">
                <span>Manage Subscription</span>
                <i class="fas fa-arrow-right ml-2"></i>
              </a>
            </div>
          <% else %>
            <div class="h-full">
              <div>
                <h3 class="mb-2">Subscriptions</h3>
                  <div class="mb-2 w-full">
                    <%- [:science, :geography, :ai].each do |service| %>
                      <div class="flex items-center justify-between mb-1 w-full">
                      <span class="font-medium flex-1"><%= service.to_s.humanize %></span>
                      <% if @current_user.subscribed_to_service?(service) %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Active
                        </span>
                      <% else %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Inactive
                        </span>
                      <% end %>
                      </div>
                    <% end %>
                  </div>
                <div>
                    <p class="text-sm font-bold overflow-hidden overflow-ellipsis max-w-48 min-h-6">
                      <% if @current_user.subscription&.renewal_date %>
                        Renews <%= @current_user.subscription&.renewal_date.strftime("%d %b %Y") %>
                      <% elsif school.renewal_month && school.renewal_month != 'renewal_month' %>
                        Renews <%= school.renewal_month.titleize %>
                      <%end%>
                    </p>
                </div>
              </div>
              <% if @current_user.is_school_admin %>
                <div class="absolute bottom-4 right-4">
                  <a href="<%= lesson_subscription_path %>"><i class="fas fa-pen-to-square opacity-80"></i></a>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="grid gap-8 grid-cols-1 lg:grid-cols-[1fr_250px] xl:grid-cols-[1fr_320px]">
      <div class="grow min-w-72 space-y-8">
        <!-- MARK: QUICK LINKS -->
        <div id="quick-links-container">
          <div class="flex gap-4 items-center mb-2">
            <h3 class="text-lg">Quick links</h3>
            <%= render ModalComponent.new(title: "Manage quick links") do |modal| %>
              <% modal.with_trigger do %>
                <button class="underline text-xs">Customise</button>
              <% end %>
              <div id="quick-link-checkboxes" class="flex flex-col gap-2">
                <% @quick_links.each do |link| %>
                  <div class="flex gap-2 items-center">
                    <input id="quick-link-<%= link[:key] %>" value="<%= link[:key] %>" type="checkbox">
                    <label for="quick-link-<%= link[:key] %>"><%= link[:label] %></label>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
          <div data-quick-links class="grid grid-cols-[repeat(auto-fill,minmax(180px,1fr))] gap-4">
          </div>
        </div>
        <!-- MARK: DASHBOARD NOTIFICATIONS -->
        <%= render "dashboard_notification" %>
        
        <!-- MARK: AI LESSON/CAREERS BUILDER PANEL -->
        <div id="ai-tools-container" class="bg-gradient-to-r from-purple-600 to-indigo-700 rounded-lg shadow-xl p-6 mt-8 text-white">
          <div class="flex flex-col gap-6 md:flex-row md:justify-between md:items-center">
            <div class="max-w-xl">
              <h3 class="text-2xl font-bold mb-3">AI Lesson & Careers Builder 🌟</h3>
              <p class="mb-4 italic font-semibold">Best ‘AI Product’ & ‘AI Edtech’ Product – National AI Awards</p>
              <blockquote class="border-l-4 border-white pl-4 italic mb-4">"Best use case of AI the judges have seen!"</blockquote>
              <p class="mb-4">
                Create stunning, sequenced lessons with interactive multimedia, engaging quizzes, and detailed lesson plans—tailored to any curriculum and age group. Enhance teacher mastery, clarify misconceptions, and connect learning directly to exciting career pathways, apprenticeships, qualifications, and progression opportunities.
              </p>
              <a href="/ai" class="btn btn-base btn-white transition-transform hover:scale-105 shadow">✨ Try Now</a>
            </div>
            <div class="flex flex-col gap-4 items-center justify-center min-w-[140px]">
              <%= image_tag "homepage_awards_bett_nominee.png", class: "max-h-24 object-contain rounded-lg shadow-md" %>
              <%= image_tag "homepage_awards_ai_product.png", class: "max-h-24 object-contain rounded-lg shadow-md" %>
            </div>
          </div>
        </div>
        <%- unless @live_lessons.empty? %>
          <div class="mb-8">
            <div class="grid gap-4 grid-cols-1">
              <%- @live_lessons.each do |live_lesson| %>
                <a href="/school/youtube/live" class="block w-full bg-white/10 border border-gray-200 rounded-xl shadow overflow-hidden hover:bg-white/20 transition">
                  <div class="grid grid-cols-1 md:grid-cols-[1fr_2fr]">
                    <div class="relative h-[180px] md:h-full w-full bg-black">
                      <% if live_lesson.external? %>
                        <%= image_tag live_lesson.thumbnail_url || "default_live_thumbnail.jpg", class: "w-full h-full object-cover" %>
                      <% else %>
                        <iframe 
                          src="<%= live_lesson.url %>?autoplay=1&mute=1" 
                          class="absolute inset-0 w-full h-full" 
                          frameborder="0" 
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                          allowfullscreen>
                        </iframe>
                      <% end %>
                    </div>
                    <div class="p-6 flex flex-col">
                      <div class="bg-de-brand text-white font-bold px-3 py-1 rounded-full w-fit mb-3 animate-pulse">
                        We are live!
                      </div>
                      <h3 class="text-xl font-bold mb-2">Come on over and check out our live lesson:</h3>
                      <p class="text-lg mb-1"><%= live_lesson.title %></p>
                      <p class="text-gray-400">
                        <i class="far fa-clock mr-1"></i>
                        Started at <%= live_lesson.start_time.strftime("%l:%M%p") %> on <%= live_lesson.start_time.strftime("%d/%m/%Y") %>
                      </p>
                      <div class="mt-auto pt-4">
                        <div class="btn btn-red inline-flex items-center">
                          Join Now
                          <svg class="ml-2 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 12 12">
                            <path d="M10.293 1.293a1 1 0 0 1 1.414 1.414l-7 7a1 1 0 0 1-1.414 0l-3-3a1 1 0 0 1 1.414-1.414L4 7.586l6.293-6.293z"/>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </a>
              <%- end %>
            </div>
          </div>
        <%- end %>
        <!-- MARK: LESSONS -->
        <div id="lessons-container" class="space-y-8">
          <% colours = ['bg-red-400', 'bg-emerald-500', 'bg-blue-500', 'bg-purple-500'] %>
          <% badge_index = 0 %>
          <% @current_user.forms.each do |form| %>
            <% form_units = form.form_units.active %>
            <% if form_units.count == 0 %>
              <div>
                <div class="flex gap-4 items-center mb-4">
                  <a href="<%= "/school/classes/#{form.id}/edit" %>">
                    <h3 class="text-xl underline"><%= form.name %></h3>
                  </a>
                </div>
                <div class="w-full bg-white/10 rounded-lg p-5 h-full">
                  <h3 class="text-lg">No upcoming lessons have been set for this class</h3>
                  <p class="mb-4">Add lessons from the library or through the lessons page on the class to have the active unit displayed</p>
                  <%= render ButtonComponent::Base.new(text: "Add lessons to this class", variant: :primary, url: school_add_lessons_path(form_id: form.id)) %>
                </div>
              </div>
            <% else %>
              <% form_units.includes([new_library_unit: [:year, :subject]]).each do |form_unit| %>
                <% next unless form_unit.new_library_unit&.subject %>
                <% subject = form_unit.new_library_unit.subject %>
                <% unit_path = "/unit-library/units/#{form_unit.new_library_unit_id}" %>
                <div>
                  <div class="flex gap-4 items-center mb-4">
                    <a href="<%= "/school/classes/#{form.id}/edit" %>">
                      <h3 class="text-xl underline"><%= form.name %></h3>
                    </a>
                    <h3 class="text-xl flex items-center gap-2"><%= image_tag subject.name =="Science" ? 'atom.svg' : 'globe.svg', class: 'inline-block w-8 h-8 -my-2' %><%= subject.name %></h3>
                  </div>
                  <div class="w-full bg-white border border-gray-200 rounded-xl shadow p-5 h-full text-black gap-4 grid grid-cols-1 md:grid-cols-[300px_1fr]">
                    <a href="<%= unit_path %>" class="min-h-[200px] md:min-h-[342px] h-full w-full overflow-hidden">
                      <div style="background-image: url(https://www.developingexperts.com/file-cdn/images/get/<%= form_unit.fileboy_image_id %>?transform=resize:300x_~fit:cover;format:webp;quality:75);" class=" block rounded-lg h-full w-full overflow-hidden bg-cover bg-center">
                        <div class="p-2">
                          <%= render SponsorIconsComponent.new(campaign_units: form_unit.campaign_units.where(show_on_unit_page: true)) %>
                        </div>
                      </div>
                    </a>
                    <div class="flex flex-col h-full min-w-[200px]">
                      <div>
                        <span class="font-bold text-de-brand rounded mb-2">Current Unit</span>
                      </div>
                      <a href="<%= unit_path %>" class="block">
                        <p class="mb-3 text-2xl font-bold tracking-tight <%= @color %>"><%= form_unit.name %></p>
                      </a>
                      <% form_unit.lesson_lessons.includes(:template).order(time: :asc).each do |lesson| %>
                        <a href="<%= school_lesson_path(lesson) %>" class="mb-1 underline"><%= lesson.name %></a>
                      <% end %>
                      <div class="mt-auto pt-2">
                        <a href="<%= unit_path %>" class="btn btn-text-base inline-flex items-center">
                          View Unit
                          <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                          </svg>
                        </a>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col md:flex-row gap-x-2 mt-2">
                    <%= link_to "/s/homework", class: "flex gap-x-2 transition-all hover:bg-white/10 px-4 py-2 rounded-xl" do %>
                      <% homework_count = form.homeworks_need_marking_for_subject(subject.id).length + Homework.where(lesson_lesson_id: nil).awaiting_marking.length %>
                      <div class="mt-1">
                        <div class="px-2 font-bold <%= colours[badge_index % colours.length] %> rounded-full h-[26px] min-w-[26px] text-center flex items-center justify-center">
                          <p class="<%= "opacity-0" if homework_count == 0 %>"><%= homework_count %></p>
                        </div>
                      </div>
                      <div>
                        <p class="text-lg font-semibold">Independent Learning</p>
                        <% if homework_count > 0 %>
                          <p><%= homework_count %> assignment<%= "s" if homework_count > 1 %> awaiting marks</p>
                        <% else %>
                          <p class="text-gray-500">You have no outstanding tasks</p>
                        <% end %>
                      </div>
                    <% end %>
                    <%= link_to lessons_school_form_path(form), class: "flex gap-x-2 transition-all hover:bg-white/10 px-4 py-2 rounded-xl" do %>
                      <div class="mt-1 mr-1">
                        <p class="px-2 font-bold <%= colours[(badge_index + 1) % colours.length] %> rounded-full h-[26px] min-w-[26px] text-center flex items-center justify-center"><%= form.lessons_for_subject(subject.id).count %></p>
                      </div>
                      <div>
                        <p class="text-lg font-semibold"><%= subject.name %> Lessons</p>
                        <p>View all the lessons</p>
                      </div>
                    <% end %>
                    <% if @school.show_mark_book %>
                      <%= link_to school_mark_book_path(form), class: "flex gap-x-2 transition-all hover:bg-white/10 px-4 py-2 rounded-xl" do %>
                        <div class="mt-1 mr-1">
                          <p class="px-2 font-bold <%= colours[(badge_index + 1) % colours.length] %> rounded-full h-[26px] min-w-[26px] text-center flex items-center justify-center"></p>
                        </div>
                        <div>
                          <p class="text-lg font-semibold">Mark book</p>
                          <p>See pupil progress</p>
                        </div>
                      <% end %>
                    <% end %>
                    <% badge_index += 2 %>
                  </div>
                </div>
              <%end%>
            <%end%>
          <%end%>
        </div>
        <!-- MARK: LEADERBOARDS -->
        <div id="pupil-leaderboards-container">
          <h4 class="text-2xl mb-4">Leaderboards</h4>
          <div class="bg-white bg-opacity-10 rounded-lg p-4 grid grid-cols-1 lg:grid-cols-2 gap-8">
            <%= render 'static_pupil/leaderboard/leaderboard', title: 'Your School', data: @leaderboard_school %>
            <%= render 'static_pupil/leaderboard/leaderboard', title: 'Global', data: @leaderboard_global %>
          </div>
        </div>
        <!-- MARK: SOCIALS -->
        <div id="de-socials-container" class="hidden md:block">
          <div class="text-white flex gap-4 mb-4">
            <div>
              <i class="fab fa-x-twitter text-6xl text-white"></i>
            </div>
            <div>
              <h3 class="text-3xl">DEVELOPING EXPERTS</h3>
              <h4 class="text-2xl">@DevelopExperts</h4>
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 max-w-screen-xl mx-auto gap-2 text-white">
            <%- @social_posts.each do |post| %>
              <%= render CardComponent.new(
                title: "@#{post.name}",
                body: post.body.html_safe,
                image_fileboy_id: post.fileboy_image_ids.any? ? post.fileboy_image_ids.first : nil,
                tags: ["#{distance_of_time_in_words_to_now(post.created_at).gsub('about ', '')} ago"],
                background_color: "bg-blue-800",
                color: "text-white",
              )
              %>
            <%- end %>
          </div>
        </div>
      </div>
      <div class="space-y-8">
        <!-- MARK: PUPIL LOGIN -->
        <div id="pupil-login-container" class="bg-white bg-opacity-10 rounded-lg p-4">
          <h3 class="font-semibold mb-2">View pupil dashboard...</h3>
          <%= form_tag "/pupil/set_pupil", method: :get, class: "mt-4" do %>
            <%= select_tag :pupil_id, options_for_select(@current_user.pupils.pluck(:name, :id), params[:pupil_id]), class: "w-full", include_blank: true, prompt: "Select a student",  data: { nice_select: true, default_options: true }, onchange: "this.form.submit();" %>
          <% end %>
        </div>
        <% if @current_user.beta_feature_enabled?(:aug_18) %>
          <%= link_to school_weekly_digest_index_path, class: "block bg-gradient-to-br from-cyan-500 to-blue-600 opacity-90 rounded-xl p-6 text-white hover:opacity-100 transition-all duration-300 shadow-lg hover:shadow-xl" do %>
            <div class="flex items-center justify-between mb-4">
              <div class="bg-white/20 rounded-full p-3">
                <i class="fas fa-chart-line text-2xl"></i>
              </div>
            </div>
            
            <h3 class="text-xl font-bold mb-2">📊 Weekly Digest</h3>
            <p class="text-indigo-100 text-sm mb-4 leading-relaxed">
              View your teaching activity summary, pupil engagement, and upcoming lessons all in one place.
            </p>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm font-medium">
                View Report
                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
              </div>
            </div>
          <% end %>
        <% end %>
        <!-- MARK: UPDATES -->
        <div id="de-updates-container">
          <h3 class="font-bold text-xl mb-2">Developing Experts Updates</h3>
          <div class="flex flex-col gap-4 mb-6">
            <% @updates.map do |update| %>
              <div class="bg-white bg-opacity-10 rounded-lg p-4">
                <h4 class="mb-4 text-xl"><%= update.name %></h4>
                <%if update.preview_image_url.present?%>
                  <img src="<%= update.preview_image_url %>" alt="<%= update.name %>" class="w-full h-48 object-cover mb-4">
                <%end%>
                <div class="prose prose-invert text-sm">
                  <%= simple_format(update.body) %>
                </div>
              </div>
            <% end %>
          </div>
          <a href="<%= school_motds_path %>" class="underline text text-right block">
            Read more updates
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<%= render ModalComponent.new(title: "Message from the Developing Experts team", external_control: true, id: 'school_memo') do %>
  <p class="whitespace-pre-wrap"><%= @school.memo %></p>
<% end %>
<div id="whats_new_modal" class="hidden fixed inset-0 bg-black/75 z-[1000] flex items-center justify-center p-4 overflow-y-auto" onclick="closeWhatsNewModal()">
  <div class="bg-gray-900 rounded-xl shadow-2xl w-full max-w-4xl my-4 md:my-8" onclick="event.stopPropagation()">
    <!-- Close button -->
    <button onclick="closeWhatsNewModal()" class="absolute top-6 right-6 text-white/75 hover:text-white z-10">
      <i class="fas fa-times text-6xl"></i>
    </button>
    
    <!-- Modal content -->
    <div class="flex flex-col md:flex-row max-h-[80vh] md:max-h-none overflow-hidden">
      <!-- Image section (left on desktop, top on mobile) -->
      <div id="whats-new-image-container" class="md:w-1/2 bg-gradient-to-br from-purple-900 to-indigo-900 flex items-center justify-center p-6 md:p-10">
        <img id="whats-new-image" src="" alt="New feature" class="max-h-60 md:max-h-[80vh] max-w-full object-contain rounded shadow-lg">
      </div>
      
      <!-- Content section (right on desktop, bottom on mobile) -->
      <div class="md:w-1/2 p-6 md:p-8 md:pt-12 relative flex flex-col overflow-y-auto max-h-[50vh] md:max-h-[70vh]">
        <div class="grow">
          <div class="bg-gradient-to-r from-purple-500 to-indigo-400 text-xs font-bold uppercase tracking-wider text-white px-3 py-1 rounded-full mb-3 inline-block shadow-sm">
            What's New
          </div>
          <h2 id="whats-new-title" class="text-3xl font-bold text-white mb-3"></h2>
          <p id="whats-new-date" class="text-xs text-gray-400 mb-4"></p>
          <div id="whats-new-description" class="prose prose-invert text-gray-300"></div>
        </div>
        
        <!-- Navigation buttons -->
        <div class="flex justify-between mt-6 md:mt-8 space-x-4 pt-4 border-t border-gray-800 bg-gray-900 pb-2">
          <button id="whats-new-prev" onclick="navigateWhatsNew(-1)" class="btn btn-base btn-purple-outline">
            <i class="fas fa-chevron-left mr-2"></i>Previous
          </button>
          <div class="flex items-center space-x-1" id="whats-new-indicators">
            <!-- Indicators will be inserted via JavaScript -->
          </div>
          <button id="whats-new-next" onclick="navigateWhatsNew(1)" class="btn btn-base btn-purple">
            Next<i class="fas fa-chevron-right ml-2"></i>
          </button>
        </div>
        <!-- Additional Close button -->
        <button onclick="closeWhatsNewModal()" class="btn btn-base btn-white-outline mt-4 mb-2">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
<script>
  function handleDisplaySchoolMemo() {
    const today = new Date();
    const memo = `<%= @school.memo %>`;

    window.localStorage.setItem('lastMemoReadDate', today.toJSON());
    window.localStorage.setItem('lastMemoRead', memo);

    const dialog = document.querySelector('#school_memo');
    dialog.showModal();
  }

  function handleCheckMemo() {
    const memo = `<%= @school.memo %>`;
    // string bool == string bool fixes vs code syntax highlighting problem
    const hasMemo = "<%= @school.memo.present? %>" == "true";
    if(!hasMemo) {
      return;
    }

    const isMemoDiff = window.localStorage.getItem('lastMemoRead') !== memo;

    let memoReadDateString = window.localStorage.getItem('lastMemoReadDate');

    // if memo has not been read, show the memo
    if(!memoReadDateString || isMemoDiff) {
      return handleDisplaySchoolMemo();
    }

    let date
    if(memoReadDateString) {
      date = new Date(memoReadDateString);
      // if date is not a real/valid date, show the memo
      if(isNaN(date.valueOf())) {
        return handleDisplaySchoolMemo();
      }
    }

    const today = new Date();
    const formattedToday = today.toISOString().split('T')[0];
    const formattedDate = date.toISOString().split('T')[0];

    // if memo has been read today, do not show the memo
    if(formattedToday === formattedDate) {
      return;
    }

    handleDisplaySchoolMemo()
  }

  function handleToggleDetailsPanel() {
    const detailsPanel = document.querySelector('[data-details-panel]');
    detailsPanel.classList.toggle('hidden');
    const showButton = document.querySelector('[data-details-panel-show]');
    showButton.classList.toggle('hidden');
    window.sessionStorage.setItem('detailsPanelHidden', detailsPanel.classList.contains('hidden'));
  }

  function loadQuickLinksCache() {
    const userId = "<%= @current_user.id %>"
    const linkToggles = window.localStorage.getItem(`quickLinks-${userId}`);
    const toggles = {}
    if(linkToggles) {
      try {
        Object.assign(toggles, JSON.parse(linkToggles));
      } catch (e) {
        console.error(e);
      }
    } else {
      console.log('no quick links cache found');

      // set default state
      const links = <%= @quick_links.to_json.html_safe %>
      const defaultKeys = ["library", "my_classes", "mark_book"]
      for(const link of links) {
        toggles[link.key] = !defaultKeys.includes(link.key);
      }
    }
    return toggles;
  }

  function setQuickLinksCache(toggles) {
    const userId = "<%= @current_user.id %>"
    window.localStorage.setItem(`quickLinks-${userId}`, JSON.stringify(toggles));
  }

  function handleConnectQuickLinksCheckboxes() {
    const container = document.querySelector('#quick-link-checkboxes');
    const checkboxes = Array.from(container.querySelectorAll('input'));
    const toggles = loadQuickLinksCache()
    for(const checkbox of checkboxes) {
      // invert the checkbox so the default state is "enabled" even tho we store it as "disabled"
      checkbox.checked = !toggles[checkbox.value];
      checkbox.addEventListener('change', () => {
        checkboxes.forEach(checkbox => toggles[checkbox.value] = !checkbox.checked);
        setQuickLinksCache(toggles);
        handleDisplayQuickLinks();
      })
    }
  }

  function handleDisplayQuickLinks() {
    const quickLinks = document.querySelector('[data-quick-links]');

    const existingLinks = Array.from(quickLinks.children);
    existingLinks.forEach(link => link.remove());

    const links = <%= @quick_links.to_json.html_safe %>

    const toggles = loadQuickLinksCache()
    links.forEach(link => link.hide = toggles[link.key]);

    links.forEach(link => {
      if(link.hide) {
        return;
      }
      const a = document.createElement('a');
      a.href = link.path;
      a.classList.add('btn', 'btn-base', 'btn-purple');
      a.textContent = link.label;
      quickLinks.appendChild(a);
    })
  }

  // What's New modal functionality
  let whatsNewFeatures = <%= @whats_new_features.to_json.html_safe %>;
  let currentFeatureIndex = 0;
  
  function loadWhatsNewFeature(index) {
    // Ensure the index is within bounds
    if (index < 0) index = whatsNewFeatures.length - 1;
    if (index >= whatsNewFeatures.length) index = 0;
    
    currentFeatureIndex = index;
    const feature = whatsNewFeatures[index];
    
    // Update the modal content
    document.getElementById('whats-new-image').src = feature.image_url;
    document.getElementById('whats-new-title').textContent = feature.title;
    document.getElementById('whats-new-date').textContent = `Added ${feature.date_added}`;
    document.getElementById('whats-new-description').innerHTML = feature.description; // Changed from textContent to innerHTML
    
    // Update navigation buttons state
    document.getElementById('whats-new-prev').disabled = whatsNewFeatures.length <= 1;
    document.getElementById('whats-new-next').disabled = whatsNewFeatures.length <= 1;
    
    // Update indicators
    updateWhatsNewIndicators();
  }
  
  function updateWhatsNewIndicators() {
    const container = document.getElementById('whats-new-indicators');
    container.innerHTML = '';
    
    // Create indicator dots
    whatsNewFeatures.forEach((_, index) => {
      const indicator = document.createElement('span');
      indicator.classList.add('w-2', 'h-2', 'rounded-full', 'cursor-pointer');
      
      // Highlight the current indicator
      if (index === currentFeatureIndex) {
        indicator.classList.add('bg-purple-500');
      } else {
        indicator.classList.add('bg-gray-600', 'hover:bg-gray-500');
      }
      
      // Add click event to navigate to that feature
      indicator.addEventListener('click', () => loadWhatsNewFeature(index));
      
      container.appendChild(indicator);
    });
  }
  
  function navigateWhatsNew(direction) {
    loadWhatsNewFeature(currentFeatureIndex + direction);
  }
  
  function handleShowWhatsNew() {
    const modal = document.querySelector('#whats_new_modal');
    modal.classList.remove('hidden');
    loadWhatsNewFeature(0);
  }
  
  function closeWhatsNewModal() {
    const modal = document.querySelector('#whats_new_modal');
    modal.classList.add('hidden');
  }

  document.addEventListener("DOMContentLoaded", () => {
    const detailsPanelState = window.sessionStorage.getItem('detailsPanelHidden');
    if (detailsPanelState === 'true') {
      handleToggleDetailsPanel();
    }
    handleCheckMemo();
    handleDisplayQuickLinks();
    handleConnectQuickLinksCheckboxes();
    
    // Check if the What's New modal should be shown automatically
    if (<%= @should_auto_show_whats_new.to_json.html_safe %>) {
      handleShowWhatsNew();
    }
    
    // Initialize the first feature in the What's New modal
    loadWhatsNewFeature(0);
  })
</script>
