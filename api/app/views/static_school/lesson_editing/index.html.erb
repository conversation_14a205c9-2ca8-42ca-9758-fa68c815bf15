<%= javascript_include_tag "nice-select", type: "module" %>
<% content_for :title, "Lesson Editing" %>
<div class="pt-12 nova-headings max-w-screen-2xl mx-auto px-4 md:px-8">
  <div class="text-white mb-8">
    <%= render TeacherStandardTitleAreaComponent.new(
      title: "Lesson Editing Tools",
      tour: {
        show: @current_user.beta_feature_enabled?(:teacher_page_tours),
        page: "lesson_editor",
      },
      icon: "fad fa-pen",
      layout: :float
    ) do %>
      <div class="text-lg">
        <p>Create and manage your custom lessons.</p>
        <p>Custom lessons you create can only be edited by you.</p>
        <p>You can view published custom lessons created by teachers in your school in the <%= link_to 'Community Library', "/s/library/community", class: "text-de-brand underline" %>.</p>
      </div>
    <% end %>
  </div>
  
  <div>
    <!-- Create New Lesson Card and Recently Edited Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
      <!-- Create New Lesson Card -->
      <div class="lg:col-span-1">
        <div class="template-card bg-white/5 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-6 group hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-blue-400/30 transition-all duration-300 h-full">
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <i class="fas fa-plus text-2xl text-white"></i>
            </div>
            <h3 class="text-white text-xl font-bold mb-3">Create New Lesson</h3>
            <p class="text-gray-300 mb-4 text-sm leading-relaxed">Start building your custom lesson from scratch, use a proven template, or let AI create one for you.</p>
            
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-xs text-gray-400 justify-center">
                <i class="fas fa-check text-de-brand mr-2 text-xs"></i>
                Complete customization
              </div>
              <div class="flex items-center text-xs text-gray-400 justify-center">
                <i class="fas fa-check text-de-brand mr-2 text-xs"></i>
                Expert templates available
              </div>
              <div class="flex items-center text-xs text-gray-400 justify-center">
                <i class="fas fa-check text-de-brand mr-2 text-xs"></i>
                AI-powered generation
              </div>
            </div>

            <div>
              <a id="create-lesson-btn" href="<%= new_school_lesson_editing_path %>" class="btn btn-base btn-purple w-full group-hover:bg-purple-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Create New Lesson
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Recently Edited Section -->
      <div class="lg:col-span-2">
        <div id="recently-edited-container" class="bg-secondary-dark-blue rounded-lg p-6">
          <h2 class="text-2xl text-white mb-4">
            Recently Edited
          </h2>
          <% if @recently_edited.present? %>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
              <% @recently_edited.includes([:source_template]).limit(2).map do |template| %>
                <%= render CardComponent.new(
                  title: template.name,
                  title_text_size: "xl",
                  image_background_fileboy_id: template.fileboy_image_id.presence || Fileboy::PLACEHOLDER_IMAGE_ID,
                  path: show_details_school_lesson_editing_path(template.id)
                ) %>
              <% end %>
            </div>
          <% else %>
            <div class="text-center text-gray-400 py-8">
              <i class="fas fa-file-edit text-4xl mb-4 opacity-50"></i>
              <p class="text-lg mb-2">No lessons created yet</p>
              <p class="text-sm">Create your first lesson to get started!</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <div id="folder-index" class="bg-white rounded-lg p-8 pb-4 text-black mb-4">
      <div class="flex flex-col lg:flex-row justify-between lg:items-center mb-4 gap-2 flex-wrap">
        <h2 class="text-2xl text-black">
          Your lessons <span class="opacity-50 ml-4"><%= @current_user.user_lesson_templates.size %></span>
        </h2>
        <!-- Filter controls - shown when no lessons are selected -->
        <div id="filter-controls" class="flex-1 flex gap-2 max-w-xl flex-wrap sm:flex-nowrap whitespace-nowrap">
          <%= form_with url: school_lesson_editing_index_path, class: "flex gap-2 max-w-xl flex-wrap sm:flex-nowrap", method: :get, local: true do |form| %>
            <script>
              document.addEventListener("DOMContentLoaded", function() {
                const form = document.querySelector('form[action="<%= school_lesson_editing_index_path %>"]');
                if(form) {
                  form.addEventListener('change', function() {
                    form.submit();
                  });
                }
              });
            </script>
            <%= form.select :year_id,
              options_from_collection_for_select(@years, 'id', 'name', @filter_year&.to_i), 
              { class: "p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-sm appearance-none cursor-pointer w-max", prompt: "Filter by year" }
            %>
            <%= form.select :unit_id,
              options_from_collection_for_select(@units, 'id', 'name', @filter_unit&.to_i), 
              { class: "p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-sm appearance-none cursor-pointer w-max max-w-xs", prompt: "Filter by unit" }
            %>
            <%= form.select :sort,
              options_for_select([
                ['Name (A-Z)', 'name_asc'],
                ['Name (Z-A)', 'name_desc'],
                ['Date (Newest)', 'date_desc'],
                ['Date (Oldest)', 'date_asc']
              ], @sort),
              { class: "p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-sm appearance-none cursor-pointer w-max" }
            %>
          <% end %>
          <%= render ButtonComponent::Base.new(
            text: "New Folder",
            left_icon: "fas fa-plus",
            data: { name: "new-folder-btn" }
          ) %>
        </div>
        
        <!-- Action controls - shown when lessons are selected -->
        <div id="action-controls" class="flex gap-2 flex-wrap" style="display: none;">
          <button name="clear-folder-btn" class="btn btn-base btn-purple">
            <div class="flex gap-4 items-center">
              <i class="fas fa-folder-minus"></i>
              <span>Remove Selected from Folders</span>
            </div>
          </button>
          <button name="move-folder-btn" class="btn btn-base btn-purple">
            <div class="flex gap-4 items-center">
              <i class="fas fa-folder-open"></i>
              <span>Move Selected to Folder</span>
            </div>
          </button>
        </div>
      </div>
      <% @folders.map do |folder| %>
        <%= render "folder_table", folder: folder, depth: 0 %>
      <%end%>
      <% if @loose_templates.present? %>
        <!-- Lessons not in folder section -->
        <div id="loose-folder-container" class="mb-6">
          <div class="bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-300 border-dashed rounded-lg p-4 mb-3 folder-drop-zone transition-all duration-200" 
               data-folder-id=""
               ondragover="handleDragOver(event)" 
               ondragleave="handleDragLeave(event)"
               ondrop="handleDropRemoveFromFolder(event)">
            <div class="flex items-center justify-between flex-wrap">
              <div class="flex gap-3 items-center min-w-0">
                <div class="text-gray-500 flex-shrink-0">
                  <i class="fas fa-folder-open text-lg"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-700 nova">Lessons not in folder</h3>
                <div class="text-sm text-gray-500 flex-shrink-0">
                  <%= pluralize(@loose_templates.count, 'lesson') %>
                </div>
              </div>
              <div class="text-gray-400 text-sm italic">
                Drag lessons here to remove from folders
              </div>
            </div>
          </div>
          
          <div class="space-y-1">
            <% @loose_templates.user_template_filter_by_year_and_unit(@current_user, @filter_year, @filter_unit).ordered_by(@sort).includes([:source_template]).map.with_index do |template, index|%>
              <%= render "folder_row", folder: nil, template: template, index: index %>
            <%end%>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<dialog name="folder-dialog" class="max-w-3xl w-full p-4 overflow-visible">
  <h1 class="text-xl mb-2" name="dialog-title">New Folder</h1>
  <%= form_with url: manage_folder_school_lesson_editing_index_path, method: :post, local: true do |form| %>
    <input type="hidden" name="id" value="">
    <div class="field mb-2">
      <%= form.label :name %>
      <%= form.text_field :name, class: 'required', required: true, placeholder: "Folder name" %>
    </div>
    <div class="mb-4">
      <div>
        <%= form.label :parent_folder_id, "Parent Folder (optional)" %>
        <%= form.select :parent_folder_id,
            options_for_select(@all_folders.pluck(:name, :id)),
            { include_blank: "Select Folder" },
            { data: { nice_select: true, default_options: true, is_clearable: "", filter_options: "folderOptionsFilter" } }
        %>
      </div>
    </div>
    <div class="flex gap-2">
      <button data-cancel-btn type="button" class="btn btn-base btn-flat-white">Cancel</button>
      <button type="submit" class="btn btn-base btn-flat-cyan">Submit</button>
      <button
        id="delete-btn"
        type="button"
        class="btn btn-base btn-flat-red ml-auto"
        onclick="handleDeleteFolder(event, this)"
      >
        Delete
      </button>
    </div>
  <% end %>
</dialog>

<dialog name="move-lesson-dialog" class="max-w-3xl w-full p-4 overflow-visible">
  <h1 class="text-xl mb-2" name="dialog-title"></h1>
  <%= form_with url: move_templates_school_lesson_editing_index_path, method: :post, local: true do |form| %>
    <input type="hidden" name="lesson_template_ids" value="">
    <div class="mb-4">
      <label for="id" class="required" required>Folder</label>
      <%= form.select :id,
        options_for_select(@all_folders.pluck(:name, :id)),
        { include_blank: "Select Folder", class: "required" },
        { required: true, data: { nice_select: true, default_options: true } }
      %>
    </div>
    <div class="flex gap-2">
      <button data-cancel-btn type="button" class="btn btn-base btn-flat-white">Cancel</button>
      <button type="submit" class="btn btn-base btn-flat-cyan">Submit</button>
    </div>
  <% end %>
</dialog>

<style>
  .folder-drop-zone.drag-over {
    background-color: #eff6ff !important;
    border: 2px dashed #3b82f6 !important;
    transform: scale(1.02);
  }
  
  .lesson-row-dragging {
    opacity: 0.5 !important;
    transform: rotate(2deg);
  }

  .template-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 2px solid rgb(75, 85, 99);
    transition: all 0.3s ease;
  }

  .template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
  }
</style>

<script type="module">
  import { computePosition, offset, shift } from 'https://cdn.jsdelivr.net/npm/@floating-ui/dom@latest/+esm';
  function initializeTooltips() {
    document.querySelectorAll('[data-tooltip]').forEach(button => {
      const tooltip = document.createElement('div');
      tooltip.classList.add('tooltip');
      tooltip.textContent = button.getAttribute('data-tooltip');
      tooltip.style.visibility = 'hidden'; // Ensure tooltip starts hidden
      tooltip.style.opacity = '0'; // Ensure tooltip starts fully transparent

      button.addEventListener('mouseover', function () {
        document.body.appendChild(tooltip); // Append only on mouseover
        computePosition(button, tooltip, {
            placement: 'top',
            middleware: [offset(5), shift({ padding: 5 })],
            strategy: 'fixed'
        }).then(({x, y}) => {
            tooltip.style.left = `${x}px`;
            tooltip.style.top = `${y}px`;
            tooltip.style.visibility = 'visible';
            tooltip.style.opacity = '1'; // Fade in
            tooltip.style['z-index'] = '1000'; // Ensure tooltip is on top
        });
      });

      button.addEventListener('mouseout', function () {
        tooltip.style.visibility = 'hidden';
        tooltip.style.opacity = '0'; // Fade out
        document.body.removeChild(tooltip); // Remove after fade out to avoid reflow
      });
    });
  }
  document.addEventListener("DOMContentLoaded", initializeTooltips);
</script>

<script>
  // Drag and Drop Variables
  let draggedTemplateId = null;

  // Drag and Drop Functions
  function handleDragStart(event) {
    draggedTemplateId = event.currentTarget.getAttribute('data-template-id');
    event.currentTarget.classList.add('lesson-row-dragging');
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/html', event.currentTarget.outerHTML);
  }

  function handleDragEnd(event) {
    event.currentTarget.classList.remove('lesson-row-dragging');
    // Clean up any remaining drag-over states
    document.querySelectorAll('.folder-drop-zone').forEach(zone => {
      zone.classList.remove('drag-over');
    });
  }

  function handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    event.currentTarget.classList.add('drag-over');
  }

  function handleDragLeave(event) {
    // Only remove the class if we're actually leaving the element
    if (!event.currentTarget.contains(event.relatedTarget)) {
      event.currentTarget.classList.remove('drag-over');
    }
  }

  function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
    
    const folderId = event.currentTarget.getAttribute('data-folder-id');
    
    if (draggedTemplateId && folderId) {
      moveTemplateToFolder(draggedTemplateId, folderId);
    }
    
    draggedTemplateId = null;
  }

  async function moveTemplateToFolder(templateId, folderId) {
    try {
      const response = await fetch('<%= move_templates_school_lesson_editing_index_path %>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: `lesson_template_ids=${templateId}&id=${folderId}`
      });
      
      if (response.ok) {
        window.location.reload();
      } else {
        throw new Error('Failed to move template');
      }
    } catch (error) {
      console.error('Error moving template:', error);
      if (typeof showToast !== 'undefined') {
        showToast('Error moving lesson to folder');
      } else {
        alert('Error moving lesson to folder');
      }
    }
  }

  function handleDropRemoveFromFolder(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
    
    if (draggedTemplateId) {
      removeTemplateFromFolder(draggedTemplateId);
    }
    
    draggedTemplateId = null;
  }

  async function removeTemplateFromFolder(templateId) {
    try {
      const response = await fetch('<%= clear_folder_school_lesson_editing_index_path %>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: `lesson_template_ids=${templateId}`
      });
      
      if (response.ok) {
        window.location.reload();
      } else {
        throw new Error('Failed to remove template from folder');
      }
    } catch (error) {
      console.error('Error removing template from folder:', error);
      if (typeof showToast !== 'undefined') {
        showToast('Error removing lesson from folder');
      } else {
        alert('Error removing lesson from folder');
      }
    }
  }

  // Updated function to toggle between filter and action controls
  function toggleTemplateActionsVisibility() {
    const checkboxes = document.querySelectorAll("input[type='checkbox']:checked")
    const hasSelected = checkboxes.length > 0
    
    // Get the control containers
    const filterControls = document.getElementById('filter-controls')
    const actionControls = document.getElementById('action-controls')
    
    if (hasSelected) {
      // Show action controls, hide filters
      filterControls.style.display = 'none'
      actionControls.style.display = 'flex'
    } else {
      // Show filters, hide action controls
      filterControls.style.display = 'flex'
      actionControls.style.display = 'none'
    }
  }

  // When a checkbox is toggled, check if the move folder button should be enabled
  function handleCheckboxToggle(event) {
    toggleTemplateActionsVisibility()
  }

  // Row Click Handler
  function handleRowClick(event, templateId) {
    // Don't trigger if clicking on a link or the checkbox itself
    if (event.target.tagName === 'A' || event.target.tagName === 'INPUT' || event.target.closest('a')) {
      return;
    }
    
    const checkbox = document.querySelector(`#template-${templateId}`);
    if (checkbox) {
      checkbox.checked = !checkbox.checked;
      handleCheckboxToggle();
    }
  }

  async function handleDeleteFolder(event, element) {
    const dialog = element.closest("dialog")
    if (!dialog) {
      console.error("Dialog not found for element")
      return
    }
    const id = dialog.querySelector("input[name='id']")?.value

    if(!id) {
      console.error("No ID available to destroy")
      return
    }
    if(!confirm("Are you sure you want to delete this folder? Any lessons or subfolders will be unassigned.")) {
      return
    }
    const path = "<%= manage_folder_school_lesson_editing_index_path %>"
    let response
    try {
      const url = new URL(path, window.location.origin)
      url.searchParams.set("id", id)
      response = await fetch(url.toString(), {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": document.querySelector("meta[name='csrf-token']").content
        }
      })
      if(response.ok) {
        const result = await response.json()
        if(!result.saved) {
          throw new Error(result.error)
        }
        window.location.reload()
      }
    } catch (e) {
      console.error("Failed to delete folder", e)
      element.closest("dialog").close()
      if (typeof showToast !== 'undefined') {
        showToast("An error occurred deleting the folder")
      } else {
        alert("An error occurred deleting the folder")
      }
    }
  }

  // ALL FOLDERS (array)
  const allFolders = <%= @all_folders.pluck_to_hash(:id, :name, :parent_folder_id).to_json.html_safe %>

  function folderOptionsFilter(selectList, option) {
    let folders = [...allFolders]
    // If it's a new folder, there is no data-folder id, so there is no need to filter the options
    const openFolderId = selectList.getAttribute("data-folder-id")
    if(!openFolderId) {
      return true
    }

    // Cannot select self
    folders = folders.filter(folder => folder.id != openFolderId)

    // Cannot select folders under the current one (e.g)
    // - this folder
    // - - sub folder (cannot select this as that would create a loop)
    folders = folders.filter(folder => {
      let parentFolderId = folder.parent_folder_id
      while(parentFolderId) {
        if(parentFolderId == openFolderId) {
          return false
        }
        parentFolderId = folders.find(f => f.id == parentFolderId).parent_folder_id
      }
      return true
    })
    return !!folders.find(x => x.id == option.value)
  }

  document.addEventListener("DOMContentLoaded", () => {
    const selectors = {
      newFolderBtn: "button[data-name='new-folder-btn']",
      moveFolderBtn: "button[name='move-folder-btn']",
      clearFolderBtn: "button[name='clear-folder-btn']",
      editFolderBtn: "button[name='edit-folder-btn']",
      newSubFolderBtn: "button[name='new-sub-folder-btn']",
      moveToFolderBtn: "button[name='move-to-folder-btn']",
      folderDialog: "dialog[name='folder-dialog']",
      dialogTitle: "h1[name='dialog-title']",
      moveFolderDialog: "dialog[name='move-lesson-dialog']",
      parentFolderIdSelect: "select[name='parent_folder_id']",
      folderIdSelect: "select[name='id']",
      templateIdsField: "input[name='lesson_template_ids']"
    }

    // NEW FOLDER open trigger
    const newFolderBtns = Array.from(document.querySelectorAll(selectors.newFolderBtn))
    newFolderBtns.forEach(btn => {
      btn.addEventListener("click", triggerNewFolderDialog)
    })

    // MOVE FOLDER open trigger
    const moveFolderBtns = document.querySelectorAll(selectors.moveFolderBtn)
    moveFolderBtns.forEach(btn => {
      btn.addEventListener("click", triggerMoveFolderDialog)
    })

    const moveToButtons = document.querySelectorAll(selectors.moveToFolderBtn)
    moveToButtons.forEach(btn => {
      btn.addEventListener("click", triggerMoveFolderDialog)
    })

    // CLEAR FOLDER open trigger
    const clearFolderBtns = document.querySelectorAll(selectors.clearFolderBtn)
    clearFolderBtns.forEach(btn => {
      btn.addEventListener("click", handleClearFolders)
    })

    // MOVE FOLDER close trigger
    const moveFolderDialog = document.querySelector(selectors.moveFolderDialog)
    moveFolderDialog.querySelector("[data-cancel-btn]").addEventListener("click", () => {
      moveFolderDialog.close()
    })

    // EDIT FOLDER open trigger
    const editFolderBtns = document.querySelectorAll(selectors.editFolderBtn)
    editFolderBtns.forEach(btn => {
      btn.addEventListener("click", triggerEditFolderDialog)
    })

    // NEW SUB FOLDER open trigger
    const newSubFolderButtons = document.querySelectorAll(selectors.newSubFolderBtn)
    newSubFolderButtons.forEach(btn => {
      btn.addEventListener("click", triggerNewFolderDialog)
    })

    // New/Edit folder dialog close trigger
    const folderDialog = document.querySelector(selectors.folderDialog)
    folderDialog.querySelector("[data-cancel-btn]").addEventListener("click", () => {
      folderDialog.close()
    })
    const folderDialogTitle = folderDialog.querySelector(selectors.dialogTitle)

    // Updated function to toggle between filter and action controls
    function toggleTemplateActionsVisibilityLocal() {
      const checkboxes = document.querySelectorAll("input[type='checkbox']:checked")
      const hasSelected = checkboxes.length > 0
      
      // Get the control containers
      const filterControls = document.getElementById('filter-controls')
      const actionControls = document.getElementById('action-controls')
      
      if (hasSelected) {
        // Show action controls, hide filters
        filterControls.style.display = 'none'
        actionControls.style.display = 'flex'
      } else {
        // Show filters, hide action controls
        filterControls.style.display = 'flex'
        actionControls.style.display = 'none'
      }
    }

    // When a checkbox is toggled, check if the move folder button should be enabled
    function handleCheckboxToggleLocal(event) {
      toggleTemplateActionsVisibilityLocal()
    }

    // Add event listeners to all checkboxes
    const checkboxes = document.querySelectorAll("input[type='checkbox']")
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener("change", handleCheckboxToggleLocal)
    })

    // Edit folder action
    function triggerEditFolderDialog(event) {
      folderDialog.showModal()
      const folderId = event.currentTarget.getAttribute("data-folder-id")
      const folder = allFolders.find(folder => folder.id == folderId)

      // set the title to editing
      folderDialogTitle.innerText = folder ? `Edit ${folder.name}` : "Edit Folder"

      // set the id field
      const idField = folderDialog.querySelector("input[name='id']")
      idField.value = folder?.id || ""

      // set the name field
      const nameField = folderDialog.querySelector("input[name='name']")
      nameField.value = folder?.name || ""

      // set the data-folder-id to prevent the select list from selecting itself
      const selectField = folderDialog.querySelector(selectors.parentFolderIdSelect)
      selectField.setAttribute('data-folder-id', folderId)

      // set all parent folder id field
      const parentFolderId = folder?.parent_folder_id || ""
      selectField.setAttribute("value", parentFolderId)
      selectField.value = parentFolderId

      const deleteButton = folderDialog.querySelector("#delete-btn")
      deleteButton.style.display = "block"
    }

    function triggerNewFolderDialog(event) {
      folderDialog.showModal()
      // Clear all the field values
      const selectField = folderDialog.querySelector(selectors.parentFolderIdSelect)
      folderDialogTitle.innerText = "New Folder"

      const idField = folderDialog.querySelector("input[name='id']")
      idField.value = ""

      const nameField = folderDialog.querySelector("input[name='name']")
      nameField.value = ""

      selectField.setAttribute('data-folder-id', '')
      selectField.setAttribute("value", '')

      const deleteButton = folderDialog.querySelector("#delete-btn")
      deleteButton.style.display = "none"

      const selectLabel = selectField.parentElement.parentElement.querySelector('label')
      const target = event?.currentTarget
      if(target && target.name == "new-sub-folder-btn") {
        const parentFolderId = target.getAttribute("data-folder-id")
        selectField.setAttribute("value", parentFolderId)
        // disabled inputs DO NOT submit a value
        selectField.setAttribute('disabled', 'true')

        // append a hidden input to submit the parent folder id
        const valueInput = document.createElement("input")
        valueInput.type = "hidden"
        valueInput.name = "parent_folder_id"
        valueInput.value = parentFolderId
        selectField.parentElement.appendChild(valueInput)

        folderDialogTitle.innerText = "New Sub Folder"
        selectLabel.innerText = "Parent Folder"
        selectField.value = parentFolderId
      } else {
        selectLabel.innerText = "Parent Folder (optional)"
        selectField.removeAttribute('disabled')
        selectField.value = ''

        // Remove the hidden input if it exists
        if(selectField.parentElement.querySelector("input[name='parent_folder_id']")) {
          selectField.parentElement.querySelector("input[name='parent_folder_id']").remove()
        }
      }
    }

    const movedFolderDialogTitle = moveFolderDialog.querySelector(selectors.dialogTitle)
    function triggerMoveFolderDialog(event) {
      moveFolderDialog.showModal()
      // Clear all the field values
      const selectField = moveFolderDialog.querySelector(selectors.folderIdSelect)

      const selectedCheckboxes = document.querySelectorAll("input[type='checkbox']:checked")
      movedFolderDialogTitle.innerText = `Move ${selectedCheckboxes.length} template${selectedCheckboxes.length == 1 ? "" : "s"} to folder`

      selectField.setAttribute("value", '')

      const lessonTemplateIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.getAttribute("data-template-id")).join(",")
      const templateIdsField = moveFolderDialog.querySelector(selectors.templateIdsField)
      templateIdsField.value = lessonTemplateIds

      const folderId = event.currentTarget.getAttribute("data-folder-id")
      const target = event?.currentTarget
      if(target && target.name == "move-to-folder-btn") {
        selectField.setAttribute("value", folderId)
        selectField.value = folderId
      } else {
        selectField.removeAttribute('disabled')
        selectField.value = ''
      }
    }

    async function handleClearFolders(event) {
      const checkboxes = document.querySelectorAll("input[type='checkbox']:checked")
      const lessonTemplateIds = Array.from(checkboxes).map(checkbox => checkbox.getAttribute("data-template-id")).join(",")

      if(!confirm(`Are you sure you want to remove ${checkboxes.length} lesson${checkboxes.length == 1 ? "" : "s"} from their folders?`)) {
        return
      }

      const path = "<%= clear_folder_school_lesson_editing_index_path %>"
      let response
      try {
        const url = new URL(path, window.location.origin)
        url.searchParams.set("lesson_template_ids", lessonTemplateIds)
        response = await fetch(url.toString(), {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": document.querySelector("meta[name='csrf-token']").content
          }
        })
        if(response.ok) {
          const result = await response.json()
          if(!result.saved) {
            throw new Error(result.error)
          }
          window.location.reload()
        }
      } catch (e) {
        console.error("Failed to clear folders from lessons", e)
        if (typeof showToast !== 'undefined') {
          showToast("An error occurred clearing the folders from the selected lessons")
        } else {
          alert("An error occurred clearing the folders from the selected lessons")
        }
      }
    }

    // final setup
    toggleTemplateActionsVisibility();
  })

  // Make functions global so they can be called from inline handlers
  window.handleDragStart = handleDragStart;
  window.handleDragEnd = handleDragEnd;
  window.handleDragOver = handleDragOver;
  window.handleDragLeave = handleDragLeave;
  window.handleDrop = handleDrop;
  window.handleRowClick = handleRowClick;
  window.handleCheckboxToggle = handleCheckboxToggle;
  window.handleDropRemoveFromFolder = handleDropRemoveFromFolder;
  window.toggleTemplateActionsVisibility = toggleTemplateActionsVisibility;
</script>
