<%# TEMPLATE SELECTION %>
<% content_for :title, "Create New Lesson" %>

<div class="px-4 md:px-8 py-8 max-w-screen-xl mx-auto nova-headings">
  <div class="text-white mb-8">
    <%= render TeacherStandardTitleAreaComponent.new(
      title: "Create Lesson",
      icon: "fad fa-pen",
      layout: :float
    ) do %>
      <p class="text-lg">
        <%- if @current_user && @current_user.beta_feature_enabled?(:ai_lesson_builder) %>
          Choose how you'd like to start building your lesson. You can start from scratch, use a proven template, or let AI create one for you.
        <%- else %>
          Choose how you'd like to start building your lesson. You can start from scratch or use a proven template designed by expert educators.
        <%- end %>
      </p>
    <% end %>
  </div>

  <!-- Main Options Grid -->
  <div class="grid grid-cols-1 <%= @current_user && @current_user.beta_feature_enabled?(:ai_lesson_builder) ? 'lg:grid-cols-3' : 'lg:grid-cols-2' %> gap-8 mb-12">
    <!-- Option 1: Start From Scratch -->
    <div class="template-card bg-white/5 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-8 group hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-blue-400/30 transition-all duration-300">
      <div class="text-center">
        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
          <i class="fas fa-pencil-alt text-3xl text-white"></i>
        </div>
        <h3 class="text-white text-2xl font-bold mb-4">Start From Scratch</h3>
        <p class="text-gray-300 mb-6 leading-relaxed">Build your lesson from the ground up with complete creative control. Perfect for unique topics or when you have a specific vision.</p>
        
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-check text-de-brand mr-3"></i>
            Complete customization
          </div>
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-check text-de-brand mr-3"></i>
            Your own structure & flow
          </div>
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-check text-de-brand mr-3"></i>
            Unlimited flexibility
          </div>
        </div>

        <div class="bg-blue-500/10 border border-blue-400/30 text-blue-400 rounded-full px-4 py-2 text-sm font-medium inline-block mb-4">
          <i class="fas fa-clock mr-2"></i>45-60 minutes
        </div>
        <div>
          <%= render ButtonComponent::Base.new(text: "Start Building", left_icon: "fas fa-pencil-alt", url: new_from_scratch_school_lesson_editing_index_path) %>
        </div>
      </div>
    </div>

    <!-- Option 2: Use Template -->
    <div class="template-card bg-white/5 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-8 group hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-blue-400/30 transition-all duration-300">
      <div class="text-center">
        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
          <i class="fas fa-layer-group text-3xl text-white"></i>
        </div>
        <h3 class="text-white text-2xl font-bold mb-4">Use Template</h3>
        <p class="text-gray-300 mb-6 leading-relaxed">Start with a proven lesson structure designed by expert educators. Customize and adapt to your specific needs.</p>
        
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-check text-de-brand mr-3"></i>
            Expert-designed structures
          </div>
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-check text-de-brand mr-3"></i>
            Proven effectiveness
          </div>
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-check text-de-brand mr-3"></i>
            Easy customization
          </div>
        </div>

        <div class="bg-blue-500/10 border border-blue-400/30 text-blue-400 rounded-full px-4 py-2 text-sm font-medium inline-block mb-4">
          <i class="fas fa-clock mr-2"></i>15-30 minutes
        </div>
        <div>
          <%= render ButtonComponent::Base.new(text: "Browse Templates", left_icon: "fas fa-layer-group", url: new_from_templates_school_lesson_editing_index_path) %>
        </div>
      </div>
    </div>

    <%- if @current_user && @current_user.beta_feature_enabled?(:ai_lesson_builder) %>
    <!-- Option 3: Generate with AI -->
      <div class="de-ai-assistant-container template-card group" style="padding: 2px; border-radius: 0.75rem;">
        <div class="de-ai-assistant-content-wrapper" style="border-radius: calc(0.75rem - 2px);">
          <div class="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 p-8 h-full rounded-xl">
            <div class="text-center relative z-10">
              <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-400 via-purple-500 to-cyan-400 rounded-full flex items-center justify-center relative">
                <i class="fas fa-robot text-3xl text-white"></i>
                <div class="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-cyan-400 rounded-full animate-ping opacity-30"></div>
              </div>
              <h3 class="text-2xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Generate with AI</h3>
              <p class="text-gray-300 mb-6 leading-relaxed">Let our AI create a complete lesson tailored to your topic and requirements. Just describe what you need and watch it come to life.</p>
              
              <div class="space-y-3 mb-6">
                <div class="flex items-center text-sm text-gray-400">
                  <i class="fas fa-check text-de-brand mr-3"></i>
                  Instant lesson creation
                </div>
                <div class="flex items-center text-sm text-gray-400">
                  <i class="fas fa-check text-de-brand mr-3"></i>
                  Tailored to your topic
                </div>
                <div class="flex items-center text-sm text-gray-400">
                  <i class="fas fa-check text-de-brand mr-3"></i>
                  Smart content suggestions
                </div>
              </div>

              <div class="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-400/30 rounded-full px-4 py-2 text-sm font-medium inline-block mb-4">
                <i class="fas fa-magic mr-2 text-blue-400"></i>2-5 minutes
              </div>

              <button class="btn btn-base btn-white w-full mt-4 bg-gradient-to-r from-blue-500 to-cyan-500 border-none hover:from-blue-600 hover:to-cyan-600" onclick="continueWithOption('ai')">
                <i class="fas fa-magic mr-2"></i>Start AI Generation
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
