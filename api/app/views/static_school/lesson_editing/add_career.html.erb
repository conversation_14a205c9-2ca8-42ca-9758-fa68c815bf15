<% content_for :title, "Editing Careers | #{@lesson_template.name}" %>
<div class="pt-12 nova-headings max-w-screen-2xl mx-auto px-4 md:px-8">
  <%= render 'show_header', lesson_template: @lesson_template %>
  <div class="bg-white rounded-xl rounded-tl-none text-black">
    <%= render 'static_admin/lesson_templates/careers_table', 
      lesson_template: @lesson_template, 
      careers: @careers,
      add_path: ->(lesson_template) { add_career_school_lesson_editing_path(lesson_template, **params.permit(:query, :page)) },
      edit_path: ->(lesson_template) { show_careers_school_lesson_editing_path(lesson_template, **params.permit(:query, :page)) },
      add_to_template_path: ->(lesson_template, career_path) { add_career_path_school_lesson_editing_path(lesson_template, career_path_id: career_path.id, **params.permit(:query, :page)) },
      remove_from_template_path: ->(lesson_template, career_path) { remove_career_path_school_lesson_editing_path(lesson_template, career_path_id: career_path.id, **params.permit(:query, :page)) },
      mode: :search
    %>
  </div>
</div>
