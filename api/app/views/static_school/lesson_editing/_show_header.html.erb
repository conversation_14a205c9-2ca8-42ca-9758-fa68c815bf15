<div class="text-white mb-8">
  <%= render TeacherStandardTitleAreaComponent.new(
    title: "Editing Lesson: #{lesson_template.name}", 
    layout: :float,
    icon: "fad fa-graduation-cap",
    tour: {
      show: @current_user.beta_feature_enabled?(:teacher_page_tours),
      page: "lesson_editor",
    },
  ) %>
</div>
<div class="bg-secondary-dark-blue p-8 mb-4 rounded-lg flex flex-wrap gap-2 items-start justify-between">
  <div>
    <h2 class="text-2xl text-white">Publish your lesson</h2>
    <p class="mb-4">Once published, other teachers in your school will be able to see this lesson in the community library.</p>
    <%# TODO actual checks needed %>

    <div id="publishable-tasks-container">
      <% lesson_template.user_publishable_tasks.each do |task| %>
        <div class="flex gap-2 items-center" data-task-required="<%= task[:required] == true %>" data-task-complete="<%= task[:state] == true %>" data-publishable-task>
          <% color = 'text-orange-500' %>
          <% icon = 'fa-exclamation' %>
          <% if task[:state] == true %>
            <% icon = 'fa-check' %>
            <% color = 'text-green-500' %>
          <% elsif task[:required] == true %>
            <% icon = 'fa-times' %>
            <% color = 'text-red-500' %>
          <% end %>
          <span class="w-4 text-center"><i class="fas <%= icon %> <%= color %>"></i></span>
          <span><%= task[:children] %></span>
        </div>
      <% end %>
    </div>
    <script type="module">
      import {computePosition, offset, shift, autoPlacement} from 'https://cdn.jsdelivr.net/npm/@floating-ui/dom@latest/+esm';
      function handleMountTooltips() {
        const tooltipEls = document.querySelectorAll("[data-publishable-task]")
        tooltipEls.forEach(node => {
          const complete = node.getAttribute("data-task-complete") == "true"
          const required = node.getAttribute("data-task-required") == "true"

          const tooltip = document.createElement('div');
          tooltip.classList.add('tooltip');
          tooltip.textContent = complete ? "This task is complete" : required ? 'This task must be done to publish this lesson' : "This task is optional"
          tooltip.style.visibility = 'hidden'; // Ensure tooltip starts hidden
          tooltip.style.opacity = '0'; // Ensure tooltip starts fully transparent

          node.addEventListener('mouseover', function () {
              document.body.appendChild(tooltip); // Append only on mouseover
              computePosition(node, tooltip, {
                  placement: 'top-start',
                  middleware: [offset(2), shift({padding: 5})],
              }).then(({x, y}) => {
                  tooltip.style.left = `${x}px`;
                  tooltip.style.top = `${y}px`;
                  tooltip.style.visibility = 'visible';
                  tooltip.style.opacity = '1'; // Fade in
                  tooltip.style['z-index'] = '1000'; // Ensure tooltip is on top
              });
          });

          node.addEventListener('mouseout', function () {
              tooltip.style.visibility = 'hidden';
              tooltip.style.opacity = '0'; // Fade out
              document.body.removeChild(tooltip); // Remove after fade out to avoid reflow
          });
        })
      }
      document.addEventListener("DOMContentLoaded", () => {
        handleMountTooltips()
      })
    </script>
  </div>
  <div>
    <div class="flex gap-2 flex-wrap">
      <%- if lesson_template.available %>
        <%= render ButtonComponent::Base.new(
          text: "View Lesson",
          left_icon: "fa-duotone fa-solid fa-arrow-up-right-from-square",
          url: static_missions_path(lesson_template.id, {"no-redirect" => 1}),
          target: "_blank",
        )
        %>
      <% end %>
      <%= render ButtonComponent::Base.new(
          text: lesson_template.available ? "Published" : "Publish Lesson", 
          left_icon: "fa-duotone fa-solid #{lesson_template.available ? 'fa-toggle-on' : 'fa-toggle-off'}", 
          variant: :white_outline, 
          disabled: !lesson_template.user_can_publish?,
          url: lesson_template.user_can_publish? ? toggle_published_school_lesson_editing_path(lesson_template) : nil
      ) %>
      </div>
  </div>
</div>
<% tabs = [] %>
<% active_tab = action_name.gsub(/show_/, '').to_sym %>
<% tabs << { name: "Details", path: show_details_school_lesson_editing_path, active: active_tab == :details } %>
<% tabs << { name: "Lesson Plan", path: show_lesson_plan_school_lesson_editing_path, active: active_tab == :lesson_plan } %>
<% tabs << { name: "Presentation", path: show_presentation_school_lesson_editing_path, active: active_tab == :presentation } %>
<% tabs << { name: "Quiz", path: show_quiz_school_lesson_editing_path, active: active_tab == :quiz } %>
<% tabs << { name: "Keywords", path: show_keywords_school_lesson_editing_path, active: active_tab == :keywords } %>
<% tabs << { name: "Documents", path: show_documents_school_lesson_editing_path, active: active_tab == :documents } %>
<% if @current_user.beta_feature_enabled?(:september_1) %>
  <% tabs << { name: "Careers", path: show_careers_school_lesson_editing_path, active: active_tab == :careers } %>
<% end %>
<% if Rails.env.development? %>
  <% tabs << { name: "JSON Update", path: show_json_update_school_lesson_editing_path, active: active_tab == :json_update } %>
<% end %>
<% tabs << { name: "Delete", path: show_delete_school_lesson_editing_path, active: active_tab == :delete } %>

<%= render PageTabsComponent.new(tabs: tabs) %>
<%- if @current_user.beta_feature_enabled?(:ai_lesson_editing_assistant) %>
  <%= render 'ai_assistant_form' %>
<%- end %>
