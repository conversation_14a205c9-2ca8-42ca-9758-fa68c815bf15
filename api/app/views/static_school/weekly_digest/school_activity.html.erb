<div class="max-w-4xl mx-auto py-8 px-4">
  <div class="bg-white/10 rounded-xl px-8 pb-8 pt-4 border-t-8 border-t-de-brand">
    <%= render partial: 'tabs', locals: { active_tab: :school } %>

    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-white mb-2">School Activity Overview</h1>
      <p class="text-gray-300">Teaching engagement and pupil participation across your school</p>
    </div>

    <!-- Teacher Activity Section -->
    <div class="mb-12">
      <div class="flex items-center mb-6">
        <div class="bg-purple-500 w-1 h-8 rounded-full mr-4"></div>
        <h2 class="text-2xl font-bold text-white">Teacher Activity</h2>
      </div>

      <% if @teacher_stats.present? %>
        <!-- Teacher Stats Cards Summary -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @teacher_stats.sum(&:lessons_taught_count) %></div>
            <div class="text-sm opacity-90">Total Lessons</div>
          </div>
          <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @teacher_stats.sum(&:quizzes_set_count) %></div>
            <div class="text-sm opacity-90">Quizzes Set</div>
          </div>
          <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @teacher_stats.sum(&:marks_done_count) %></div>
            <div class="text-sm opacity-90">Marks Given</div>
          </div>
          <div class="bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @teacher_stats.count %></div>
            <div class="text-sm opacity-90">Active Teachers</div>
          </div>
        </div>

        <!-- Top Teachers Showcase -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
          <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-chalkboard-user text-purple-500 mr-2"></i>
            Most Active Teachers This Week
          </h3>
          <div class="space-y-3">
            <% @teacher_stats.sort_by { |stat| -(stat.lessons_taught_count + stat.quizzes_set_count + stat.marks_done_count) }.first(3).each_with_index do |stat, index| %>
              <% 
                colors = [
                  { bg: 'from-purple-50 to-purple-100', border: 'border-purple-200', badge: 'bg-purple-500' },
                  { bg: 'from-blue-50 to-blue-100', border: 'border-blue-200', badge: 'bg-blue-400' },
                  { bg: 'from-green-50 to-green-100', border: 'border-green-200', badge: 'bg-green-500' }
                ]
                color = colors[index] || { bg: 'from-gray-50 to-gray-100', border: 'border-gray-200', badge: 'bg-gray-500' }
                total_activity = stat.lessons_taught_count + stat.quizzes_set_count + stat.marks_done_count
              %>
              <div class="flex items-center justify-between p-4 bg-gradient-to-r <%= color[:bg] %> rounded-lg border <%= color[:border] %>">
                <div class="flex items-center">
                  <div class="w-10 h-10 <%= color[:badge] %> text-white rounded-full flex items-center justify-center font-bold mr-4">
                    <%= index + 1 %>
                  </div>
                  <div>
                    <div class="font-semibold text-gray-800"><%= stat.teacher.name %></div>
                    <div class="text-sm text-gray-600">
                      <%= stat.lessons_taught_count %> lessons • 
                      <%= stat.quizzes_set_count %> quizzes • 
                      <%= stat.marks_done_count %> marks
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="<%= color[:badge] %> text-white px-3 py-1 rounded-full text-sm font-bold">
                    <%= total_activity %> activities
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    <%= stat.login_count %> logins
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Detailed Teacher Table (Expandable) -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="p-4 border-b border-gray-200">
            <button onclick="toggleTeacherDetails()" class="flex items-center justify-between w-full text-left">
              <h3 class="text-lg font-semibold text-gray-800">Detailed Teacher Statistics</h3>
              <i id="teacher-chevron" class="fas fa-chevron-down text-gray-500 transition-transform"></i>
            </button>
          </div>
          <div id="teacher-details" class="hidden">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lessons</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quizzes</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marks</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Logins</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pupil Views</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pupil Quizzes</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <% @teacher_stats.each do |stat| %>
                    <tr class="hover:bg-gray-50">
                      <td class="px-4 py-3 text-sm font-medium text-gray-900"><%= stat.teacher.name %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.lessons_taught_count %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.quizzes_set_count %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.marks_done_count %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.login_count %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.presentation_view_count %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.quizzes_taken_count %></td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      <% else %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 text-center py-12">
          <i class="fas fa-chalkboard-user text-gray-400 text-4xl mb-4"></i>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">No Teacher Activity Found</h3>
          <p class="text-gray-500 max-w-md mx-auto">
            Teacher statistics for lessons taught, quizzes set, and student engagement will appear here once activity begins.
          </p>
        </div>
      <% end %>
    </div>

    <!-- Pupil Activity Section -->
    <div class="mb-8">
      <div class="flex items-center mb-6">
        <div class="bg-cyan-500 w-1 h-8 rounded-full mr-4"></div>
        <h2 class="text-2xl font-bold text-white">Pupil Engagement</h2>
      </div>

      <% if @pupil_stats.present? %>
        <!-- Pupil Stats Summary -->
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @pupil_stats.sum(&:presentation_views) %></div>
            <div class="text-sm opacity-90">Total Views</div>
          </div>
          <div class="bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @pupil_stats.sum(&:quiz_submissions) %></div>
            <div class="text-sm opacity-90">Quiz Attempts</div>
          </div>
          <div class="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl p-4 text-white text-center shadow-lg">
            <div class="text-2xl font-bold"><%= @pupil_stats.count %></div>
            <div class="text-sm opacity-90">Active Pupils</div>
          </div>
        </div>

        <!-- Top Pupils Leaderboard -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
          <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-trophy text-yellow-500 mr-2"></i>
            Top Performing Pupils This Week
          </h3>
          <div class="space-y-3">
            <% @pupil_stats.first(5).each_with_index do |stat, index| %>
              <% 
                colors = [
                  { bg: 'from-yellow-50 to-yellow-100', border: 'border-yellow-200', badge: 'bg-yellow-500' },
                  { bg: 'from-gray-50 to-gray-100', border: 'border-gray-200', badge: 'bg-gray-400' },
                  { bg: 'from-orange-50 to-orange-100', border: 'border-orange-200', badge: 'bg-orange-500' },
                  { bg: 'from-blue-50 to-blue-100', border: 'border-blue-200', badge: 'bg-blue-500' },
                  { bg: 'from-green-50 to-green-100', border: 'border-green-200', badge: 'bg-green-500' }
                ]
                color = colors[index] || { bg: 'from-purple-50 to-purple-100', border: 'border-purple-200', badge: 'bg-purple-500' }
              %>
              <div class="flex items-center justify-between p-4 bg-gradient-to-r <%= color[:bg] %> rounded-lg border <%= color[:border] %>">
                <div class="flex items-center">
                  <div class="w-10 h-10 <%= color[:badge] %> text-white rounded-full flex items-center justify-center font-bold mr-4">
                    <%= index + 1 %>
                  </div>
                  <div>
                    <div class="font-semibold text-gray-800"><%= stat.pupil.name %></div>
                    <div class="text-sm text-gray-600">
                      <%= stat.class_names %> • <%= stat.teacher_names %>
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="<%= color[:badge] %> text-white px-3 py-1 rounded-full text-sm font-bold">
                    <%= stat.activity_score %> pts
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    <%= stat.presentation_views %> views • <%= stat.quiz_submissions %> quizzes
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Detailed Pupil Table (Expandable) -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="p-4 border-b border-gray-200">
            <button onclick="togglePupilDetails()" class="flex items-center justify-between w-full text-left">
              <h3 class="text-lg font-semibold text-gray-800">All Pupil Statistics (Top 20)</h3>
              <i id="pupil-chevron" class="fas fa-chevron-down text-gray-500 transition-transform"></i>
            </button>
          </div>
          <div id="pupil-details" class="hidden">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pupil</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teachers</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quizzes</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <% @pupil_stats.each_with_index do |stat, index| %>
                    <tr class="hover:bg-gray-50">
                      <td class="px-4 py-3 text-sm font-bold text-gray-900">
                        <span class="inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold
                               <%= index < 3 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600' %>">
                          <%= index + 1 %>
                        </span>
                      </td>
                      <td class="px-4 py-3 text-sm font-medium text-gray-900"><%= stat.pupil.name %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.class_names %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.teacher_names %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.presentation_views %></td>
                      <td class="px-4 py-3 text-sm text-gray-600"><%= stat.quiz_submissions %></td>
                      <td class="px-4 py-3 text-sm font-semibold text-gray-900"><%= stat.activity_score %></td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      <% else %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 text-center py-12">
          <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">No Pupil Activity to Show</h3>
          <p class="text-gray-500 max-w-md mx-auto">
            When pupils engage with lessons by viewing presentations or completing quizzes, the most active participants will be showcased here.
          </p>
        </div>
      <% end %>
    </div>

  </div>
</div>

<script>
function toggleTeacherDetails() {
  const details = document.getElementById('teacher-details');
  const chevron = document.getElementById('teacher-chevron');
  
  if (details.classList.contains('hidden')) {
    details.classList.remove('hidden');
    chevron.classList.add('rotate-180');
  } else {
    details.classList.add('hidden');
    chevron.classList.remove('rotate-180');
  }
}

function togglePupilDetails() {
  const details = document.getElementById('pupil-details');
  const chevron = document.getElementById('pupil-chevron');
  
  if (details.classList.contains('hidden')) {
    details.classList.remove('hidden');
    chevron.classList.add('rotate-180');
  } else {
    details.classList.add('hidden');
    chevron.classList.remove('rotate-180');
  }
}
</script>