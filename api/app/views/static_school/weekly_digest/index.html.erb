<% content_for :title, "Weekly Digest" %>
<div class="max-w-4xl mx-auto py-8 px-4">
  <div class="bg-white/10 rounded-xl px-8 pb-8 pt-4 border-t-8 border-t-de-brand">
    <div class="flex justify-between">
      <%= render partial: 'tabs', locals: { active_tab: :teacher } %>
      <% if @current_user.beta_feature_enabled?(:september_1) %>
        <%= link_to 'Email Digest', send_digest_school_weekly_digest_index_path, class: 'btn btn-base btn-purple' %>
      <% end %>
    </div>
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-white mb-2">Weekly Teaching Digest</h1>
      <p class="text-gray-300">Your teaching activity summary and upcoming lessons</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
      <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-4 text-white text-center shadow-lg">
        <div class="text-2xl font-bold"><%= @past_week_stats.taught_lessons.count %></div>
        <div class="text-sm opacity-90">Lessons Taught</div>
      </div>
      <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-4 text-white text-center shadow-lg">
        <div class="text-2xl font-bold"><%= @past_week_stats.taught_units.count %></div>
        <div class="text-sm opacity-90">Units Taught</div>
      </div>
      <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-4 text-white text-center shadow-lg">
        <div class="text-2xl font-bold"><%= @past_week_stats.active_pupil_count %></div>
        <div class="text-sm opacity-90">Pupils Active</div>
      </div>
      <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-4 text-white text-center shadow-lg">
        <div class="text-2xl font-bold"><%= @past_week_stats.quiz_submission_count %></div>
        <div class="text-sm opacity-90">Quizzes Taken</div>
      </div>
    </div>

    <!-- Most Active Pupils Leaderboard -->
    <% if @past_week_stats.top_active_pupils.any? %>
      <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
          <i class="fas fa-trophy text-yellow-500 mr-2"></i>
          Most Active Pupils This Week
        </h3>
        <div class="space-y-3">
          <% @past_week_stats.top_active_pupils.each_with_index do |pupil, index| %>
            <% 
              colors = [
                { bg: 'from-yellow-50 to-yellow-100', border: 'border-yellow-200', badge: 'bg-yellow-500' },
                { bg: 'from-gray-50 to-gray-100', border: 'border-gray-200', badge: 'bg-gray-400' },
                { bg: 'from-orange-50 to-orange-100', border: 'border-orange-200', badge: 'bg-orange-500' }
              ]
              color = colors[index] || { bg: 'from-blue-50 to-blue-100', border: 'border-blue-200', badge: 'bg-blue-500' }
            %>
            <div class="flex items-center justify-between p-3 bg-gradient-to-r <%= color[:bg] %> rounded-lg border <%= color[:border] %>">
              <div class="flex items-center">
                <div class="w-8 h-8 <%= color[:badge] %> text-white rounded-full flex items-center justify-center font-bold text-sm mr-3"><%= index + 1 %></div>
                <span class="font-semibold text-gray-800"><%= pupil.name %></span>
              </div>
              <span class="<%= color[:badge] %> text-white px-3 py-1 rounded-full text-sm font-bold"><%= pupil.activity_score %> pts</span>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- This Week Section -->
    <div class="mb-12">
      <div class="flex items-center mb-6">
        <div class="bg-blue-500 w-1 h-8 rounded-full mr-4"></div>
        <h2 class="text-2xl font-bold text-white">This Week's Lessons</h2>
      </div>

      <% @current_user.forms.where(lessons: @past_week_stats.taught_lessons).each do |form| %>
        <div class="mb-8">
          <h3 class="text-xl text-white mb-4 pl-5"><%= form.name %></h3>
          
          <% @past_week_stats.taught_lessons.where(form: form).each do |lesson| %>
            <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-4">
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-0">
                <!-- Lesson Content -->
                <div class="lg:col-span-2 p-6">
                  <div class="flex flex-col lg:flex-row gap-6">
                    <div class="lg:w-1/3">
                      <a href="<%= school_lesson_path(lesson) %>">
                        <div style="background-image: url(https://www.developingexperts.com/file-cdn/images/get/<%= lesson.fileboy_image_id %>?transform=resize:300x200~fit:cover;format:webp;quality:75);" class="w-full h-40 bg-cover bg-center rounded-lg">
                          <div class="p-2">
                            <%= render SponsorIconsComponent.new(campaign_units: Lesson::Template.find(lesson[:template_id]).campaign_units.where(show_on_unit_page: true)) %>
                          </div>
                        </div>
                      </a>
                    </div>
                    <div class="lg:w-2/3">
                      <a href="<%= school_lesson_path(lesson) %>">
                        <h4 class="text-xl font-bold text-gray-800 mb-2 <%= @color %>"><%= lesson.name %></h4>
                      </a>
                      <div class="space-y-1 text-sm text-gray-600 mb-4">
                        <% lesson.objectives.each do |objective| %>
                          <p>• <%= objective %></p>
                        <% end %>
                      </div>
                      <div class="flex gap-3">
                        <a href="<%= school_lesson_path(lesson) %>" class="btn btn-base btn-cyan">
                          View Lesson
                        </a>
                        <% form_unit = @current_user.lessons.find(lesson.id).form_unit %>
                        <a href="<%= unit_library_units_path(form_unit.new_library_unit) %>" class="btn btn-base btn-purple">
                          Unit: <%= form_unit.name %>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Homework Summary -->
                <div class="bg-gray-50 p-6 border-l border-gray-200">
                  <h5 class="font-semibold text-gray-800 mb-3">Homework Set</h5>
                  <% if lesson.homeworks.empty? %>
                    <div class="text-center py-4">
                      <p class="text-gray-500 text-sm mb-2">No homework set</p>
                      <a href="/s/homework/new?lesson=<%= lesson.id %>" class="text-blue-500 text-xs font-medium hover:underline">Set homework →</a>
                    </div>
                  <% else %>
                    <div class="space-y-3">
                      <% lesson.homeworks.where(lesson: lesson).each do |homework| %>
                        <div class="bg-white p-3 rounded-lg border">
                          <div class="font-medium text-gray-800 text-sm"><%= homework.title %></div>
                          <div class="text-xs text-gray-600 mt-1">Tasks: <%= homework.tasks_count %> • Submissions: <%= homework.submissions.count %></div>
                          <a href="#" class="text-blue-500 text-xs font-medium hover:underline">View details →</a>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Upcoming Week Section -->
    <div class="mb-8">
      <div class="flex items-center mb-6">
        <div class="bg-green-500 w-1 h-8 rounded-full mr-4"></div>
        <h2 class="text-2xl font-bold text-white">Next Week's Lessons</h2>
      </div>

      <% if @upcoming_week_lessons.empty? %>
        <div class="text-center bg-white/10 rounded-xl py-8 px-6">
          <i class="fas fa-calendar-week text-3xl text-gray-400 mb-3"></i>
          <h3 class="text-lg font-semibold text-white mb-2">No Upcoming Lessons Scheduled</h3>
          <p class="text-gray-300 text-sm">This space will show all your lessons planned for the next 7 days.</p>
        </div>
      <% else %>
        <% @current_user.forms.where(lessons: @upcoming_week_lessons).each do |form| %>
          <div class="mb-8">
            <h3 class="text-xl text-white mb-4 pl-5"><%= form.name %></h3>
            
            <% @upcoming_week_lessons.where(form: form).order(time: :asc).each do |lesson| %>
              <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-4">
                <div class="p-6">
                  <div class="flex flex-col lg:flex-row gap-6">
                    <!-- Lesson Info -->
                    <div class="lg:w-2/3">
                      <div class="flex gap-4">
                        <a href="<%= school_lesson_path(lesson) %>">
                          <div style="background-image: url(https://www.developingexperts.com/file-cdn/images/get/<%= lesson.fileboy_image_id %>?transform=resize:200x120~fit:cover;format:webp;quality:75);" class="w-24 h-16 bg-cover bg-center rounded-lg flex-shrink-0">
                          </div>
                        </a>
                        <div>
                          <a href="<%= school_lesson_path(lesson) %>">
                            <h4 class="text-lg font-bold text-gray-800 mb-1 underline leading-tight <%= @color %>"><%= lesson.name %></h4>
                          </a>
                          <p class="text-sm text-gray-600 mb-2">Scheduled for <%= lesson.time.strftime("%A, %d %B") %></p>
                          <div class="text-xs text-gray-500 space-y-1">
                            <% lesson.objectives.first(2).each do |objective| %>
                              <p>• <%= objective %></p>
                            <% end %>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Homework Status -->
                      <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                        <div class="text-sm text-gray-600">
                          <% if lesson.homeworks.empty? %>
                            <strong>Homework:</strong> No homework currently set • 
                            <a href="/s/homework/new?lesson=<%= lesson.id %>" class="text-blue-500 hover:underline">Set homework</a>
                          <% else %>
                            <strong>Homework:</strong> <%= lesson.homeworks.count %> assignment<%= 's' if lesson.homeworks.count > 1 %> set • 
                            <a href="#" class="text-blue-500 hover:underline">View details</a>
                          <% end %>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Downloads Section - More Prominent -->
                    <% if lesson.template.documents.any? %>
                      <div class="lg:w-1/3 bg-blue-50 p-4 rounded-lg">
                        <h5 class="font-semibold text-gray-800 mb-3 flex items-center">
                          <i class="fas fa-download text-blue-500 mr-2"></i>
                          Downloads
                        </h5>
                        <div class="space-y-2">
                          <% lesson.template.documents.each do |document| %>
                            <% if @current_user.present? %>
                              <%= link_to document.fileboy_url({ user_id: @current_user&.id }), class: "flex items-center justify-between p-2 bg-white rounded border hover:bg-gray-50", target: "_blank", download: true do %>
                                <span class="text-sm font-medium text-gray-700"><%= document.name %></span>
                                <i class="fas fa-file-pdf text-red-500"></i>
                              <% end %>
                            <% else %>
                              <div class="flex items-center justify-between p-2 bg-white rounded border opacity-50">
                                <span class="text-sm font-medium text-gray-700"><%= document.name %></span>
                                <i class="fas fa-file-pdf text-red-500"></i>
                              </div>
                            <% end %>
                          <% end %>
                        </div>
                      </div>
                    <% else %>
                      <div class="lg:w-1/3 bg-gray-50 p-4 rounded-lg">
                        <h5 class="font-semibold text-gray-800 mb-3 flex items-center">
                          <i class="fas fa-download text-gray-400 mr-2"></i>
                          Downloads
                        </h5>
                        <p class="text-sm text-gray-500">No downloads available for this lesson</p>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>

  </div>
</div>