# app/services/discount_service.rb
# Handles validation and application of discount codes for subscriptions.
# Provides functionality to verify discount codes, check their validity conditions,
# and apply them to subscribers.
class DiscountService
  # @param subscriber [Subscriber] The subscriber to check or apply discounts for
  def initialize(subscriber)
    @subscriber = subscriber
  end

  # Validates if a discount code is eligible for use by the current subscriber
  #
  # Checks various conditions including:
  # - Code existence in the database
  # - Active status
  # - Expiration date
  # - Maximum redemption limits
  # - Previous usage by this subscriber
  # - Stripe coupon existence
  #
  # @param code [String] The discount code to validate
  # @return [Hash] Validation result with keys:
  #   - valid: [<PERSON><PERSON><PERSON>] Whether the code is valid
  #   - message: [String] Error message if invalid
  #   - discount_code: [DiscountCode] The discount code object if valid
  #   - amount: [Integer] Discount amount if valid
  #   - discount_type: [String] Type of discount ('percentage' or 'fixed_amount') if valid
  #   - formatted_discount: [String] Human-readable discount representation if valid
  def validate_discount_code(code)
    return { valid: false, message: 'Discount code cannot be blank' } if code.blank?

    # Convert to uppercase for consistent lookup
    code = code.strip.upcase

    # Find the discount code in our database
    discount_code = DiscountCode.find_by(code: code)

    if discount_code.nil?
      Rails.logger.info "Invalid discount code attempted: #{code}"
      return { valid: false, message: 'Invalid discount code' }
    end

    # Check if the code is active
    unless discount_code.active?
      Rails.logger.info "Inactive discount code attempted: #{code}"
      return { valid: false, message: 'This discount code is no longer active' }
    end

    # Check if the code has expired
    if discount_code.expired?
      Rails.logger.info "Expired discount code attempted: #{code}, expires_at: #{discount_code.expires_at}"
      return { valid: false, message: 'This discount code has expired' }
    end

    # Check if all redemptions are used
    if discount_code.redemptions_exhausted?
      Rails.logger.info "Exhausted discount code attempted: #{code}, max_redemptions: #{discount_code.max_redemptions}, current redemptions: #{discount_code.discount_code_redemptions.count}"
      return { valid: false, message: 'This discount code has reached its maximum number of redemptions' }
    end

    # Check if this subscriber has already redeemed this code
    if discount_code.discount_code_redemptions.exists?(subscriber: @subscriber)
      Rails.logger.info "Already used discount code: #{code} by subscriber #{@subscriber.id}"
      return { valid: false, message: 'You have already used this discount code' }
    end

    # Check if the discount type is valid (additional validation)
    unless %w[percentage fixed_amount].include?(discount_code.discount_type)
      Rails.logger.error "Invalid discount type in code #{code}: #{discount_code.discount_type}"
      return { valid: false, message: 'This discount code has an invalid discount type' }
    end

    # Validate amount based on type
    if discount_code.discount_type == 'percentage' && (discount_code.amount <= 0 || discount_code.amount > 100)
      Rails.logger.error "Invalid percentage amount in code #{code}: #{discount_code.amount}"
      return { valid: false, message: 'This discount code has an invalid percentage value' }
    end

    if discount_code.discount_type == 'fixed_amount' && discount_code.amount <= 0
      Rails.logger.error "Invalid fixed amount in code #{code}: #{discount_code.amount}"
      return { valid: false, message: 'This discount code has an invalid amount value' }
    end

    # Verify Stripe coupon exists
    begin
      stripe_coupon_id = discount_code.stripe_coupon_id
      Stripe::Coupon.retrieve(stripe_coupon_id)
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe coupon not found for discount code #{code}: #{e.message}"
      return { valid: false, message: 'This discount code cannot be applied at this time. Please try again later or contact support.' }
    end

    # Code is valid
    Rails.logger.info "Valid discount code: #{code} for subscriber #{@subscriber.id}"
    {
      valid: true,
      discount_code: discount_code,
      amount: discount_code.amount,
      discount_type: discount_code.discount_type,
      description: discount_code.description,
      formatted_discount: discount_code.formatted_discount
    }
  rescue => e
    Rails.logger.error "Error validating discount code #{code}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    { valid: false, message: 'An error occurred while validating this discount code. Please try again later.' }
  end

  # Applies a validated discount code to the subscriber
  #
  # Validates the code first, then records the redemption in the database
  # using a transaction to prevent race conditions with concurrent redemptions
  #
  # @param code [String] The discount code to apply
  # @return [Hash] Application result with keys:
  #   - success: [Boolean] Whether the code was successfully applied
  #   - message: [String] Success or error message
  #   - discount_code: [DiscountCode] The applied discount code if successful
  #   - stripe_coupon_id: [String] Stripe coupon ID if successful
  def apply_discount_code(code)
    result = validate_discount_code(code)

    if result[:valid]
      discount_code = result[:discount_code]

      # Redeem the code in a database transaction to avoid race conditions
      success = false
      DiscountCode.transaction do
        # Re-check conditions inside transaction to prevent race conditions
        raise ActiveRecord::Rollback, 'Discount code is no longer valid' unless discount_code.redeemable?

        # Redeem the code
        raise ActiveRecord::Rollback, 'Failed to redeem discount code' unless discount_code.redeem!(@subscriber)
        success = true
      end

      if success
        Rails.logger.info "Successfully applied discount code #{code} for subscriber #{@subscriber.id}"
        {
          success: true,
          message: 'Discount code applied successfully',
          discount_code: discount_code,
          stripe_coupon_id: discount_code.stripe_coupon_id,
          discount_code_redemption_id: discount_code.discount_code_redemptions.where(subscriber: @subscriber).last.id,
        }
      else
        Rails.logger.warn "Failed to redeem discount code #{code} for subscriber #{@subscriber.id}"
        { success: false, message: 'Failed to redeem discount code' }
      end
    else
      Rails.logger.info "Invalid discount code #{code} could not be applied: #{result[:message]}"
      { success: false, message: result[:message] }
    end
  rescue => e
    Rails.logger.error "Error applying discount code #{code}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    { success: false, message: 'An error occurred while applying this discount code. Please try again later.' }
  end

  def rollback_code_redemption(discount_code_redemption_id)
    discount_code_redemption = DiscountCodeRedemption.find_by(id: discount_code_redemption_id, subscriber: @subscriber)
    return { success: false, message: 'Redemption not found for this subscriber' } unless discount_code_redemption

    discount_code_redemption.destroy
    Rails.logger.info "Rolled back discount code redemption ID #{discount_code_redemption_id} for subscriber #{@subscriber.id}"
    { success: true, message: 'Discount code redemption rolled back successfully' }
  rescue => e
    Rails.logger.error "Error rolling back discount code redemption ID #{discount_code_redemption_id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    { success: false, message: 'An error occurred while rolling back the discount code redemption. Please contact support.' }
  end

  # Gets the active discount code (if any) for the subscriber
  #
  # Retrieves the most recently applied discount code for the subscriber
  #
  # @return [Hash, nil] Details of the active discount or nil if none found
  def get_active_discount
    redemption = DiscountCodeRedemption.where(subscriber: @subscriber)
                                       .order(created_at: :desc)
                                       .first

    if redemption&.discount_code
      discount_code = redemption.discount_code

      return {
        discount_code: discount_code,
        stripe_coupon_id: discount_code.stripe_coupon_id,
        amount: discount_code.amount,
        discount_type: discount_code.discount_type,
        formatted_discount: discount_code.formatted_discount
      }
    end

    nil
  end
end
