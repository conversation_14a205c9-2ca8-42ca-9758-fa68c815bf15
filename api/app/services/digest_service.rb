class DigestService
  # Define structs for each set of data
  TeacherStats = Struct.new(
    :teacher,
    :lessons_taught_count,
    :quizzes_set_count,
    :marks_done_count,
    :presentation_view_count,
    :quizzes_taken_count,
    :login_count,
    keyword_init: true
  )

  PupilStats = Struct.new(
    :pupil,
    :presentation_views,
    :quiz_submissions,
    :activity_score,
    :class_names,
    :teacher_names,
    keyword_init: true
  )

  PastWeekStats = Struct.new(
    :taught_lessons,
    :taught_units,
    :active_pupil_count,
    :presentation_view_count,
    :quiz_submission_count,
    :top_active_pupils,
    :set_homeworks,
    :set_homeworks_count,
    keyword_init: true
  )

  def initialize(user)
    @current_user = user
  end

  def personal
    pupil_ids_for_teacher = @current_user.pupil_ids

    # Get taught lessons from teacher lessons with at least one view
    taught_lessons = @current_user.lessons
                                  .where(time: past_week_range)
                                  .joins(:tracking_lesson_template_views)
                                  .includes(:template)
                                  .distinct

    # Get taught units from teacher form units with at least one lesson view
    taught_units = @current_user.form_units
                                .where(lesson_lessons: taught_lessons)
                                .where(start_date: past_week_range)
                                .joins(lesson_lessons: :tracking_lesson_template_views)
                                .includes(:new_library_unit)
                                .distinct

    # Get info for homeworks including counts
    set_homeworks = Homework
                    .joins(lesson: :form)
                    .where(lesson: taught_lessons)
                    .left_outer_joins(:tasks, :submissions)
                    .group('homeworks.id')
                    .select(
                      'homeworks.*',
                      'COUNT(DISTINCT homework_tasks.id) AS tasks_count',
                      'COUNT(DISTINCT homework_task_submissions.id) AS submissions_count'
                    )
    set_homeworks_count = set_homeworks.length

    # Get presentation view count and quiz submission count
    presentation_views = TrackingPresentationView.where(user_id: pupil_ids_for_teacher, created_at: past_week_range).count
    quiz_submissions = QuipQuizResult.where(pupil_id: pupil_ids_for_teacher, created_at: past_week_range).count

    @past_week_stats = PastWeekStats.new(
      taught_lessons: taught_lessons,
      taught_units: taught_units,
      active_pupil_count: Pupil
        .joins(:active_days)
        .where(id: pupil_ids_for_teacher)
        .where(active_days: { date: past_week_range })
        .distinct
        .count,
      presentation_view_count: presentation_views,
      quiz_submission_count: quiz_submissions,
      set_homeworks: set_homeworks,
      set_homeworks_count: set_homeworks_count,
      top_active_pupils: calculate_top_active_pupils(pupil_ids_for_teacher, limit: 5, date_range: past_week_range)
    )

    @upcoming_week_lessons = Lesson::Lesson
                             .joins(form: { enrollments: :user })
                             .includes(template: :documents)
                             .where(time: upcoming_week_range, users: { id: @current_user.id })
                             .order(:time)
    OpenStruct.new(
      past_week_stats: @past_week_stats,
      upcoming_week_lessons: @upcoming_week_lessons
    )
  end

  def school
    # Load teacher stats
    teachers = @current_user.school.teachers
    teacher_ids = teachers.map(&:id)

    teacher_pupil_ids = Teacher
                        .joins(forms: :pupils)
                        .where(users: { id: teacher_ids })
                        .group('users.id')
                        .pluck('users.id', Arel.sql('ARRAY_AGG(DISTINCT pupils_forms.id)'))

    pupil_ids_by_teacher = teacher_pupil_ids.to_h
    all_school_pupil_ids = pupil_ids_by_teacher.values.flatten.uniq

    activity_scores_data = calculate_pupil_activity_scores(all_school_pupil_ids, date_range: past_week_range)

    # Fetch lessons taught counts
    lessons_taught_counts = if teacher_ids.empty?
                              {}
                            else
                              scope = TrackingLessonTemplateViewed.joins(lesson: { form: { enrollments: :user } })
                                                                  .where(users: { id: teacher_ids })
                                                                  .where(lesson_lessons: { time: past_week_range })
                                                                  .where(created_at: past_week_range)
                              scope.group('users.id').distinct.count(:lesson_id)
                            end

    # Fetch quizzes set counts
    quizzes_set_counts = if teacher_ids.empty?
                           {}
                         else
                           HomeworkTask.joins(:homework)
                                       .where(task_type: %w[quiz lesson_quiz])
                                       .where(homeworks: { created_by_id: teacher_ids, date_set: past_week_range })
                                       .group('homeworks.created_by_id')
                                       .count
                         end

    # Fetch marks done counts
    marks_done_counts = if all_school_pupil_ids.empty?
                          {}
                        else
                          lesson_marks = PupilLessonMark.where(user_id: all_school_pupil_ids, created_at: past_week_range)
                                                        .group(:user_id).count
                          unit_marks = PupilUnitMark.where(user_id: all_school_pupil_ids, created_at: past_week_range)
                                                    .group(:user_id).count

                          total_marks_per_pupil = Hash.new(0)
                          lesson_marks.each { |id, count| total_marks_per_pupil[id] += count }
                          unit_marks.each { |id, count| total_marks_per_pupil[id] += count }

                          marks_by_teacher = Hash.new(0)
                          pupil_ids_by_teacher.each do |teacher_id, pupil_ids|
                            marks_by_teacher[teacher_id] = pupil_ids.sum { |pid| total_marks_per_pupil.fetch(pid, 0) }
                          end
                          marks_by_teacher
                        end

    @teacher_stats = teachers.map do |teacher|
      ids = pupil_ids_by_teacher.fetch(teacher.id, [])
      TeacherStats.new(
        teacher: teacher,
        lessons_taught_count: lessons_taught_counts.fetch(teacher.id, 0),
        quizzes_set_count: quizzes_set_counts.fetch(teacher.id, 0),
        marks_done_count: marks_done_counts.fetch(teacher.id, 0),
        presentation_view_count: activity_scores_data[:views].slice(*ids).values.sum,
        quizzes_taken_count: activity_scores_data[:submissions].slice(*ids).values.sum,
        login_count: teacher.active_days.where(date: past_week_range).count
      )
    end.sort_by! { |stat| stat.teacher.name }

    # Load pupil stats - top 20 pupils
    top_pupils_with_scores = calculate_top_active_pupils(all_school_pupil_ids, limit: 20, date_range: past_week_range)

    pupil_ids = top_pupils_with_scores.map(&:id)
    pupils_with_associations = Pupil.where(id: pupil_ids).eager_load(forms: :teachers)
    pupil_lookup = pupils_with_associations.index_by(&:id)

    @pupil_stats = top_pupils_with_scores.map do |pupil_with_score|
      pupil = pupil_lookup[pupil_with_score.id]
      next unless pupil

      PupilStats.new(
        pupil: pupil,
        presentation_views: pupil_with_score.presentation_views,
        quiz_submissions: pupil_with_score.quiz_submissions,
        activity_score: pupil_with_score.activity_score,
        class_names: pupil.forms.map(&:name).join(', '),
        teacher_names: pupil.forms.flat_map(&:teachers).uniq.map(&:name).join(', ')
      )
    end.compact

    OpenStruct.new(
      teacher_stats: @teacher_stats,
      pupil_stats: @pupil_stats
    )
  end

  private

  def past_week_range
    start = 7.days.ago.beginning_of_week
    @past_week_range ||= start..start.end_of_week
  end

  def upcoming_week_range
    start = Time.current.beginning_of_week
    @upcoming_week_range ||= start..start.end_of_week
  end

  # Get pupil activity count based on presentation views + quiz submissions
  def calculate_pupil_activity_scores(pupil_ids, date_range: nil)
    return { views: {}, submissions: {}, combined: {} } if pupil_ids.empty?

    views_scope = TrackingPresentationView.where(user_id: pupil_ids)
    submissions_scope = QuipQuizResult.where(pupil_id: pupil_ids)

    if date_range
      views_scope = views_scope.where(created_at: date_range)
      submissions_scope = submissions_scope.where(created_at: date_range)
    end

    views = Hash.new(0).merge(views_scope.group(:user_id).count)
    submissions = Hash.new(0).merge(submissions_scope.group(:pupil_id).count)

    all_ids = (views.keys + submissions.keys).uniq
    combined = Hash.new(0)
    all_ids.each { |id| combined[id] = views[id] + submissions[id] }

    { views: views, submissions: submissions, combined: combined }
  end

  # Find most active pupils
  def calculate_top_active_pupils(pupil_ids, limit: 5, date_range: nil)
    activity_data = calculate_pupil_activity_scores(pupil_ids, date_range: date_range)
    return [] if activity_data[:combined].empty?

    views_scores = activity_data[:views]
    submissions_scores = activity_data[:submissions]
    combined_scores = activity_data[:combined]

    top_pupil_ids = combined_scores.sort_by { |_id, score| -score }.first(limit).map(&:first)

    return [] if top_pupil_ids.empty?

    activity_score_sql = 'CASE users.id ' + top_pupil_ids.map { |id| "WHEN #{id} THEN #{combined_scores[id]}" }.join(' ') + ' ELSE 0 END'
    views_score_sql = 'CASE users.id ' + top_pupil_ids.map { |id| "WHEN #{id} THEN #{views_scores[id]}" }.join(' ') + ' ELSE 0 END'
    submissions_score_sql = 'CASE users.id ' + top_pupil_ids.map { |id| "WHEN #{id} THEN #{submissions_scores[id]}" }.join(' ') + ' ELSE 0 END'

    Pupil.where(id: top_pupil_ids)
         .select(
           'users.*',
           "(#{activity_score_sql}) AS activity_score",
           "(#{views_score_sql}) AS presentation_views",
           "(#{submissions_score_sql}) AS quiz_submissions"
         )
         .order('activity_score DESC')
  end
end
