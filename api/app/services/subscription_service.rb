# frozen_string_literal: true
# app/services/subscription_service.rb
# Handles all Stripe subscription-related operations, including creation, modification,
# cancellation, and retrieval of subscription data. Supports both individual (immediate payment)
# and school (invoice-based) payment flows.
require Rails.root.join('lib', 'utils', 'error_logger')
class SubscriptionService
  include ErrorLogger
  require_dependency 'stripe_products'

  # @param subscribable [User, School] The entity (user or school) to be subscribed
  # @return [SubscriptionService] A new service instance tied to a subscriber record
  def initialize(subscribable)
    @subscriber = subscribable.get_or_create_subscriber
    @stripe_env = Rails.configuration.stripe[:environment]
  end

  # Creates a new subscription with the selected products and size
  #
  # @param product_keys [Array<Symbol>] Product keys (e.g., [:science, :geography])
  # @param plan_key [Symbol] Plan key (e.g., :individual, :individual_monthly, :small)
  # @param return_url [String, nil] URL to return to after checkout (required for individual subscriptions)
  # @param discount_code [String, nil] Optional discount code to apply
  # @return [Hash] Result containing checkout URL or invoice details
  def create_subscription(product_keys, plan_key, return_url = nil, discount_code = nil, no_notifications: false)
    @subscriber.update(silence_admin_notification: no_notifications)
    customer = find_or_create_stripe_customer
    discount_code_redemption_id = nil

    begin
      price_ids = product_keys.map do |product_key|
        price_id = StripeProducts.get_price_id(product_key, plan_key, @stripe_env)
        Rails.logger.info "Retrieved price ID for #{product_key}, #{plan_key}: #{price_id}"

        raise ArgumentError, "Could not find price ID for product #{product_key} and plan #{plan_key}" if price_id.nil?

        price_id
      end

      discounts = []

      if product_keys.length > 1
        bundle_discount = StripeProducts.get_discount_coupon_id(product_keys.length, @stripe_env)
        if bundle_discount
          discounts.push({ coupon: bundle_discount })
          Rails.logger.info "Adding bundle discount coupon: #{bundle_discount}"
        end
      end

      if discount_code.present?
        discount_service = DiscountService.new(@subscriber)
        discount_result = discount_service.apply_discount_code(discount_code)
        discount_code_redemption_id = discount_result[:discount_code_redemption_id] if discount_result[:success]

        if discount_result[:success]
          custom_coupon_id = discount_result[:stripe_coupon_id]
          discounts.push({ coupon: custom_coupon_id })
          Rails.logger.info "Applied discount code: #{discount_code}, Stripe coupon: #{custom_coupon_id}"
        else
          error_message = discount_result[:message]
          Rails.logger.warn "Failed to apply discount code: #{discount_code}, Error: #{error_message}"
          raise StandardError, "Discount code error: #{error_message}"
        end
      end

      formatted_discounts = discounts.map { |d| { coupon: d[:coupon] } }

      # Different flow for individual vs school subscriptions
      is_individual_base_plan = (StripeProducts.get_base_size_from_plan_key(plan_key) == :individual)

      # create a new discount with for the combined coupon + bundle and overwrite the fmt discounts
      # if we have multiple discounts for a checkout flow.
      formatted_discounts = calculate_collective_discount(discounts, price_ids) if is_individual_base_plan && return_url.present? && discounts.length > 1
      if is_individual_base_plan && return_url.present? # This now correctly handles :individual and :individual_monthly
        create_checkout_session(customer, price_ids, product_keys, plan_key, return_url, formatted_discounts)
      else
        create_invoice_subscription(customer, price_ids, product_keys, plan_key, formatted_discounts)
      end
    rescue ArgumentError => e
      Rails.logger.error "Invalid arguments when creating subscription: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      discount_service&.rollback_code_redemption(discount_code_redemption_id) if discount_code_redemption_id.present?
      raise e
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error when creating subscription: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      discount_service&.rollback_code_redemption(discount_code_redemption_id) if discount_code_redemption_id.present?

      error_message = case e.code
                      when 'card_declined'
                        'Your card was declined. Please try another payment method.'
                      when 'expired_card'
                        'Your card has expired. Please update your payment method.'
                      when 'insufficient_funds'
                        'Your card has insufficient funds. Please try another payment method.'
                      when 'rate_limit'
                        "We're processing too many requests right now. Please try again in a few moments."
                      else
                        "Payment processing error: #{e.message}"
                      end

      raise StandardError, error_message
    rescue => e
      Rails.logger.error "Error when creating subscription: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      discount_service&.rollback_code_redemption(discount_code_redemption_id) if discount_code_redemption_id.present?
      raise e
    end
  end

  def calculate_collective_discount(discounts, price_ids)
    prices = price_ids.map { |id| Stripe::Price.retrieve(id) }
    coupons = discounts.map { |d| Stripe::Coupon.retrieve(d[:coupon]) }

    # Calculate the total price before any discounts
    total_price = prices.sum(&:unit_amount)

    # Apply each discount sequentially to calculate the final amount
    discounted_amount = total_price

    coupons.each do |coupon|
      if coupon.percent_off
        # Apply percentage discount
        discount_value = (discounted_amount * coupon.percent_off / 100).round
        discounted_amount -= discount_value
      elsif coupon.amount_off
        # Apply fixed amount discount
        discounted_amount -= coupon.amount_off
      end

      # Ensure we don't go below zero
      discounted_amount = [discounted_amount, 0].max
    end

    # Calculate the total discount amount
    total_discount_amount = total_price - discounted_amount

    # Create a new coupon with the combined discount
    combined = create_combined_discount_coupon(total_discount_amount)
    [{ coupon: combined }]
  rescue Stripe::StripeError => e
    handle_error_log(e, 'calculate_collective_discount')
    raise
  rescue => e
    handle_error_log(e, 'calculate_collective_discount')
    raise
  end

  # Creates a Stripe checkout session for immediate payment (used for individual subscriptions)
  #
  # @param customer [Stripe::Customer] The Stripe customer object
  # @param price_ids [Array<String>] Stripe price IDs for the selected products
  # @param product_keys [Array<Symbol>] Product keys used for description generation
  # @param plan_key [Symbol] Plan key used for description generation
  # @param return_url [String] URL to redirect to after checkout
  # @param discounts [Array<Hash>] Array of discount coupon objects to apply
  # @return [Hash] Contains checkout session object and URL
  def create_checkout_session(customer, price_ids, product_keys, plan_key, base_return_url, discounts = [])
    line_items = price_ids.map do |price_id|
      { price: price_id, quantity: 1 }
    end

    session_params = {
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: line_items,
      mode: 'subscription',
      success_url: "#{base_return_url}?success=true&session_id={CHECKOUT_SESSION_ID}", 
      cancel_url: "#{base_return_url}?canceled=true",
      automatic_tax: { enabled: true },
      subscription_data: {
        metadata: {
          subscriber_id: @subscriber.id,
          environment: @stripe_env.to_s
        },
        description: generate_subscription_description(product_keys, plan_key)
      }
    }

    if discounts.present? && discounts.any?
      session_params[:discounts] = discounts
      Rails.logger.info "Adding discounts to checkout: #{discounts.inspect}"
    end

    session = Stripe::Checkout::Session.create(session_params)

    @subscriber.update(subscription_status: 'pending')

    {
      checkout_session: session,
      checkout_url: session.url
    }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating checkout session: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")

    error_message = case e.code
                    when 'resource_missing'
                      'One of the products or prices could not be found. Please contact support.'
                    when 'parameter_invalid'
                      'Invalid parameters for checkout. Please contact support.'
                    else
                      "Error creating checkout session: #{e.message}"
                    end

    raise StandardError, error_message
  rescue => e
    Rails.logger.error "Error creating checkout session: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end

  # Processes a completed Stripe checkout session and updates subscriber record
  #
  # @param session_id [String] The Stripe checkout session ID
  # @return [Hash] Success status and either subscription details or error message
  def complete_checkout(session_id)
    session = Stripe::Checkout::Session.retrieve({
                                                   id: session_id,
                                                   expand: ['subscription']
                                                 })

    return { success: false, error: 'No subscription found in checkout session' } unless session.subscription

    @subscriber.update(
      stripe_subscription_id: session.subscription.id,
      subscription_status: session.subscription.status
    )

    {
      success: true,
      subscription: session.subscription
    }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error completing checkout: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")

    {
      success: false,
      error: "Error processing payment: #{e.message}"
    }
  rescue => e
    Rails.logger.error "Error completing checkout: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")

    {
      success: false,
      error: "Error completing subscription: #{e.message}"
    }
  end

  # Creates a subscription with invoice-based billing (used for school subscriptions)
  #
  # @param customer [Stripe::Customer] The Stripe customer object
  # @param price_ids [Array<String>] Stripe price IDs for the selected products
  # @param product_keys [Array<Symbol>] Product keys used for description generation
  # @param plan_key [Symbol] Plan key used for description generation
  # @param discounts [Array<Hash>] Array of discount coupon objects to apply
  # @return [Hash] Contains subscription object and invoice
  def create_invoice_subscription(customer, price_ids, product_keys, plan_key, discounts = [])
    subscription_params = {
      customer: customer.id,
      items: price_ids.map { |price_id| { price: price_id } },
      collection_method: 'send_invoice',
      days_until_due: 30,
      description: generate_subscription_description(product_keys, plan_key),
      automatic_tax: { enabled: true },
      metadata: {
        subscriber_id: @subscriber.id,
        environment: @stripe_env.to_s
      }
    }

    if discounts.present? && discounts.any?
      subscription_params[:discounts] = discounts
      Rails.logger.info "Adding discounts to subscription: #{discounts.inspect}"
    end

    subscription = Stripe::Subscription.create(subscription_params)

    puts "Created subscription with ID: #{subscription.id}, status: #{subscription.status}"
    puts "Created invoice subscription for subscriber #{@subscriber.id} with ID: #{subscription.id}, status: #{subscription.status}"

    @subscriber.update_columns(
      stripe_subscription_id: subscription.id,
      subscription_status: subscription.status
    )

    if subscription.latest_invoice
      invoice = Stripe::Invoice.retrieve(subscription.latest_invoice)

      if invoice.status == 'draft'
        Rails.logger.info "Finalizing invoice #{invoice.id}"
        invoice = Stripe::Invoice.finalize_invoice(invoice.id)
      end

      Rails.logger.info "Sending invoice #{invoice.id}"
      begin
        invoice = Stripe::Invoice.send_invoice(invoice.id)
        Rails.logger.info "Invoice sent, new status: #{invoice.status}"
      rescue Stripe::StripeError => e
        handle_error_log(e, 'create_invoice_subscription#send_invoice')
      end

      return {
        subscription: subscription,
        invoice: invoice
      }
    end

    { subscription: subscription, invoice: nil }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating invoice subscription: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")

    error_message = case e.code
                    when 'resource_missing'
                      'One of the products or prices could not be found. Please contact support.'
                    when 'parameter_invalid'
                      'Invalid parameters for subscription. Please contact support.'
                    else
                      "Error creating subscription: #{e.message}"
                    end

    raise StandardError, error_message
  rescue => e
    Rails.logger.error "Error creating invoice subscription: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end

  # Marks an invoice as paid out of band.
  # This is used when payment is received outside of Stripe (e.g., bank transfer, check).
  #
  # @param invoice_id [String] The ID of the Stripe invoice to mark as paid.
  # @return [Hash] Contains success status and the invoice object or an error message.
  def mark_invoice_as_paid(invoice_id)
    unless invoice_id.present?
      Rails.logger.warn 'mark_invoice_as_paid called with no invoice_id.'
      return { success: false, error: 'Invoice ID cannot be blank.' }
    end

    begin
      invoice = Stripe::Invoice.retrieve(invoice_id)

      if invoice.paid
        Rails.logger.info "Invoice #{invoice_id} is already marked as paid."
        return { success: true, invoice: invoice, message: 'Invoice already paid.' }
      end

      if invoice.status == 'void'
        Rails.logger.info "Invoice #{invoice_id} is void and cannot be marked as paid."
        return { success: false, invoice: invoice, error: 'Invoice is void.' }
      end

      if invoice.status == 'draft'
        Rails.logger.info "Invoice #{invoice_id} is a draft. Finalizing before marking as paid."
        invoice = Stripe::Invoice.finalize_invoice(invoice.id)
      end

      # Only attempt to pay if the invoice is 'open'
      if invoice.status == 'open'
        paid_invoice = Stripe::Invoice.pay(invoice_id, { paid_out_of_band: true })
        Rails.logger.info "Successfully marked invoice #{invoice_id} as paid out of band."

        # Optionally, update local subscriber status if payment implies subscription activation
        if @subscriber && paid_invoice.subscription
          subscription = Stripe::Subscription.retrieve(paid_invoice.subscription)
          @subscriber.update(subscription_status: subscription.status)
          Rails.logger.info "Updated subscriber #{@subscriber.id} status to #{subscription.status} after invoice payment."
        end
        { success: true, invoice: paid_invoice }
      else
        Rails.logger.warn "Invoice #{invoice_id} is not open (status: #{invoice.status}). Cannot mark as paid."
        { success: false, invoice: invoice, error: "Invoice is not open (status: #{invoice.status}). Cannot mark as paid." }
      end

    rescue Stripe::InvalidRequestError => e
      if e.code == 'invoice_payment_intent_requires_action'
         Rails.logger.warn "Invoice #{invoice_id} has a payment intent that requires action. It might need to be paid online or the payment intent handled differently. Error: #{e.message}"
         return { success: false, error: "Invoice has a payment intent that requires action: #{e.message}"}
      end
      Rails.logger.error "Stripe invalid request error marking invoice #{invoice_id} as paid: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      { success: false, error: "Stripe error: #{e.message}" }
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error marking invoice #{invoice_id} as paid: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      { success: false, error: "Stripe error: #{e.message}" }
    rescue => e
      Rails.logger.error "Error marking invoice #{invoice_id} as paid: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      { success: false, error: "An unexpected error occurred: #{e.message}" }
    end
  end


  # Finalizes and sends the most recent invoice for the subscriber
  #
  # Checks if a draft invoice exists, finalizes it, and sends it to the customer
  #
  # @return [Stripe::Invoice, nil] The finalized invoice or nil if not found/error
  def finalize_and_send_latest_invoice
    return nil unless @subscriber.stripe_subscription_id

    begin
      subscription = Stripe::Subscription.retrieve(@subscriber.stripe_subscription_id)
      return nil unless subscription.latest_invoice

      invoice = Stripe::Invoice.retrieve(subscription.latest_invoice)

      if invoice.status == 'draft'
        Rails.logger.info "Finalizing draft invoice: #{invoice.id}"
        invoice = Stripe::Invoice.finalize_invoice(invoice.id)
      end

      if invoice.status == 'open' && !invoice.sent_at
        Rails.logger.info "Sending invoice: #{invoice.id}"
        invoice = Stripe::Invoice.send_invoice(invoice.id)
        Rails.logger.info "Invoice sent, new status: #{invoice.status}"
      end

      invoice
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error finalizing/sending invoice: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      nil
    rescue => e
      Rails.logger.error "Error finalizing/sending invoice: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      nil
    end
  end

  # Retrieves detailed subscription information from Stripe
  #
  # Fetches the subscription with expanded price and product information
  #
  # @return [Stripe::Subscription, nil] The subscription or nil if not found/error
  def get_subscription_details
    return nil unless @subscriber.stripe_subscription_id

    Rails.logger.info "Retrieving subscription details for subscriber #{@subscriber.stripe_subscription_id.inspect}"

    Rails.logger.debug do
      masked_key = Stripe.api_key ? Stripe.api_key[0..5] : 'nil'
      "Stripe call happening with key starting: #{masked_key}... | Env: #{Rails.configuration.stripe[:environment]}"
    end

    begin
      Stripe::Subscription.retrieve({ 
        id: @subscriber.stripe_subscription_id, 
        expand: ['items.data.price.product', 'latest_invoice.payment_intent'] 
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error retrieving subscription details: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      nil
    rescue => e
      Rails.logger.error "Error retrieving subscription details: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      nil
    end
  end

  def update_stripe_customer_address
    return false unless @subscriber.stripe_customer_id

    address = {
      line1: @subscriber.address_line1,
      line2: @subscriber.address_line2,
      city: @subscriber.city,
      state: @subscriber.state,
      postal_code: @subscriber.postal_code,
      country: @subscriber.country
    }

    Stripe::Customer.update(@subscriber.stripe_customer_id, { address: address })

    true
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error updating customer address: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    false
  rescue => e
    Rails.logger.error "Error updating customer address: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    false
  end

  # Updates an existing subscription with new products, size, and discounts
  #
  # Updates by completely replacing all subscription items to ensure clean slate
  # Can generate prorated invoices for changes in subscription cost
  #
  # @param product_keys [Array<Symbol>] New product keys for the subscription
  # @param size_key [Symbol] New size tier key for the subscription
  # @param discount_code [String, nil] Optional discount code to apply
  # @return [Hash] Updated subscription and any generated invoice
  def update_subscription(product_keys, size_key, discount_code, invoice_immediately: false)
    return nil unless @subscriber.stripe_subscription_id
    discount_code_redemption_id = nil

    begin
      Rails.logger.info "Starting subscription update for subscriber #{@subscriber.id}"

      subscription = Stripe::Subscription.retrieve({
                                                     id: @subscriber.stripe_subscription_id,
                                                     expand: ['items.data.price.product']
                                                   })

      Rails.logger.info "Retrieved current subscription: #{subscription.id}"

      current_products = []
      current_prices = []
      subscription.items.data.each do |item|
        if item.price&.product
          current_products << item.price.product.name
          current_prices << item.price.id
        end
      end

      Rails.logger.info "Current products: #{current_products.inspect}"
      Rails.logger.info "Current prices: #{current_prices.inspect}"

      # Reset items completely to fix product removal issue
      items = []

      subscription.items.data.each do |item|
        items << { id: item.id, deleted: true }
        Rails.logger.info "Marking item for deletion: #{item.id}"
      end

      product_keys.each do |product_key|
        price_id = StripeProducts.get_price_id(product_key, size_key, @stripe_env)
        raise ArgumentError, "Could not find price ID for product #{product_key} and size #{size_key}" if price_id.nil?
        items << { price: price_id }
        Rails.logger.info "Adding new item with price: #{price_id}"
      end

      discounts = []

      if product_keys.length > 1
        bundle_discount = StripeProducts.get_discount_coupon_id(product_keys.length, @stripe_env)
        if bundle_discount
          discounts.push({ coupon: bundle_discount })
          Rails.logger.info "Adding bundle discount coupon: #{bundle_discount}"
        end
      end

      if discount_code.present?
        discount_service = DiscountService.new(@subscriber)
        discount_result = discount_service.apply_discount_code(discount_code)
        discount_code_redemption_id = discount_result[:discount_code_redemption_id] if discount_result[:success]

        if discount_result[:success]
          custom_coupon_id = discount_result[:stripe_coupon_id]
          discounts.push({ coupon: custom_coupon_id })
          Rails.logger.info "Applied discount code: #{discount_code}, Stripe coupon: #{custom_coupon_id}"
        else
          error_message = discount_result[:message]
          Rails.logger.warn "Failed to apply discount code: #{discount_code}, Error: #{error_message}"
          raise StandardError, "Discount code error: #{error_message}"
        end
      end

      update_params = {
        items: items,
        discounts: discounts,
        proration_behavior: 'always_invoice',
        collection_method: 'send_invoice',
        days_until_due: 30,
        automatic_tax: { enabled: true }
      }

      if invoice_immediately
        update_params.delete(:days_until_due) # only works if collection_method is 'send_invoice'
        update_params[:collection_method] = 'charge_automatically'
        update_params[:proration_behavior] = 'always_invoice'
        # MUST set billing cycle to now to force a proration for the current subscription
        # otherwise discount only applies to new items, adding 1 service would bill for £37.50
        # With this set -> adding 1 service bills for £30 to correctly total to £80
        update_params[:billing_cycle_anchor] = 'now'
      end

      Rails.logger.info "Updating subscription with params: #{update_params.inspect}"

      updated_subscription = Stripe::Subscription.update(
        subscription.id,
        update_params
      )

      Rails.logger.info "Subscription updated. New status: #{updated_subscription.status}"

      invoice = nil
      if updated_subscription.latest_invoice
        Rails.logger.info "Latest invoice ID: #{updated_subscription.latest_invoice}"

        invoice = Stripe::Invoice.retrieve(updated_subscription.latest_invoice)
        Rails.logger.info "Invoice status: #{invoice.status}, amount: #{invoice.total}"

        if invoice.status == 'draft'
          Rails.logger.info 'Finalizing draft invoice'
          invoice = Stripe::Invoice.finalize_invoice(invoice.id)
          Rails.logger.info "Invoice finalized, new status: #{invoice.status}"
        end

        if invoice.status == 'open'
          begin
            Rails.logger.info 'Sending invoice'
            invoice = Stripe::Invoice.send_invoice(invoice.id)
            Rails.logger.info "Invoice sent, new status: #{invoice.status}"
          rescue Stripe::StripeError => e
            handle_error_log(e, 'update_subscription#send_invoice')
            Rails.logger.error "Error sending invoice: #{e.message}"
          end
        end
      else
        Rails.logger.info 'No invoice was created for this subscription update'
      end

      @subscriber.update(subscription_status: updated_subscription.status)

      {
        subscription: updated_subscription,
        invoice: invoice
      }
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error updating subscription: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")

      error_message = case e.code
                      when 'resource_missing'
                        'The subscription could not be found. It may have been cancelled.'
                      when 'parameter_invalid'
                        'Invalid parameters for subscription update. Please contact support.'
                      else
                        "Error updating subscription: #{e.message}"
                      end
      discount_service&.rollback_code_redemption(discount_code_redemption_id) if discount_code_redemption_id.present?
      raise StandardError, error_message
    rescue => e
      Rails.logger.error "Error updating subscription: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      discount_service&.rollback_code_redemption(discount_code_redemption_id) if discount_code_redemption_id.present?
      raise e
    end
  end

  # Retrieves all invoices for the customer
  #
  # Gets both paid and open invoices, combines them, and sorts by date
  #
  # @param limit [Integer] Maximum number of invoices to retrieve
  # @return [Array<Stripe::Invoice>] Array of invoice objects, sorted newest first
  def get_all_customer_invoices(limit = 100)
    return [] unless @subscriber.stripe_customer_id

    begin
      # Get recently paid invoices
      paid_invoices = Stripe::Invoice.list({
                                             customer: @subscriber.stripe_customer_id,
                                             limit: 50,
                                             status: 'paid',
                                             expand: ['data.subscription', 'data.discounts']
                                           })

      # Get all open, draft, uncollectible invoices
      open_invoices = Stripe::Invoice.list({
                                             customer: @subscriber.stripe_customer_id,
                                             limit: 50,
                                             status: 'open',
                                             expand: ['data.subscription', 'data.discounts']
                                           })

      # Get all upcoming invoices
      upcoming_invoice = Stripe::Invoice.upcoming({
                                                    customer: @subscriber.stripe_customer_id,
                                                    subscription: @subscriber.stripe_subscription_id,
                                                    expand: ['subscription']
                                                  })

      # Combine and sort by date, newest first
      (paid_invoices.data + open_invoices.data + [upcoming_invoice]).sort_by { |inv| -inv.created }
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error retrieving customer invoices: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      []
    rescue => e
      Rails.logger.error "Error retrieving customer invoices: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      []
    end
  end

  # Voids an invoice to prevent it from being paid
  #
  # Only works for invoices that haven't been finalized/paid
  #
  # @param invoice_id [String] The Stripe invoice ID to void
  # @return [Boolean] True if successful, false otherwise
  def void_invoice(invoice_id)
    Stripe::Invoice.void_invoice(invoice_id)
    Rails.logger.info "Voided invoice: #{invoice_id}"
    true
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error voiding invoice: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")

    error_message = case e.code
                    when 'resource_missing'
                      'Invoice not found. It may have been already processed.'
                    when 'invoice_no_customer_line_items'
                      'This invoice cannot be voided as it has already been finalized.'
                    else
                      "Error voiding invoice: #{e.message}"
                    end

    raise StandardError, error_message
  rescue => e
    Rails.logger.error "Error voiding invoice: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    false
  end

  # Cancels a subscription either immediately or at the end of the billing period
  #
  # When immediate cancellation is requested, a prorated refund may be generated
  # When scheduled, subscription remains active until end of current period
  #
  # @param cancel_at_period_end [Boolean] Whether to cancel at period end (true) or immediately (false)
  # @param cancellation_reason [String, nil] Optional reason for cancellation to record
  # @return [Stripe::Subscription, nil] The updated/deleted subscription or nil if not found
  def cancel_subscription(cancel_at_period_end = true, cancellation_reason = nil)
    return nil unless @subscriber.stripe_subscription_id

    begin
      Rails.logger.info "Canceling subscription #{@subscriber.stripe_subscription_id} for subscriber #{@subscriber.id}"

      subscription = Stripe::Subscription.retrieve(@subscriber.stripe_subscription_id)

      cancellation_params = {
        cancellation_details: {
          comment: cancellation_reason
        }
      }

      if cancel_at_period_end
        Rails.logger.info 'Scheduling cancellation at period end'
        cancellation_params[:cancel_at_period_end] = true

        updated_subscription = Stripe::Subscription.update(
          @subscriber.stripe_subscription_id,
          cancellation_params
        )

        @subscriber.update(
          subscription_status: 'active_until_period_end',
          cancellation_date: Time.at(subscription.current_period_end).to_datetime,
          cancellation_reason: cancellation_reason
        )
      else
        Rails.logger.info 'Canceling subscription immediately'

        deleted_subscription = Stripe::Subscription.delete(
          @subscriber.stripe_subscription_id,
          {
            prorate: true,
            invoice_now: true # Create a final invoice for any unbilled charges
          }
        )

        @subscriber.update(
          subscription_status: 'canceled',
          stripe_subscription_id: nil,
          cancellation_date: Time.now,
          cancellation_reason: cancellation_reason
        )

        # as there is no stripe subscription id, this will just sync the active_products
        # without a request // this is only in dev anyway
        SubscriptionSyncJob.perform_now(@subscriber.id)

        updated_subscription = deleted_subscription
      end

      Rails.logger.info "Subscription cancelled. New status: #{updated_subscription.status}"
      updated_subscription
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error canceling subscription: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")

      # Create custom error with preserved code
      custom_error = Stripe::StripeError.new(
        case e.code
        when 'resource_missing'
          'The subscription could not be found. It may have already been cancelled.'
        when 'subscription_already_canceled'
          'This subscription has already been cancelled.'
        else
          "Error canceling subscription: #{e.message}"
        end
      )

      # Preserve the original error code
      custom_error.define_singleton_method(:code) { e.code }

      raise custom_error
    rescue => e
      Rails.logger.error "Error canceling subscription: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end

  # Gets details about a scheduled cancellation
  #
  # Retrieves information about when a subscription will end and how many days remain
  #
  # @return [Hash, nil] Cancellation details including end date and days remaining, or nil if error
  def get_cancellation_details
    return nil unless @subscriber.stripe_subscription_id

    begin
      subscription = Stripe::Subscription.retrieve(@subscriber.stripe_subscription_id)

      if subscription.cancel_at_period_end
        # Calculate remaining days
        remaining_days = (Time.at(subscription.current_period_end) - Time.now).to_i / 86_400

        return {
          cancel_at_period_end: true,
          current_period_end: subscription.current_period_end,
          current_period_end_date: Time.at(subscription.current_period_end).strftime('%B %d, %Y'),
          remaining_days: remaining_days
        }
      end

      { cancel_at_period_end: false }
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error retrieving cancellation details: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      nil
    rescue => e
      Rails.logger.error "Error retrieving cancellation details: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      nil
    end
  end

  # Checks if a subscription is scheduled for cancellation
  #
  # @return [Boolean] True if scheduled for cancellation, false otherwise or on error
  def is_cancellation_scheduled?
    return false unless @subscriber.stripe_subscription_id

    begin
      subscription = Stripe::Subscription.retrieve(@subscriber.stripe_subscription_id)
      subscription.cancel_at_period_end
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error checking cancellation status: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      false
    rescue => e
      Rails.logger.error "Error checking cancellation status: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  # Reactivates a subscription that was scheduled for cancellation
  #
  # Removes the cancel_at_period_end flag to continue the subscription
  #
  # @return [Stripe::Subscription, nil] The reactivated subscription or nil if error/not found
  def reactivate_subscription
    return nil unless @subscriber.stripe_subscription_id

    begin
      subscription = Stripe::Subscription.retrieve(@subscriber.stripe_subscription_id)

      unless subscription.cancel_at_period_end
        Rails.logger.info "Subscription #{subscription.id} is not scheduled for cancellation"
        return subscription
      end

      updated_subscription = Stripe::Subscription.update(
        @subscriber.stripe_subscription_id,
        { cancel_at_period_end: false }
      )

      @subscriber.update(
        subscription_status: 'active',
        cancellation_date: nil,
        cancellation_reason: nil
      )

      Rails.logger.info "Subscription #{subscription.id} has been reactivated"
      updated_subscription
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error reactivating subscription: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")

      custom_error = Stripe::StripeError.new(
        case e.code
        when 'resource_missing'
          'The subscription could not be found. It may have been cancelled.'
        else
          "Error reactivating subscription: #{e.message}"
        end
      )

      custom_error.define_singleton_method(:code) { e.code }

      raise custom_error
    rescue => e
      Rails.logger.error "Error reactivating subscription: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end

  # Creates a Stripe Customer Portal session for managing billing
  #
  # Generates a portal URL where customers can update payment methods, view invoices, etc.
  #
  # @param return_url [String] URL to return to after using the portal
  # @return [Hash] Success status and either portal URL or error message
  def create_customer_portal_session(return_url)
    return { success: false, error: 'No customer ID' } unless @subscriber.stripe_customer_id

    begin
      session = Stripe::BillingPortal::Session.create({
                                                        customer: @subscriber.stripe_customer_id,
                                                        return_url: return_url,
                                                        configuration: Rails.configuration.stripe[:portal_configuration_id]
                                                      })

      { success: true, url: session.url }
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error creating customer portal session: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")

      error_message = case e.code
                      when 'resource_missing'
                        'Customer not found. Please contact support.'
                      when 'parameter_missing'
                        'Missing required parameter for customer portal. Please contact support.'
                      else
                        "Error creating customer portal session: #{e.message}"
                      end

      { success: false, error: error_message }
    rescue => e
      Rails.logger.error "Error creating customer portal session: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      { success: false, error: 'An unexpected error occurred. Please try again later.' }
    end
  end

  def update_po_number(po_number)
    return unless @subscriber.stripe_customer_id

    customer = Stripe::Customer.retrieve(@subscriber.stripe_customer_id)
    # Stripe doesn't accept a stripe json object as a valid value in the update
    custom_fields = (customer.invoice_settings.custom_fields || []).map { |f| f.to_h.transform_keys(&:to_sym) }
    po_number_index = custom_fields.index { |field| field[:name] == 'PO Number' }

    if po_number_index && po_number.present?
      # Update existing value
      custom_fields[po_number_index][:value] = po_number
    elsif po_number_index
      # Remove existing value
      custom_fields.delete_at(po_number_index)
    elsif po_number.present?
      # Add a new value
      custom_fields << { name: 'PO Number', value: po_number }
    end

    # pass an empty string to clear the field entirely.
    # must do this instead of empty array etc
    custom_fields = '' if custom_fields.empty?

    Stripe::Customer.update(@subscriber.stripe_customer_id, { invoice_settings: { custom_fields: custom_fields } })
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error updating PO number: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    raise StandardError, "Error updating PO number: #{e.message}"
  rescue => e
    Rails.logger.error "Error updating PO number: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    false
  end

  # Retrieves the internal plan_key (e.g., :individual_monthly, :small)
  # from a Stripe Subscription object's item details.
  # @param stripe_subscription_object [Stripe::Subscription] Expanded Stripe subscription object.
  # @return [Symbol, nil] The determined plan_key or nil.
  def get_current_plan_key_from_details(stripe_subscription_object)
    return nil unless stripe_subscription_object&.items&.data&.any?
    first_item_price = stripe_subscription_object.items.data.first&.price
    return nil unless first_item_price

    # Nickname examples: "Science - Individual (Annual)", "Geography - Individual (Monthly)", "AI - Small School"
    price_nickname = first_item_price.nickname
    
    # Check if the nickname contains "(Monthly)" There's only one plan that is monthly
    # and it is the Individual plan. This is a bit of a hack, but it works for now.
    if price_nickname.include?("(Monthly)")
      return :individual_monthly
    end

    # The product part of the nickname can be complex to generically remove if product names vary wildly or contain "School" etc.
    # We need to map the nickname structure back to one of our StripeProducts::BASE_PRICES keys.

    # Sort the keys by length so that when matching, we don't pick very large school for large school
    sorted_keys = StripeProducts::BASE_PRICES.keys.sort_by { |k| -StripeProducts::SIZES[StripeProducts.get_base_size_from_plan_key(k)][:name].length }

    if price_nickname
      sorted_keys.each do |plan_key|
        base_size_key = StripeProducts.get_base_size_from_plan_key(plan_key)
        size_name = StripeProducts::SIZES[base_size_key][:name] # e.g., "Individual", "Small School"

        if price_nickname.include?(size_name)
          puts "Matched plan_key: #{plan_key}"
          # This is a basic match. If product names are also in nicknames, you might need to account for that.
          # e.g., if nickname is "Science - Individual (Monthly)", ensure `expected_nickname_segment` is "Individual (Monthly)"
          # This match is more reliable if the plan part of nickname is unique enough.
          return plan_key
        end
      end
    end
    Rails.logger.warn "Could not reliably determine plan_key from subscription items for sub: #{stripe_subscription_object.id}. Nickname: #{price_nickname}"
    nil # Fallback if no match
  end

  # Convenience method to get plan_key for the @subscriber's current active subscription.
  def get_current_plan_key_from_active_sub
    stripe_sub_details = get_subscription_details # Fetches current subscription for @subscriber
    return nil unless stripe_sub_details
    get_current_plan_key_from_details(stripe_sub_details)
  end

  # Creates a combined discount coupon based on the total discount amount
  #
  # @param total_discount_amount [Integer] The total discount amount in cents
  # @return [String] The ID of the created coupon
  def create_combined_discount_coupon(total_discount_amount)
    # Generate a unique coupon ID
    subscriber_id = @subscriber.id
    coupon_id = "COMBINED_#{subscriber_id}_#{SecureRandom.hex(4).upcase}"

    # Convert discount amount to pounds for display
    discount_pounds = (total_discount_amount / 100.0).round(2)

    coupon_params = {
      id: coupon_id,
      duration: 'once',
      name: "Combined Discount (£#{discount_pounds} off)",
      amount_off: total_discount_amount,
      currency: 'gbp',
      metadata: {
        environment: @stripe_env.to_s,
        subscriber_id: @subscriber.id,
        combined_discount: 'true'
      }
    }

    coupon = Stripe::Coupon.create(coupon_params)
    Rails.logger.info "Created combined discount coupon: #{coupon.id} with £#{discount_pounds} off"

    coupon.id
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating combined discount coupon: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    nil
  rescue => e
    Rails.logger.error "Error creating combined discount coupon: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    nil
  end

  private

  # Finds an existing Stripe customer or creates a new one
  #
  # Updates customer info if it exists but details have changed
  #
  # @return [Stripe::Customer] The retrieved or newly created customer
  def find_or_create_stripe_customer
    if @subscriber.stripe_customer_id.present?
      begin
        customer = Stripe::Customer.retrieve(@subscriber.stripe_customer_id)

        if customer.email != @subscriber.email
          Stripe::Customer.update(
            customer.id,
            { email: @subscriber.email, name: @subscriber.name }
          )
          Rails.logger.info "Updated customer #{customer.id} email to #{@subscriber.email}"
        end

        customer
      rescue Stripe::InvalidRequestError => e
        Rails.logger.warn "Customer not found in Stripe (#{e.message}), creating a new one."
        create_new_stripe_customer
      rescue Stripe::StripeError => e
        Rails.logger.error "Stripe error retrieving customer: #{e.message}"
        Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
        Rails.logger.error e.backtrace.join("\n")
        create_new_stripe_customer
      rescue => e
        Rails.logger.error "Error retrieving customer: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        create_new_stripe_customer
      end
    else
      create_new_stripe_customer
    end
  end

  # Creates a new Stripe customer with the subscriber's information
  #
  # @return [Stripe::Customer] The newly created customer
  def create_new_stripe_customer
    address = {
      line1: @subscriber.address_line1,
      line2: @subscriber.address_line2,
      city: @subscriber.city,
      state: @subscriber.state,
      postal_code: @subscriber.postal_code,
      country: @subscriber.country
    }

    customer_params = {
      email: @subscriber.email,
      name: @subscriber.name,
      address: address,
      shipping: {
        name: @subscriber.name,
        address: address
      },
      metadata: {
        subscriber_id: @subscriber.id,
        environment: @stripe_env.to_s
      }
    }

    # Add invoice settings with PO number if available
    if @subscriber.po_number.present?
      customer_params[:invoice_settings] = {
        custom_fields: [
          {
            name: 'PO Number',
            value: @subscriber.po_number
          }
        ]
      }
    end

    customer = Stripe::Customer.create(customer_params)

    @subscriber.update(stripe_customer_id: customer.id)
    customer
  end

  # Generates a human-readable description for the subscription
  #
  # @param product_keys [Array<Symbol>] Product keys in the subscription
  # @param plan_key [Symbol] Plan key (e.g., :individual_monthly, :small)
  # @return [String] Formatted description (e.g. "Science, Geography - Individual (Monthly)")
  def generate_subscription_description(product_keys, plan_key)
    product_names = product_keys.map { |key| StripeProducts::PRODUCTS[key][:name] }.join(', ')

    # Get the base size key (e.g., :individual from :individual_monthly)
    base_size_key = StripeProducts.get_base_size_from_plan_key(plan_key)
    size_name = StripeProducts::SIZES[base_size_key] ? StripeProducts::SIZES[base_size_key][:name] : base_size_key.to_s.humanize

    description = "#{product_names} - #{size_name}"
    description += " (Monthly)" if StripeProducts.is_monthly_plan?(plan_key)
    # Optionally, be explicit for annual individual too if desired for clarity elsewhere
    # description += " (Annual)" if !StripeProducts.is_monthly_plan?(plan_key) && base_size_key == :individual
    description
  end

  def handle_error_log(error, location)
    log_error(error, "SubscriptionService##{location}", @subscriber&.user_or_creator)
  end
end
