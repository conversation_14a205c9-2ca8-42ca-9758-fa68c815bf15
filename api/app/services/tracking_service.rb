# app/services/tracking_service.rb
class TrackingService
  # Tracks the view of an article.
  #
  # @param article_id [Integer] The ID of the article being viewed.
  # @param current_user [User] The current user viewing the article.
  def self.track_article_view(article_id, current_user)
    return if article_id.blank?
    article = Article.find_by(id: article_id)
    return if article.blank?

    properties = user_data(current_user).merge({
                                                article_id: article.id,
                                                article_name: article.name,
                                              })
    posthog_data = {
      distinct_id: current_user&.id || 'anonymous',
      event: 'blog view',
      properties: properties
    }

    $posthog.capture(posthog_data)
  end

  # Tracks a video view and captures relevant properties.
  #
  # @param video [Video] The video being viewed.
  # @param current_user [User] The user viewing the video.
  def self.track_video_view(video, current_user)
    record = VideoView.create!(
      video: video,
      user: current_user
    )

    properties = user_data(current_user).merge({
                                                 video_id: video.id,
                                                 video_name: video.name,
                                               })
    properties = properties.merge(campaign_data(video.campaign)) if video.campaign.present?

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'video view',
      properties: properties
    )

    record
  end

  # Tracks a film view for a user.
  #
  # @param current_user [User] The current user viewing the film.
  # @param lesson_template [LessonTemplate] Relevant lesson template
  # @param resource [VideoResource] POLYMORPHIC Resource the video is associated with. (e.g Lesson::Slide)
  # @param data [Hash] Additional data for tracking the film view.
  # @option data [String] :jw_id The ID of the lesson.
  # @option data [Integer] :lesson_id The ID of the lesson.
  # @option data [Integer] :fileboy_video_id The ID of the video in the fileboy system.
  # @option data [Integer] :time_viewing The duration of time the user viewed the film in seconds.
  # @option data [Integer] :film_duration The total duration of the film in seconds.
  # @option data [String] :film_type The type of film being viewed.
  #
  def self.track_film_view(current_user, lesson_template, resource, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    # lesson_id: Number(lessonId),
    # fileboy_video_id: fileboyId,
    # time_viewing (seconds)
    # film_duration (seconds)
    # film_type: filmType :: "expert" | "mission_assignment" | "career"  | "employer" | "training_pathway" | "video"

    # passing in video resource data as resource...
    # video_resource_id: videoId - polymorphic id
    # video_resource: "Lesson::Slide" (seems to only ever be this..) - this is a polymoprhic

    record = TrackingFilm.create!(
      lesson_template: lesson_template,
      lesson_id: data[:lesson_id]&.to_i,
      pupil: current_user,
      time_viewing: data[:time_viewing],
      film_duration: data[:film_duration],
      film_type: data[:film_type],
      fileboy_video_id: data[:fileboy_video_id],
      jw_id: data[:jw_id],
      video_resource: resource,
      video_resource_id: data[:video_id],
    ) if data[:lesson_id].present? && current_user&.pupil?

    properties = user_data(current_user)
                  .merge(template_data(lesson_template))
                  .merge({
                          lesson_id: data[:lesson_id]&.to_i,
                          jw_id: data[:jw_id],
                          fileboy_video_id: data[:fileboy_video_id],
                          video_url: data[:video_url],
                          video_id: data[:video_id]&.to_i,
                          time_viewing: data[:time_viewing]&.to_f,
                          film_duration: data[:film_duration]&.to_f,
                          film_type: data[:film_type],
                          video_resource_id: resource&.id,
                          video_resource: resource&.model_name&.name,
                        })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'film view',
      properties: properties
    )

    record
  end

  # Tracks a glossary view event.
  #
  # @param glossary_word [GlossaryWord] The glossary word being viewed.
  # @param current_user [User] The current user viewing the glossary word.
  #
  def self.track_glossary_view(glossary_word, current_user)
    properties = {
      glossary_id: glossary_word.id,
      glossary_name: glossary_word.name,
    }.merge(user_data(current_user))

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'glossary view',
      properties: properties
    )
  end

  # Tracks the completion of a rocket word quiz by a user.
  #
  # @param current_user [User] The current user who completed the quiz.
  # @param lesson_template [LessonTemplate] The lesson template associated with the quiz.
  # @param data [Hash] Additional data for the quiz completion, including time taken, score, and lesson ID.
  # @option data [Integer] :time_taken The time taken to complete the quiz.
  # @option data [Integer] :score The score achieved in the quiz.
  # @option data [Integer] :lesson_id The ID of the lesson associated with the quiz.
  # @option data [Hash] :answers_json The ID of the lesson associated with the quiz.
  #
  def self.track_rocket_word_quiz_completion(current_user, lesson_template, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    lesson_id = data.dig(:lesson_id)&.to_i

    properties = user_data(current_user).merge(template_data(lesson_template)).merge({
                                                                                       time_taken: data[:time_taken],
                                                                                       score: data[:score],
                                                                                       lesson_id: lesson_id,
                                                                                     })
    if current_user&.pupil? && lesson_id.present?
      TrackingRocketWord.create(
        time_taken: data[:time_taken],
        score: data[:score],
        lesson_id: lesson_id,
        lesson_template: lesson_template,
        pupil: current_user,
        answers_json: data[:answers_json],
      )
    end

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'rocket word quiz completed',
      properties: properties
    )
  end

  # Tracks the view of a lesson template by a user.
  #
  # @param current_user [User] The current user.
  # @param lesson_template [LessonTemplate] The lesson template being viewed.
  # @param data [Hash] Additional data related to the view.
  # @option data [Integer] :lesson_id The ID of the lesson being viewed.
  #
  def self.track_lesson_template_view(current_user, lesson_template, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    properties = user_data(current_user).merge(template_data(lesson_template)).merge({
                                                                                       lesson_id: data[:lesson_id]&.to_i,
                                                                                     })

    if current_user&.pupil? && data[:lesson_id].present?
      TrackingLessonTemplateViewed.create(
        lesson_template: lesson_template,
        lesson_id: data[:lesson_id]&.to_i,
        pupil: current_user,
      )
    else
      TrackingLessonTemplateViewed.create(
        lesson_template: lesson_template,
        lesson_id: data[:lesson_id]&.to_i,
        user: current_user,
      )
    end

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'lesson template viewed',
      properties: properties
    )
  end

  # Tracks the completion of a word search by a user.
  #
  # @param current_user [User] The current user.
  # @param lesson_template [LessonTemplate] The lesson template.
  # @param data [Hash] Additional data for tracking.
  # @option data [Integer] :time_taken The time taken to complete the word search.
  # @option data [Integer] :lesson_id The ID of the lesson.
  #
  def self.track_word_search_completion(current_user, lesson_template, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    lesson_id = data.dig(:lesson_id)&.to_i
    properties = user_data(current_user).merge(template_data(lesson_template)).merge({
                                                                                       time_taken: data[:time_taken],
                                                                                       lesson_id: lesson_id,
                                                                                     })
    if current_user&.pupil? && lesson_id.present?
      TrackingWordSearch.create(
        time_taken: data[:time_taken],
        lesson_id: lesson_id,
        lesson_template: lesson_template,
        pupil: current_user,
      )
    end

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'word search completed',
      properties: properties
    )
  end

  # Tracks the completion of a quip quiz by a user.
  #
  # @param current_user [User] The current user.
  # @param lesson_template [LessonTemplate] The lesson template associated with the quiz.
  # @param data [Hash] Additional data related to the quiz completion.
  # @option data [Integer] :time_taken The time taken to complete the quiz.
  # @option data [Integer] :score The score achieved by the user.
  # @option data [Integer] :total_score The total score possible for the quiz.
  # @option data [Integer] :noodle_quiz_id The ID of the quiz.
  # @option data [Integer] :lesson_id The ID of the lesson.
  # @option data [Integer] :campaign_quiz_id.
  # @option data [Integer] :quiz_id.
  # @option data [Hash] :answers_json.
  #
  def self.track_quip_quiz_completion(current_user, lesson_template, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    properties = user_data(current_user).merge({
                                                time_taken: data[:time_taken],
                                                score: data[:score],
                                                total_score: data[:total_score],
                                                quiz_key: data[:noodle_quiz_id],
                                                lesson_id: data[:lesson_id]&.to_i,
                                                campaign_quiz_id: data[:campaign_quiz_id],
                                                quiz_id: data[:quiz_id]&.to_s,
                                                flow_progress_id: data[:flow_progress_id]&.to_s,
                                                result_type: data[:result_type]
                                              })
    properties.merge!(template_data(lesson_template)) if lesson_template.present?
    campaign_quiz = Quiz.find_by(id: data[:campaign_quiz_id]) if data[:campaign_quiz_id]
    properties = properties.merge(campaign_data(campaign_quiz.campaign)) if campaign_quiz.present?

    if current_user&.pupil? && data[:lesson_id].present? && lesson_template.present?
      TrackingSummativeQuiz.create(
        time_taken: data[:time_taken],
        score: data[:score],
        answers_json: data[:answers_json],
        lesson_id: data[:lesson_id]&.to_i,
        lesson_template: lesson_template,
        pupil: current_user,
        quiz_id: data[:quiz_id],
        noodle_quiz_id: data[:noodle_quiz_id],
        campaign_quiz_id: data[:campaign_quiz_id],
        total_score: data[:total_score],
      )
    end

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'summative quiz completed',
      properties: properties
    )
  end

  # Tracks the view of a link by a user.
  #
  # @param current_user [User] The current user.
  # @param lesson_template [LessonTemplate] The lesson template.
  # @param data [Hash] Additional data for tracking.
  # @option data [String] :url The URL of the link.
  # @option data [String] :link_type The type of the link.
  # @option data [Integer] :lesson_id The ID of the lesson.
  #
  def self.track_link_view(current_user, lesson_template, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    if data[:lesson_id].present? && data[:link_type].present? && current_user&.pupil?
      record = TrackingLinkTracking.create!(
        pupil: current_user,
        lesson_template: lesson_template,
        lesson_id: data[:lesson_id]&.to_i,
        url: data[:url],
        link_type: data[:link_type]
      )
    end

    properties = user_data(current_user)
                 .merge(template_data(lesson_template))
                 .merge({
                    url: data[:url],
                    link_type: data[:link_type],
                    lesson_id: data[:lesson_id]&.to_i,
                  })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'link viewed',
      properties: properties
    )

    record
  end

  # Tracks the download of a document by a user.
  #
  # @param current_user [User] The current user who downloaded the document.
  # @param lesson_template [Lesson::Template] The lesson template associated with the document.
  # @param data [Hash] Additional data related to the document download.
  # @option data [Integer] :document_id The ID of the document being downloaded.
  # @option data [String] :document_type The type of the document being downloaded.
  # @option data [Integer] :lesson_id The ID of the lesson associated with the document.
  #
  def self.track_document_download(document, current_user, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    lesson_id = data.dig(:lesson_id)&.to_i
    template = document&.template rescue nil
    properties = user_data(current_user).merge({
                                                  document_type: data[:document_type],
                                                  lesson_id: lesson_id,
                                                  document_id: document&.id,
                                                  document_name: document&.name,
                                                  document_source: data[:document_source]
                                                })
    properties.merge!(template_data(template)) if template

    if current_user&.pupil? && lesson_id.present?
      TrackingDocument.create(
        document: document,
        lesson_template: template,
        lesson_id: lesson_id,
        pupil: current_user,
        document_type: 'handout', # this can't be anything else..
      )
    end

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'document downloaded',
      properties: properties
    )
  end

  # Tracks the login event of a user.
  #
  # @param current_user [User] The user who logged in.
  #
  def self.track_user_login(current_user)
    record = TrackingLogin.create!(user: current_user, created_at: DateTime.now)

    properties = user_data(current_user).merge({ date: record.created_at })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'user login',
      properties: properties
    )

    record
  end

  # Tracks the user login activity and captures the event using PostHog.
  #
  # @param current_user [User] The currently logged-in user.
  # @return [boolean] return true if the record already exists
  #
  def self.track_active_day(current_user)
    record = ActiveDay.find_by(user: current_user, date: Date.today)
    # # avoid creating new posthog events if the active day already exists
    return true if record && record&.date == Date.today
    begin
      if (new_record = ActiveDay.create(user: current_user, date: Date.today))
        # Track user login activity here
        if new_record.persisted?
          properties = user_data(current_user).merge({ date: new_record.date })
          $posthog.capture(
            distinct_id: current_user&.id || 'anonymous',
            event: 'user active day',
            properties: properties
          )
        end
        return false # return false if it created a record
      end
    rescue ActiveRecord::RecordNotUnique => _e
      # silence this error as we want to prevent duplicates anyway
    end

    true # default to true
  end

  # Tracks the view of a lesson plan by a user.
  #
  # @param current_user [User] The current user viewing the lesson plan.
  # @param lesson_template [LessonTemplate] The lesson template being viewed.
  #
  def self.track_lesson_plan_view(current_user, lesson_template)
    record = LessonPlanView.create!(
      user: current_user,
      lesson_template: lesson_template
    )

    properties = user_data(current_user).merge(template_data(lesson_template))

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'lesson plan viewed',
      properties: properties
    )

    record
  end

  # Tracks the usage of a custom sign up URL by a user.
  #
  # @param user [User] The user who used the custom sign up URL.
  # @param signup_url [SignupUrl] The custom sign up URL that was used.
  #
  def self.track_custom_signup_url_use(user, signup_url)
    record = CustomSignUpUrlUse.create!(
      user: user,
      custom_sign_up_url: signup_url
    )

    properties = user_data(user).merge({
                                         custom_sign_up_url_id: signup_url.id,
                                         custom_sign_up_url_name: signup_url.name,
                                         custom_sign_up_url_url: signup_url.url,
                                       })

    $posthog.capture(
      distinct_id: user&.id || 'anonymous',
      event: 'custom sign up url used',
      properties: properties
    )

    record
  end

  def self.track_tour_view(tour, user)
    return if tour.blank?
    properties = user_data(user).merge({
                                         tour_id: tour.id,
                                         tour_name: tour.name,
                                       })
    properties = properties.merge(campaign_data(tour.campaign)) if tour.campaign.present?

    record = TourView.create(tour: tour, user: user)

    $posthog.capture(
      distinct_id: user&.id || 'anonymous',
      event: 'tour viewed',
      properties: properties
    )
    record
  end

  # Tracks the view of a custom sign up URL by a user.
  #
  # @param user [User] The user who viewed the custom sign up URL.
  # @param signup_url [SignupUrl] The custom sign up URL that was viewed.
  #
  def self.track_custom_signup_url_view(current_user, signup_url)
    CustomSignUpUrlView.create!(
      user: current_user,
      custom_sign_up_url: signup_url
    )

    properties = user_data(current_user).merge({
                                         custom_sign_up_url_id: signup_url.id,
                                         custom_sign_up_url_name: signup_url.name,
                                         custom_sign_up_url_url: signup_url.url,
                                       })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'custom sign up url viewed',
      properties: properties
    )
  end

  # Tracks a career view event for a career path.
  #
  # @param career_ath [CareerPath] The career path being viewed.
  # @param current_user [User] The current user viewing the career path.
  #
  def self.track_career_path_view(career_path, current_user)
    properties = user_data(current_user).merge({
                                                 career_id: career_path.id,
                                                 career_name: career_path.career_name,
                                                 career_job_family: career_path.job_family&.name,
                                                 career_type: 'Career Path'
                                               })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'career view',
      properties: properties
    )
  end

  # Tracks a career view event for a career.
  #
  # @param career [Career] The career being viewed.
  # @param current_user [User] The current user viewing the career.
  #
  def self.track_career_view(career, current_user)
    properties = user_data(current_user).merge({
                                                 career_id: career.id,
                                                 career_name: career.name,
                                                 career_type: 'Career'
                                               })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'career view',
      properties: properties
    )
  end

  # # Tracks the view of a unit.
  #
  # @param unit [NewLibrary::Unit] The ID of a unit being viewed.
  # @param current_user [User] The current user viewing the unit.
  #
  def self.track_unit_view(unit, current_user)
    return if unit.blank?

    properties = user_data(current_user)
                 .merge({
                          unit_id: unit.id,
                          unit_name: unit.name
                        })
                 .merge({
                          unit_campaigns: unit.campaigns.map { |c| campaign_data(c) },
                          unit_campaign_ids: unit.campaigns.pluck(:id),
                        })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'unit viewed',
      properties: properties
    )
  end

  # # Tracks when a campaign link has been clicked.
  #
  # @param campaign [Campaign] The ID of a campaign.
  # @param current_user [User] The current user clicking the campaign link.
  #
  def self.track_campaign_clicks(campaign, current_user)
    return if campaign.blank?

    properties = user_data(current_user).merge({ campaign_id: campaign.id, campaign_name: campaign.name })

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'campaign link clicked',
      properties: properties
    )
  end

  # # Tracks the view of a quiz.
  #
  # @param quiz_id The ID of a quiz being viewed.
  # @param current_user [User] The current user viewing the quiz.
  # @param data [Hash] Additional data related to the document download.
  # @option data [String] :quiz_type (quip_quiz, noodle_quiz) The type of quiz being viewed.
  # @option data [Integer] :lesson_id The ID of the lesson of the quiz.
  # @option data [Integer] :lesson_template_id The ID of the lesson of the quiz.
  #
  def self.track_quiz_view(quiz_id, current_user, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    return if quiz_id.blank?

    template = Lesson::Template.find_by(id: data[:lesson_template_id]) if data[:lesson_template_id]
    lesson = Lesson::Lesson.find_by(id: data[:lesson_id]) if data[:lesson_id]
    template = lesson.template if lesson && !template

    properties = user_data(current_user).merge({
                                                  type: data[:quiz_type],
                                                  quiz_id: quiz_id&.to_s,
                                                  lesson_id: lesson&.id,
                                               })

    properties.merge!(template_data(template)) if template

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'quiz viewed',
      properties: properties
    )
  end

  # # Tracks the view of a word search.
  #
  # @param template [Lesson::Template] The ID of the lesson of a word search being viewed.
  # @param current_user [User] The current user viewing the word search.
  # @param data [Hash] Additional data related to the word search.
  # @option data [Integer] :lesson_id The ID of the lesson of the word search.
  #
  def self.track_word_search_view(template, current_user, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    return if template.blank?

    properties = user_data(current_user)
                 .merge({ lesson_id: data[:lesson_id]&.to_i })
                 .merge(template_data(template))

    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'word search viewed',
      properties: properties
    )
  end

  # # Tracks the view of a presentation.
  #
  # @param template [Lesson::Template] The ID of the lesson of a presentation being viewed.
  # @param current_user [User] The current user viewing the presentation.
  # @param data [Hash] Additional data related to the document download.
  # @option data [Integer] :lesson_id
  #
  def self.track_presentation_view(template, current_user, data = {})
    data.transform_keys!(&:to_sym) # ensure keys are symbols for consistency of accessing data
    return if template.blank?

    if data[:lesson_id].present? && current_user.pupil?
      TrackingPresentationView.create({
        lesson_template: template,
        lesson: Lesson::Lesson.find(data[:lesson_id]),
        user: current_user
      })
      PresentationProgress.find_or_create_by(user_id: current_user.id, lesson_template_id: template.id)
    end

    properties = user_data(current_user)
                 .merge({
                          lesson_id: data[:lesson_id]&.to_i,
                          unit_id: template.primary_new_library_unit_id,
                          unit_name: template.primary_new_library_unit&.name
                        })
                 .merge(template_data(template))


    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: 'presentation viewed',
      properties: properties
    )
  end

  def self.track_subscription_event(event_name, current_user, data = {})
    valid_event_names = [
      'subscription_new',
      'subscription_create',
      'subscription_update',
      'subscription_show',
      'subscription_edit',
      'subscription_cancel',
      'subscription_cancelled',
      'subscription_reactivate'
    ]
    raise ArgumentError, 'Invalid event name' unless valid_event_names.include?(event_name)
    properties = user_data(current_user).merge({ data: data })
    $posthog.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: event_name,
      properties: properties
    )
  end

  # # Tracks the clicking of a marketing link by an anonymous user.
  # 
  # @param marketing_identifier [String] The identifier of the marketing link.
  # @param link_destination [String] The destination of the marketing link.
  def self.track_marketing_link_click(marketing_identifier, link_destination)
    return if marketing_identifier.blank? || link_destination.blank?

    properties = {
      marketing_identifier: marketing_identifier,
      link_destination: link_destination
    }

    $posthog.capture(
      distinct_id: 'anonymous',
      event: 'marketing link clicked',
      properties: properties
    )
  end

  private 

  #### helpers

  def self.query(query_str)
    return if query_str.blank?

    if Rails.env.development?
      puts "=== TRACKING SERVICE QUERY ==="
      puts query_str
      puts "=== ====================== ==="
    end

    properties = {
      query: {
        kind: 'HogQLQuery',
        query: query_str #'select properties.* from events limit 100'
      }
    }

    uri = URI.parse("https://eu.posthog.com/api/projects/#{ENV['POSTHOG_PROJECT_ID']}/query")
    params = { personal_api_key: ENV['POSTHOG_PERSONAL_API_KEY'] }
    uri.query = URI.encode_www_form(params)

    request = Net::HTTP::Post.new(uri)
    request['Accept'] = 'application/json'

    request['content-type'] = 'application/json'
    request.body = properties.to_json

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    return JSON.parse(response.body) if response.code == '200'
    # Handle error
    raise "Error: #{response.code} - #{response.body}"
  end

  def self.user_data(user)
    {
      user_id: user&.id,
      user_email: user&.email,
      user_type: user&.type,
      user_is_admin_teacher: user&.is_school_admin?,
      user_gender: user&.gender,
      user_dob: user&.dob,
      user_ethnicity: user&.ethnicity,
    }.merge(school_data(user&.school))
  end

  def self.school_data(school)
    return {} unless school.present?

    {
      school_id: school&.id,
      school_name: school&.name,
      school_postcode: school&.postcode,
      school_region: school&.region,
    }
  end

  def self.template_data(lesson_template)
    {
      lesson_template_id: lesson_template.id,
      lesson_template_name: lesson_template.name,
      lesson_template_machine_name: lesson_template.machine_name,
      lesson_template_campaigns: lesson_template.campaigns.map { |c| campaign_data(c) },
      lesson_template_campaign_ids: lesson_template.campaigns.pluck(:id),
    }
  end

  def self.campaign_data(campaign)
    {
      campaign_id: campaign.id,
      campaign_name: campaign.name,
      campaign_organization_name: campaign.organisation&.name
    }
  end
end
