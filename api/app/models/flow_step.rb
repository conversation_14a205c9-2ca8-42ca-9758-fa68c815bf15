# == Schema Information
#
# Table name: flow_steps
#
#  id                         :bigint           not null, primary key
#  description                :text
#  estimated_duration_minutes :integer          default(0)
#  name                       :string           not null
#  rich_text_content          :text
#  step_type                  :string           not null
#  weight                     :integer          not null
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  fileboy_image_id           :string
#  flow_id                    :bigint           not null
#  quiz_id                    :bigint
#  video_id                   :bigint
#
# Indexes
#
#  index_flow_steps_on_flow_id             (flow_id)
#  index_flow_steps_on_flow_id_and_weight  (flow_id,weight) UNIQUE
#  index_flow_steps_on_quiz_id             (quiz_id)
#  index_flow_steps_on_step_type           (step_type)
#  index_flow_steps_on_video_id            (video_id)
#
# Foreign Keys
#
#  fk_flow_steps_quip_quizzes  (quiz_id => quip_quizzes.id)
#  fk_rails_...                (flow_id => flows.id)
#  fk_rails_...                (video_id => videos2.id)
#
class FlowStep < ApplicationRecord
  belongs_to :flow, counter_cache: true
  belongs_to :video, optional: true
  belongs_to :quiz, optional: true, class_name: 'QuipQuiz'
  has_many :flow_progresses, dependent: :destroy

  validates :name, presence: true
  validates :step_type, inclusion: { in: %w[video rich_text quiz] }
  validates :weight, presence: true, uniqueness: { scope: :flow_id }

  # Validate content based on step type
  validates :video, presence: true, if: -> { step_type == 'video' }
  validates :quiz, presence: true, if: -> { step_type == 'quiz' }
  validates :rich_text_content, presence: true, if: -> { step_type == 'rich_text' }

  before_validation :set_weight, on: :create

  scope :videos, -> { where(step_type: 'video') }
  scope :rich_text, -> { where(step_type: 'rich_text') }
  scope :quizzes, -> { where(step_type: 'quiz') }

  def completed_by?(user)
    flow_progresses.exists?(user: user, completed: true)
  end

  def next_step
    flow.flow_steps.where('weight > ?', weight).first
  end

  def previous_step
    flow.flow_steps.where('weight < ?', weight).last
  end

  def progress_for(user)
    flow_progresses.find_by(user: user)
  end

  def started_by?(user)
    flow_progresses.exists?(user: user)
  end

  private

  def set_weight
    return if weight.present? # Don't override if already set
    self.weight = (flow.flow_steps.maximum(:weight) || 0) + 1
  end
end
