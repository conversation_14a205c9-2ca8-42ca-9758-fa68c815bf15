# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_templates
#
#  id                                :bigint           not null, primary key
#  activities                        :text
#  admin_checklist                   :jsonb            not null
#  analogies_models                  :text             default("")
#  anonymous                         :boolean          default(FALSE)
#  assessment                        :text
#  available                         :boolean          default(FALSE), not null
#  awaiting_translation              :boolean          default(FALSE), not null
#  content_uploaded                  :boolean          default(FALSE), not null
#  core_knowledge                    :text             default("")
#  deleted                           :boolean          default(FALSE), not null
#  demo                              :boolean          default(FALSE), not null
#  disable_viewing                   :boolean          default(FALSE)
#  employer_link                     :string
#  enquiry_types                     :string           default([]), is an Array
#  film_link                         :string
#  image_cache                       :jsonb
#  image_name                        :string
#  image_uid                         :string
#  impact_assessment                 :string
#  implementation                    :string
#  intent                            :string
#  is_default                        :boolean          default(FALSE)
#  ks4_learning_outcomes             :text             default("")
#  ks4_teacher_mastery               :string           default("")
#  last_modified_at                  :datetime
#  last_modified_by                  :integer
#  learning_outcomes                 :text
#  lesson_check_summative_quiz       :boolean          default(FALSE)
#  lesson_plan_layout                :integer          default("legacy")
#  machine_name                      :string
#  name                              :string
#  new_lesson_plan_resources         :string
#  objectives                        :jsonb
#  post_16_link                      :string
#  previous_rocket_word_narration    :string
#  proof_feedback                    :jsonb
#  pupil_mastery                     :text
#  pupils_awaiting_marks_count_cache :jsonb
#  quip_quiz_key                     :text
#  resources                         :text
#  risk_assessment                   :text
#  sentence_structure                :text
#  specification                     :text             default("")
#  sponsor_logo_cache                :jsonb            not null
#  sponsor_logo_name                 :string
#  sponsor_logo_uid                  :string
#  teacher_mastery                   :text
#  uploaded_old_quiz                 :boolean          default(FALSE), not null
#  use_2022_lesson_plan              :boolean          default(FALSE)
#  use_new                           :boolean
#  use_new_presentation              :boolean          default(FALSE)
#  user_generated                    :boolean          default(FALSE)
#  viewable_only                     :boolean          default(FALSE)
#  weight                            :integer
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  country_id                        :bigint
#  delete__quip_quiz_id              :string
#  employer_fileboy_video_id         :string
#  employer_jw_id                    :string
#  employer_video_id                 :bigint
#  fileboy_image_id                  :string
#  fileboy_sponsor_logo_id           :string
#  fileboy_video_film_id             :string
#  film_fileboy_video_id             :string
#  film_jw_id                        :string
#  film_video_id                     :bigint
#  group_id                          :bigint
#  lesson_template_folder_id         :bigint
#  organisation_id                   :bigint
#  post16_video_id                   :bigint
#  post_16_fileboy_video_id          :string
#  post_16_jw_id                     :string
#  primary_new_library_unit_id       :bigint
#  secondary_unit_id                 :bigint
#  source_template_id                :bigint
#  tertiary_unit_id                  :bigint
#  user_id                           :bigint
#
# Indexes
#
#  index_lesson_templates_on_country_id                   (country_id)
#  index_lesson_templates_on_employer_video_id            (employer_video_id)
#  index_lesson_templates_on_film_video_id                (film_video_id)
#  index_lesson_templates_on_group_id                     (group_id)
#  index_lesson_templates_on_group_id_and_country_id      (group_id,country_id) UNIQUE WHERE (NOT deleted)
#  index_lesson_templates_on_lesson_template_folder_id    (lesson_template_folder_id)
#  index_lesson_templates_on_name                         (name)
#  index_lesson_templates_on_organisation_id              (organisation_id)
#  index_lesson_templates_on_post16_video_id              (post16_video_id)
#  index_lesson_templates_on_primary_new_library_unit_id  (primary_new_library_unit_id)
#  index_lesson_templates_on_secondary_unit_id            (secondary_unit_id)
#  index_lesson_templates_on_source_template_id           (source_template_id)
#  index_lesson_templates_on_tertiary_unit_id             (tertiary_unit_id)
#  index_lesson_templates_on_user_id                      (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (employer_video_id => videos.id)
#  fk_rails_...  (film_video_id => videos.id)
#  fk_rails_...  (group_id => lesson_groups.id)
#  fk_rails_...  (post16_video_id => videos.id)
#  fk_rails_...  (primary_new_library_unit_id => new_library_units.id)
#  fk_rails_...  (secondary_unit_id => units.id)
#  fk_rails_...  (tertiary_unit_id => units.id)
#
require 'keyword_extractor'
require_relative '../../concerns/video_url_helpers'

module Lesson
  class Template < ApplicationRecord
    include HasCareerTags
    include ::VideoUrlHelpers

    enum lesson_plan_layout: { legacy: 0, ks1_3: 1, ks4: 2 }

    LOCALE_TEXT = {
      zh: {
        "todays_rocket_word_quiz": '今天的 关键词：学习您的5个单词及其含义',
        "previous_rocket_word_text": '上一篇关键词'
      },
      en: {
        "todays_rocket_word_quiz": "Today's Rocket Words: Your 5 words and meanings to learn",
        "previous_rocket_word_text": 'Previous Rocket Words:'
      }
    }

    include HasLessonPlan

    scope :available, -> { where(available: true) }
    scope :platform, -> { where(user: nil) }
    scope :ordered, -> { order machine_name: :asc }
    scope :demo, -> { where(demo: true) }
    scope :anonymous_or_demo, -> { where <<-SQL }
      lesson_templates.anonymous OR lesson_templates.demo OR lesson_templates.id = 832
    SQL
    scope :accessible, -> { where <<-SQL }
      lesson_templates.anonymous OR lesson_templates.demo OR lesson_templates.available OR lesson_templates.id = 832
    SQL
    scope :unavailable, -> { where(available: false) }

    scope :one_per_group, -> do
      where(id:
              unscoped
                .where.not(group_id: nil)
                .group(:group_id)
                .select('FIRST(lesson_templates.id ORDER BY country_id ASC)'))
    end

    scope :one_per_group_available, -> { one_per_group.available }
    scope :one_per_group_unavailable, -> { one_per_group.unavailable }

    scope :awaiting_translation, -> { where(awaiting_translation: true) }

    scope :ordered_by, ->(sort_scope) {
      case sort_scope
      when 'name_asc'  then order(name: :asc)
      when 'name_desc' then order(name: :desc)
      when 'date_desc' then order(updated_at: :desc)
      when 'date_asc'  then order(updated_at: :asc)
      else
        order(name: :asc)
      end
    }

    scope :user_template_filter_by_year_and_unit, -> (user, year_id, unit_id) {
      curriculum_id = user&.school&.new_library_curriculum&.id
      ids = left_joins(source_template: { new_library_units: :year })
            .left_joins(new_library_units: :year)
      if unit_id.present? || year_id.present?
        ids = ids.where('new_library_units.id = ?', unit_id) if unit_id.present?
        ids = ids.where('new_library_years.id = ?', year_id) if year_id.present?
        ids = ids.where('new_library_years.curriculum_id = ?', curriculum_id) if curriculum_id.present?
      end
      ids = ids.distinct.pluck(:id)
      where(id: ids)
    }

    before_save do
      self.country_id ||= 1
      self.available_country_ids |= [country_id]
    end

    after_save do
      if saved_change_to_name? &&
         user_generated &&
         new_presentation.present? && new_presentation&.name != saved_change_to_name[1]

        new_presentation.update(name: saved_change_to_name[1])
      end
      if saved_change_to_name? &&
         user_generated &&
         quip_quiz &&
         quip_quiz.name != saved_change_to_name[1]

        quip_quiz.update(name: saved_change_to_name[1])
      end

      # Clear cache
      custom_cache.delete("lesson_template_show_#{id}")
    end

    validates :machine_name, presence: true, uniqueness: {
      unless: :deleted?,
      conditions: -> { where(deleted: false) }
    }

    validates :country_id, uniqueness: {
      if: :country_id,
      unless: :deleted?,
      scope: :group_id,
      conditions: -> { where(deleted: false) }
    }

    belongs_to :source_template, optional: true, class_name: 'Lesson::Template'
    has_many :user_templates, class_name: 'Lesson::Template', foreign_key: :source_template_id

    belongs_to :group, class_name: 'Lesson::Group', inverse_of: :templates
    belongs_to :country, optional: true

    belongs_to :user, optional: true
    has_one :school, through: :user
    belongs_to :organisation, optional: true
    belongs_to :primary_new_library_unit, optional: true, class_name: 'NewLibrary::Unit'

    has_many :template_countries, foreign_key: :lesson_template_id, inverse_of: :lesson_template
    has_many :available_countries, through: :template_countries, source: :country

    has_one :dump, class_name: 'Lesson::Dump', inverse_of: :template

    has_many :author_lesson_templates, class_name: 'AuthorsLessonTemplate'
    has_many :authors, through: :author_lesson_templates

    has_many :keywords, -> { order(weight: :asc) }, dependent: :destroy
    has_many :lessons, dependent: :destroy
    has_many :pupils, through: :lessons
    has_many :forms, through: :lessons
    has_many :results, dependent: :destroy
    has_many :slides, -> { order(weight: :asc) }, dependent: :destroy

    has_many :campaign_lessons, foreign_key: :lesson_template_id
    has_many :campaign_units, through: :campaign_lessons
    has_many :campaigns, through: :campaign_lessons
    has_many :organisations, through: :campaigns

    has_many :template_recommended_careers, foreign_key: :lesson_template_id
    has_many :recommended_careers, through: :template_recommended_careers, source: :career

    has_many :template_frameworks
    has_many :assessment_frameworks, through: :template_frameworks
    has_many :template_assessments
    has_many :risk_assessments, through: :template_assessments

    has_many :template_recommendations, foreign_key: :recommended_by_id
    has_many :recommended_templates, through: :template_recommendations
    has_many :template_recommendations_by, foreign_key: :recommended_template_id, class_name: 'TemplateRecommendation'
    has_many :recommended_by_templates, through: :template_recommendations_by, source: :recommended_by

    has_many :ratings, dependent: :destroy
    has_many :documents, dependent: :destroy

    has_many :quiz_questions, dependent: :destroy, class_name: 'QuizOld::Question', foreign_key: 'lesson_template_id'
    has_many :quiz_attempts, dependent: :destroy, class_name: 'QuizOld::Attempt', foreign_key: 'lesson_template_id'

    has_many :word_search_results, class_name: 'Lesson::WordSearchResult', foreign_key: 'lesson_template_id'

    has_many :presentation_progresses, foreign_key: 'lesson_template_id'

    has_many :new_library_unit_templates, class_name: 'NewLibrary::UnitTemplate'
    has_many :new_library_units, through: :new_library_unit_templates, source: :unit, class_name: 'NewLibrary::Unit'
    has_many :new_library_subjects, through: :new_library_units, source: :subject, class_name: 'NewLibrary::Subject'
    has_many :new_library_years, through: :new_library_units, source: :year, class_name: 'NewLibrary::Year'
    has_many :new_library_curricula, through: :new_library_years, source: :curriculum, class_name: 'NewLibrary::Curriculum'

    has_many :exemplar_works, foreign_key: :lesson_template_id

    has_many :tracking_rocket_words, class_name: 'TrackingRocketWord', foreign_key: :lesson_template_id
    has_many :tracking_films, class_name: 'TrackingFilm', foreign_key: :lesson_template_id
    has_many :tracking_lesson_template_views, class_name: 'TrackingLessonTemplateViewed', foreign_key: :lesson_template_id, dependent: :destroy
    has_many :tracking_word_searches, class_name: 'TrackingWordSearch', foreign_key: :lesson_template_id
    has_many :tracking_summative_quizzes, class_name: 'TrackingSummativeQuiz', foreign_key: :lesson_template_id
    has_many :tracking_link_trackings, class_name: 'TrackingLinkTracking', foreign_key: :lesson_template_id
    has_many :tracking_documents, class_name: 'TrackingDocument', foreign_key: :lesson_template_id
    has_many :tracking_lesson_template_favourites, class_name: 'TrackingLessonTemplateFavourite', foreign_key: :lesson_template_id
    has_many :tracking_presentation_views, class_name: 'TrackingPresentationView', foreign_key: :lesson_template_id

    has_many :scientific_enquiry_lessons, foreign_key: :lesson_template_id
    has_many :scientific_enquiry_types, through: :scientific_enquiry_lessons

    has_one :new_presentation, foreign_key: :lesson_template_id

    has_one :quip_quiz, foreign_key: :lesson_template_id

    has_many :lesson_plan_views, foreign_key: :lesson_template_id

    has_one :film_video, class_name: 'Video'
    has_one :employer_video, class_name: 'Video'
    has_one :post16_video, class_name: 'Video'

    has_one :lesson_template_folder, primary_key: :lesson_template_folder_id, foreign_key: :id

    has_many :lesson_reports, foreign_key: :lesson_template_id

    has_many :lesson_template_career_paths, dependent: :destroy, foreign_key: :lesson_template_id
    has_many :career_paths, through: :lesson_template_career_paths, source: :career_path

    def subject
      if new_library_subjects.any?
        new_library_subjects.first
      elsif user && source_template
        source_template.subject
      end
    end

    def banner_image_url
      return nil unless fileboy_image_id
      "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:1200x600;format:webp;quality:80"
    end

    def extracted_keywords
      slides.text.pluck(:body).join(' ')
      name
      slide_bodies_without_general_words = slides.text.pluck(:body).join(' ').gsub(/’s|mission|starter|differences|today|developing|experts|lesson|:|,/i, '').strip
      KeywordExtractor.extract slide_bodies_without_general_words
    end

    def self.career_tagged_with_all(ids)
      conditions = Array.wrap(ids).map { |id| "BOOL_OR(career_tag_id = #{escape id})" }
      joins(:career_taggings).group(:id).having(conditions.join(' AND '))
    end

    def self.career_tagged_with_any(ids)
      conditions = Array.wrap(ids).map { |id| "BOOL_OR(career_tag_id = #{escape id})" }
      joins(:career_taggings).group(:id).having(conditions.join(' OR '))
    end

    scope :anonymous, -> { where(anonymous: true) }

    def quiz_questions_formatted_for_quip
      keyword_ids = keywords.ids
      quiz_questions
        .where(lesson_keyword_id: keyword_ids)
        .left_joins(:lesson_slide)
        .where('lesson_slides.id IS NOT NULL OR quiz_questions.use_data')
        .map do |question|
        {
          type: 'multi-choice',
          slide_id: question.lesson_slide_id,
          key: question.id,
          prompt: "<p>#{question.body}</p>",
          validResponse: [question.lesson_keyword_id],
          use_data: question.use_data,
          question_body: question.question_body,
          question_video_url: question.question_video_url,
          question_fileboy_image_id: question.question_fileboy_image_id,
          options: keywords.to_a.shuffle.map do |keyword|
            {
              label: keyword.body,
              value: keyword.id,
              fileboyId: keyword.fileboy_image_id
            }
          end,
        }
      end
    end

    def ordered_slide_ids=(ids)
      self.slide_ids = ids
      ids.each_with_index do |id, index|
        Slide.where(id: id).update_all(weight: index)
      end
    end

    def clear_awaiting_marks_cache
      update(pupils_awaiting_marks_count_cache: {})
    end

    def quiz_results_for_teacher(teacher)
      quiz_attempts.where(user_id: teacher.pupils.select(:id))
    end

    def best_word_search_result
      word_search_results.minimum(:time_taken)
    end

    ENQUIRY_TYPES = {
      0 => {
        1 => 'Ask questions to explore different answers',
        2 => 'Using simple equipment',
        3 => 'Performing simple tests',
        4 => 'Identifying and grouping',
        5 => 'Make observations and to suggest answers to questions',
        6 => 'Gather data to answer questions'
      },
      1 => {
        1 => 'Asking simple questions and recognising that they can be answered in different ways',
        2 => 'Observing closely, using simple equipment',
        3 => 'Performing simple tests',
        4 => 'Identifying and classifying',
        5 => 'Using their observations and ideas to suggest answers to questions',
        6 => 'Gathering and recording data to help in answering questions'
      },
      2 => {
        1 => 'Asking relevant questions and using different types of scientific enquiries to answer them',
        2 => 'Setting up simple practical enquiries, comparative and fair tests',
        3 => 'Making systematic and careful observations and, where appropriate, taking accurate measurements using standard units, using a range of equipment, Including thermometers and data loggers',
        4 => 'Gathering, recording, classifying and presenting data in a variety of ways to help in answering questions',
        5 => 'Recording findings using simple scientific language, drawings, labelled diagrams, keys, bar charts, and tables',
        6 => 'Reporting on findings from enquiries, including oral and written explanations, displays or presentations of results and conclusions',
        7 => 'Using results to draw simple conclusions, make predictions for new values, suggest improvements and raise further questions',
        8 => 'Identifying differences, similarities or changes related to simple scientific ideas and processes',
        9 => 'Using straightforward scientific evidence to answer questions or to support their findings.'
      },
      3 => {
        1 => 'Planning different types of scientific enquiries to answer questions, including recognising and controlling variables where necessary',
        2 => 'Taking measurements, using a range of scientific equipment, with increasing accuracy and precision, taking repeat readings when appropriate',
        3 => 'Recording data and results of increasing complexity using scientific diagrams and labels, classification keys, tables, scatter graphs, bar and line graphs',
        4 => 'Using test results to make predictions to set up further comparative and fair tests',
        5 => 'Reporting and presenting findings from enquiries, including conclusions, causal relationships and explanations of and a degree of trust in results, in oral and written forms such as displays and other presentations',
        6 => 'Identifying scientific evidence that has been used to support or refute ideas or arguments'
      }
    }.freeze

    def self.enquiry_type(str)
      a, b = str.split('_').map(&:to_i)
      ENQUIRY_TYPES[a][b]
    end

    def enquiry_types
      super.map { |x| self.class.enquiry_type(x) }
    end

    # returns the lesson scheduled closest to the current time
    def lesson_for_user(user)
      return unless user
      # The absolute difference between the scheduled time and the current time
      query = "@EXTRACT(epoch FROM age(lesson_lessons.time, #{escape Time.current})) ASC NULLS LAST"
      user.lessons.where(template_id: id).order(query).first
    end

    # returns a set of slide ids suitable for the given user
    def slide_ids_for_presentation(user, lesson = nil)
      return slides.where.not(slide_type: :previous_keywords).ids unless user

      result = slides

      if lesson ||= lesson_for_user(user)
        form_ids = lesson.form.lessons.ordered.ids
        form_index = form_ids.index(lesson.id)
      end

      # Remove the previous_keywords slide unless we're in a form and it's not the first lesson
      result = result.where.not(slide_type: :previous_keywords) unless previous(user, lesson)

      result = result.ids

      # Add the course_intro slide if it's the first lesson of the form
      result = result.insert(1, Slide.global.course_intro.first&.id) if form_index == 0

      result.compact
    end

    def previous(user = nil, lesson = nil)
      return lesson.previous&.template if lesson
      if user
        user.lessons.ordered.find_by(template: self)&.previous&.template
      else
        self.class.ordered.where('machine_name < ?', machine_name).last
      end
    end

    def presentation_data(user, locale)
      lesson ||= lesson_for_user(user)
      query = load_sql('lesson/template/presentation').strip_comments.compact.assign(
        id: id,
        previous_id: previous(user, lesson)&.id,
        slide_ids: slide_ids_for_presentation(user, lesson),
        rocket_word_text: LOCALE_TEXT[locale][:todays_rocket_word_quiz],
        previous_rocket_word_text: LOCALE_TEXT[locale][:previous_rocket_word_text]
      )
      data = JSONString.new(query.run.values[0][0])

      puts data

      data.merge({ 'country_machine_name' => country&.machine_name })
    end

    before_validation(on: :create) { build_group(machine_name: machine_name) unless group }
    after_create { group.save! if group.new_record? }
    after_save do
      next unless group
      if group.templates.none?
        group.destroy!
      elsif group.main_template.id == id
        group.update!(machine_name: machine_name)
      end
    end

    def destroy * args
      raise 'Cannot delete template with lessons referencing it' if lessons.exists?
      super(*args)
    end

    def mark_as_complete(user)
      PresentationProgress.set_slide(user.id, id, nil, true)
    end

    def mark_quip_quiz_as_complete(user)
      PresentationProgress.set(user.id, id, quip_quiz_completed: true)
    end

    def mark_keyword_quiz_as_complete(user)
      PresentationProgress.set(user.id, id, keyword_quiz_completed: true)
    end

    def mark_word_search_as_complete(user)
      PresentationProgress.set(user.id, id, word_search_completed: true)
    end

    def complete?(user)
      PresentationProgress.get(user.id, id)[:completed]
    end

    def pupil_data(user)
      presentation_progresses.where(user_id: user.pupils).pluck_first(<<-SQL.squish)
        jsonb_build_object(
          'watched_ids',
          coalesce(JSON_AGG(DISTINCT user_id) FILTER (WHERE completed), '[]'),
          'keyword_quiz_complete_ids',
          coalesce(JSON_AGG(DISTINCT user_id) FILTER (WHERE keyword_quiz_completed), '[]'),
          'quip_quiz_complete_ids',
          coalesce(JSON_AGG(DISTINCT user_id) FILTER (WHERE quip_quiz_completed), '[]'),
          'word_search_complete_ids',
          coalesce(JSON_AGG(DISTINCT user_id) FILTER (WHERE word_search_completed), '[]')
        )
      SQL
    end

    def last_viewed_at(user)
      PresentationProgress.get(user.id, id)[:updated_at]
    end

    def document_links
      documents
        .where(skip_translation: false)
        .map { |document| { name: document.name, id: document.fileboy_file_id } }
        .reject { |doc| doc[:id].blank? }
    end

    def video_slides_for_translation
      slides.where(skip_translation: false).where.not(jw_id: nil)
    end

    def video_links
      videos = slides.map.with_index do |slide, i|
        { name: "Slide #{i + 1}", id: slide.jw_id } if slide.jw_id.present? && !slide.skip_translation
      end
      videos.compact.reject { |video| video[:id].blank? }.map do |video|
        { name: video[:name], url: "https://cdn.jwplayer.com/videos/#{video[:id]}.mp4" }
      end
    end

    def duplicate(opts = {})
      transaction do
        copy = Template.create!(
          name: name,
          machine_name: "#{opts.dig(:machine_name) || machine_name}-dup",
          available: false,
          awaiting_translation: false,
          country: country,
          available_country_ids: available_country_ids
        )

        Dump.dump(self)

        # Quip stores huge amounts of image data, so avoid loading it into memory too many times
        sql = sql(<<-SQL.squish)
          INSERT INTO lesson_dumps(template_id, raw_data, content, created_at, updated_at)
          SELECT :copy_id, raw_data, content, :time, :time
          FROM lesson_dumps
          WHERE template_id = :original_id
        SQL
        sql.assign(original_id: id, copy_id: copy.id, time: Time.current).run

        Dump.restore(copy, nil, duplicate: true, lite: !!opts.dig(:lite))

        copy.update!(name: "#{name} (copy)")

        copy
      end
    end

    def self.serialized_for_index_request
      includes(:documents).map do |x|
        x.as_json.merge(
          {
            'document_translation_progress' => {
              count: x.documents.count,
              of: x.group.main_template.documents.where(skip_translation: false).count,
            },
            'video_translation_progress' => {
              count: x.slides.where.not(vtt_file_uid: nil).count,
              of: x.group&.main_template&.video_slides_for_translation&.count,
            }
          }
        )
      end
    end

    def create_default_presentation
      slides.create!(
        %i[
          intro text previous_keywords keywords video
          text text text text text text text text text text
          expert text mission_assignment text keywords quiz
        ].map.with_index { |type, i| { weight: i, slide_type: type } }
      )
    end

    def preferred_new_library_unit_for_user(user)
      curriculum = user&.school&.new_library_curriculum
      new_library_units.find_by(curriculum: curriculum) || new_library_units.first
    end

    def data_for_user(user)
      {
        best_result_for_lesson_current_user: word_search_results.where(user: user).minimum(:time_taken),
        lesson_override_id: default_template_id_for_user(user),
        new_presentation_id: (user_generated || new_presentation&.published_for(user)) ? new_presentation&.id : nil,
        year: new_library_years.where(curriculum_id: user&.school&.new_library_curriculum&.id || 1).first
      }
    end

    def data_for_show
      template_authors = []
      if user_generated
        template_user = user
        if template_user.organisation
          template_authors << template_user.organisation.author if template_user.organisation.author
        elsif template_user.author
          template_authors << template_user.author
        end
      else
        template_authors = authors
      end

      as_json.merge(
        {
          keywords: keywords.map { |x| x.as_json.merge(lesson_slide_id: x.quiz_question&.lesson_slide_id) },
          expert_slide: slides.find_by(slide_type: 'expert'),
          mission_assignment_slide: slides.find_by(slide_type: 'mission_assignment'),
          slide_count: slides.count,
          years: new_library_years,
          plan_learning_outcomes: plan_learning_outcomes.map { |x| x if x['body'].present? }.compact,
          plan_activities: plan_activities,
          plan_assessment_questions: plan_assessment_questions,
          plan_assessment_marks: plan_assessment_marks,
          plan_assessment_phrases: plan_assessment_phrases,
          teacher_mastery: teacher_mastery,
          plan_national_curriculum: plan_national_curriculum,
          plan_curriculum_of_excellence: plan_curriculum_of_excellence,
          plan_international_baccalaureate: plan_international_baccalaureate,
          plan_early_years_framework: plan_early_years_framework,
          plan_scientific_enquiry_type: plan_scientific_enquiry_type,
          plan_working_scientifically_skills: plan_working_scientifically_skills,
          plan_century_skills_for_life: plan_century_skills_for_life,
          plan_cross_curriculum_opportunities: plan_cross_curriculum_opportunities,
          plan_cbse: plan_cbse,
          plan_kingdom_of_saudi_arabia: plan_kingdom_of_saudi_arabia,
          plan_next_generation_science_standards: plan_next_generation_science_standards,
          plan_chinese_compulsory_education_primary_school_science: plan_chinese_compulsory_education_primary_school_science,
          documents: documents.order(name: :asc).map { |x| { id: x.id, name: x.name, fileboy_file_id: x.fileboy_file_id, for_pupil: !!x.for_pupil } },
          document_ids: document_ids,
          authors: template_authors,
          author_ids: template_authors.pluck(:id),
          available_country_ids: available_country_ids,
          best_word_search_result: best_word_search_result,
          career_tag_ids: career_tags.map(&:id),
          career_tags: career_tags,
          key_stages: assessment_frameworks.select(:key_stage),
          unit_ids: new_library_units.ids,
          recommended_template_ids: recommended_template_ids,
          recommended_by_template_ids: recommended_by_template_ids,
          recommended_career_ids: recommended_career_ids,
          exemplar_works: exemplar_works.approved,
          is_published: user_generated && !!available,
          published_state: if user_generated
                             available ? 'all_users' : 'unpublished'
                           else
                             new_presentation&.published
                           end,
          quip_quiz_id: quip_quiz&.id,
          presentation_rating: new_presentation&.rating,
          primary_unit: primary_new_library_unit,
          scientific_enquiry_types: scientific_enquiry_types,
          scientific_enquiry_type_ids: scientific_enquiry_types.ids,
          creator: user&.name || 'Developing Experts',
        }
      )
    end

    def default_template_id_for_user(user)
      return nil unless user
      parent_template = source_template || self
      id = parent_template
           .user_templates
           .where(is_default: true)
           .left_joins(:new_presentation, :user)
           .where(user: user)
           .where(available: true)
           .first&.id
      return nil if id == self.id
      id
    end

    def default_for_user(user)
      return self unless user
      # get the parent template // original template (user_generated have a source_template)
      parent = source_template || self
      # if the user is nil, return the original template
      # get the default template for the user or default to the parent
      parent.user_templates.find_by(user: user, is_default: true, available: true).presence || parent
    end

    def self.default_lesson_for_user(template_id, user)
      return nil unless user
      parent_template = find(template_id)
      parent_template = parent_template&.source_template || parent_template
      template = parent_template
                 .user_templates
                 .where(is_default: true)
                 .left_joins(:new_presentation, :user)
                 .where(user: user)
                 .where(available: true)
                 .first
      return nil if template&.id == parent_template&.id
      template
    end

    def download_content
      template = self

      unless ::Lesson::Dump.where(template: template).exists?
        main_template = template.group.main_template
        ::Lesson::Dump.dump(main_template)

        # Quip stores huge amounts of image data, so avoid loading it into memory too many times
        sql = sql(<<-SQL)
          INSERT INTO lesson_dumps(template_id, raw_data, content, created_at, updated_at)
          SELECT :template_id, raw_data, content, :time, :time
          FROM lesson_dumps
          WHERE template_id = :main_template_id
        SQL

        sql.compact.assign(
          main_template_id: main_template.id,
          template_id: template.id,
          time: Time.current
        ).run
      end

      content = ::Lesson::Dump.where(template: template).pluck(:content)[0]

      file = Tempfile.new
      file.write(JSON.pretty_generate(content))
      file.rewind
      [file, { filename: "#{template.machine_name}.json" }]
    end

    def upload_content(file)
      template = self

      data = nil
      begin
        data = JSON.parse(file.read.force_encoding('UTF-8').tr(65_279.chr, ''))
      rescue
        render json: { success: false, error: :notJson }
        return
      end

      begin
        ::Lesson::Dump.restore(template, data)
        template.update!(content_uploaded: true)
      rescue ::Lesson::Dump::RestoreError => e
        ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
        return { success: false, error: e.message }
      rescue => e
        ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
        return { success: false }
      end

      { success: true }
    end

    def file_links
      template = self
      main_template = template.group.main_template

      {
        documents: main_template.document_links,
        videos: main_template.video_links,
        contentUploaded: template.content_uploaded,
      }
    end

    def self.mark_book_data(pupil_ids, template_ids)
      load_sql('user/mark_book_data').strip_comments.compact.assign(
        user_filter_sql: sql('users.id IN (?)', pupil_ids),
        template_filter_sql: sql('lesson_templates.id IN (?)', template_ids),
        select_sql: sql(<<-SQL).compact,
          'templates' AS record_type,
          user_templates.template_id AS record_id,
          user_templates.template_name AS record_name,
          COALESCE(JSONB_AGG(DISTINCT units) FILTER (WHERE units is not null), '[]') AS collections
        SQL
        group_sql: sql('user_templates.template_id, user_templates.template_name'),
        order_sql: sql('user_templates.template_name'),
        row_count_sql: sql('user_id')
      )
    end

    def links
      links = []
      if film_link.present? || film_fileboy_video_id.present? || film_video_id.present?
        links << {
          link: film_link,
          fileboy_video_id: film_fileboy_video_id,
          name: "Career #{film_link.present? ? 'Link' : 'Film'}",
          video_id: film_video_id,
          resource: 'Career',
          id: 'career-links',
          link_type: 'career',
        }
      end

      if employer_link.present? || employer_fileboy_video_id.present? || employer_video_id.present?
        links << {
          link: employer_link,
          fileboy_video_id: employer_fileboy_video_id,
          name: "Employer #{employer_link.present? ? 'Link' : 'Film'}",
          video_id: employer_video_id,
          resource: 'Employer',
          id: 'employer-links',
          link_type: 'employer',
        }
      end

      if post_16_link.present? || post_16_fileboy_video_id.present? || post16_video_id.present?
        links << {
          link: post_16_link,
          fileboy_video_id: post_16_fileboy_video_id,
          name: 'Post 16 course',
          video_id: post16_video_id,
          resource: 'Post 16 Course',
          id: 'post-16-links',
          link_type: 'training_pathway',
        }
      end

      # Add a type so we can just check the type in the view
      links.map do |link|
        type = if !link[:fileboy_video_id].present? && !link[:video_id].present?
                 'link'
               else
                 link[:video_id].present? ? 'campaign' : 'video'
               end
        link.merge({ type: type })
      end
    end

    def generate_recommended_careers
      words = related_words
      careers = Career.career_tagged_with_all(career_tag_ids)
      careers = Career.career_tagged_with_any(career_tag_ids) if careers.empty?
      careers = Career.by_related_words(words) if careers.empty?

      words_sql = words.is_a?(ActiveRecord::Relation) ? words.select(:name).to_sql : escape(words)

      count_related_words_sql = <<-SQL.squish
      SELECT COUNT(related_words.name)
      FROM related_words
      JOIN career_taggings ON career_taggings.career_tag_id = related_words.career_tag_id
      JOIN lesson_templates ON career_taggings.taggable_id = #{id} AND career_taggings.taggable_type = 'Lesson::Template'
      JOIN careers ON career_taggings.taggable_id = careers.id AND career_taggings.taggable_type = 'Career'
      WHERE related_words.name IN (#{words_sql})
      SQL

      careers.order(Arel.sql("(#{count_related_words_sql}) DESC")).limit(6)
    end

    def rocket_word_quiz
      quiz = { "name": 'Rocket Words', id: id, options: { "showAnswersBetweenQuestions": true } }.with_indifferent_access
      quiz['questions'] = quiz_questions_formatted_for_quip.map do |question|
        question = question.with_indifferent_access

        if question['use_data'] == true
          question['question_data'] = {
            body: question['question_body'],
            video_url: question['question_video_url'],
            image_url: question['question_fileboy_image_id'] ? "https://www.developingexperts.com/file-cdn/images/get/#{question['question_fileboy_image_id']}?transform=resize:800x600;format:webp;quality:80" : nil,
          }
        elsif !question['use_data'] && question['slide_id'].present? && (slide = Slide.find(question['slide_id']))
          video = nil
          video = { type: 'fileboy', url: slide.fileboy_video_id } if slide.fileboy_video_id.present?
          video = { type: 'youtube', url: yt_convert_to_embed_url(slide.video_url) } if !video && slide.video_url.present? && youtube_url_check(slide.video_url)
          video = { type: 'vimeo', url: slide.video_url } if !video && slide.video_url.present? && vimeo_url_check(slide.video_url)
          question['question_data'] = {
            body: slide.body,
            video: video,
            image_url: slide.fileboy_image_id ? "https://www.developingexperts.com/file-cdn/images/get/#{slide.fileboy_image_id}?transform=resize:800x600;format:webp;quality:80" : nil,
          }
        else
          question['question_data'] = nil
        end

        question['limitChoices'] = { "min": 1, "max": 1 }
        { type: question['type'], id: question['key'].to_s, dataJson: question }
      end
      quiz.deep_transform_keys!(&:to_s)
      quiz
    end

    def user_publishable_tasks
      [
        {
          children: 'Your presentation must have at least 1 slide',
          state: slides.any?,
          required: true,
        },
        {
          children: 'Add an image',
          state: fileboy_image_id.present?,
          required: true,
        },
        {
          children: 'Add some keywords',
          state: keywords.length >= 1,
        },
        {
          children: 'Fill out the lesson plan',
          state: intent.present?,
        },
        {
          children: 'Create a quiz',
          state: quip_quiz.present?,
        }
      ]
    end

    def user_can_publish?
      # no tasks that are required and state is false
      !user_publishable_tasks.any? { |task| task[:required] == true && task[:state] == false }
    end

    def json_data
      TemplateJsonService.new(id).to_json
    end

    def attr_with_source_fallback(attr)
      value = send(attr)
      value = source_template.send(attr) if value.blank? && source_template
      value
    end
  end
end
