# == Schema Information
#
# Table name: questionnaire_options
#
#  id                          :bigint           not null, primary key
#  option_data                 :jsonb
#  questionnaire_answers_count :integer          default(0), not null
#  weight                      :integer
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  questionnaire_question_id   :bigint
#
# Indexes
#
#  index_questionnaire_options_on_questionnaire_question_id  (questionnaire_question_id)
#
class QuestionnaireOption < ApplicationRecord
  include HasCareerTags

  validates :option_data, :weight, presence: true
  belongs_to :questionnaire_question
  has_many :questionnaire_answers, dependent: :destroy
end
