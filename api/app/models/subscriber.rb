# == Schema Information
#
# Table name: subscribers
#
#  id                         :bigint           not null, primary key
#  active_products            :jsonb            not null
#  address_line1              :string
#  address_line2              :string
#  cancellation_date          :datetime
#  cancellation_reason        :text
#  city                       :string
#  country                    :string           default("GB")
#  free_subscription          :jsonb            not null
#  po_number                  :string
#  postal_code                :string
#  renewal_date               :date
#  silence_admin_notification :boolean          default(FALSE), not null
#  state                      :string
#  subscriber_type            :string
#  subscription_status        :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  stripe_customer_id         :string
#  stripe_subscription_id     :string
#  subscriber_id              :integer
#
# Indexes
#
#  index_subscribers_on_active_products  (active_products) USING gin
#
class Subscriber < ApplicationRecord
  FREE_SUBSCRIPTION_OPTIONS = [
    { label: 'AI', key: 'ai' },
    { label: 'Science', key: 'science' },
    { label: 'Geography', key: 'geography' },
  ].freeze

  belongs_to :subscriber, polymorphic: true
  has_one :subscription_creator, dependent: :destroy
  has_one :creator, through: :subscription_creator, source: :user

  validate :free_subscription_reason_required

  # Add a scope for eager loading common associations
  scope :with_associations, -> { includes(:subscriber) }

  # Scope to join both User and School tables for searching
  scope :with_search_joins, -> {
    joins(
      "LEFT JOIN users ON subscribers.subscriber_type = 'User' AND subscribers.subscriber_id = users.id"
    ).joins(
      "LEFT JOIN schools ON subscribers.subscriber_type = 'School' AND subscribers.subscriber_id = schools.id"
    ).joins(
      'LEFT JOIN uk_schools ON schools.uk_school_id = uk_schools.id'
    )
  }

  scope :search_by_name_or_email, -> (term) {
    search_term = "%#{term}%"
    with_search_joins.where(
      'users.name ILIKE ? OR users.email ILIKE ? OR schools.name ILIKE ? OR schools.finance_email ILIKE ?',
      search_term, search_term, search_term, search_term
    )
  }

  # Search scope
  scope :search, -> (term) {
    search_term = "%#{term}%"
    with_search_joins.where('
      users.name ILIKE :q OR
      users.email ILIKE :q OR
      schools.name ILIKE :q OR
      schools.finance_email ILIKE :q OR
      schools.postcode ILIKE :q OR
      uk_schools.urn::text ILIKE :q OR
      uk_schools.postcode ILIKE :q
    ', q: search_term)
  }

  # Sorting scopes
  scope :order_by_name, -> (direction = 'asc') {
    with_search_joins.order("COALESCE(users.name, schools.name) #{direction}")
  }

  scope :order_by_email, -> (direction = 'asc') {
    with_search_joins.order("COALESCE(users.email, schools.finance_email) #{direction}")
  }

  # Address validation
  validates :address_line1, :postal_code, :country, presence: true, if: :address_required?

  after_save :sync_trial_dates_if_needed
  def sync_trial_dates_if_needed
    sync_trial_dates_to_hubspot if saved_change_to_free_subscription?
  end

  # Check if a product is accessible
  def has_access_to?(product_key)
    # Check both free and paid access - user has access if either is true
    has_free_access = free_account_enabled?(product_key)
    has_paid_access = active? && (active_products['products'] || []).include?(product_key.to_s)
    has_bypass = has_bypass_for?(product_key)

    has_free_access || has_paid_access || has_bypass
  end

  # Check if a product is enabled for free accounts
  def free_account_enabled?(product_key)
    return false unless product_key

    # Check if the product is in the free subscription options and not expired
    free_subscription_options.any? do |option|
      option.key == product_key.to_s && option.enabled && option.active?
    end
  end

  def has_paid_access_to?(product_key)
    return false unless active?
    (active_products['products'] || []).include?(product_key.to_s)
  end

  def available_products
    paid_products = active? ? (active_products['products'] || []) : []
    free_products = free_subscription_options.select(&:enabled).select(&:active?).map(&:key)

    (paid_products + free_products).uniq
  end

  # Check if the subscriber has any services marked as free
  def has_any_free_subscription?
    free_subscription_options.any?(&:active?)
  end

  # Check if the subscriber has all services marked as free
  def has_free_subscription?
    free_subscription_options.all?(&:active?)
  end

  def has_bypass?
    %i[ai science geography].any? do |product|
      has_bypass_for?(product)
    end
  end

  def has_bypass_for?(product_key)
    return false unless product_key
    # Check if the product is in the free subscription options and has bypass enabled
    free_subscription_options.any? do |option|
      option.key == product_key.to_s && option.bypass?
    end
  end

  def free_subscription_options
    Subscriber::FREE_SUBSCRIPTION_OPTIONS.map do |obj|
      key = obj[:key]
      label = obj[:label]
      body = free_subscription["#{key}_body"]
      enabled = free_subscription[key] == '1'
      bypass = free_subscription["bypass_#{key}"] == '1'
      expires_at = begin
        free_subscription["#{key}_expires_at"].present? ? Date.parse(free_subscription["#{key}_expires_at"]) : nil
      rescue
        nil
      end
      trial_begins = begin
        free_subscription["#{key}_trial_start"].present? ? Date.parse(free_subscription["#{key}_trial_start"]) : nil
      rescue
        nil
      end

      started = trial_begins.nil? || trial_begins <= Date.current
      expired = expires_at.present? && expires_at < Date.current

      OpenStruct.new(
        key: key,
        label: label,
        body: body,
        trial_begins: trial_begins,
        expires_at: expires_at,
        enabled: enabled,
        started?: started,
        expired?: expired,
        active?: enabled && started && !expired,
        bypass?: bypass,
      )
    end
  end

  def active?
    %w[active active_until_period_end].include?(subscription_status)
  end

  # Sync subscription data from Stripe
  def sync_subscription_data
    unless stripe_subscription_id.present?
      update!(
        active_products: {
          'products' => [],
          'synced_at' => Time.current.iso8601,
          'subscription_status' => 'canceled',
          'sync_message' => 'No Stripe subscription ID'
        },
        renewal_date: nil,
        subscription_status: ''
      )
      return true
    end

    begin
      service = SubscriptionService.new(subscriber)
      subscription = service.get_subscription_details
      if subscription && %w[active active_until_period_end].include?(subscription.status)
        # Extract product information from subscription
        products = []

        subscription.items.data.each do |item|
          next unless item.price&.product
          product_id = item.price.product.id
          product_key = find_product_key_from_stripe_id(product_id)
          products << product_key if product_key
        end

        # Get renewal date (next billing date) from subscription
        next_billing_date = subscription.current_period_end ? Time.at(subscription.current_period_end).to_date : nil

        # Update renewal_month on subscriber's school if applicable
        update_subscriber_renewal_month(next_billing_date) if next_billing_date

        # Update the active_products field and renewal_date
        update!(
          active_products: {
            'products' => products,
            'synced_at' => Time.current.iso8601,
            'subscription_status' => subscription.status,
            'school_size' => service.get_current_plan_key_from_active_sub.to_s,
            'amount' => subscription.items&.data&.first&.plan&.amount / 100.0,
            'interval' => subscription.items&.data&.first&.plan&.interval,
            'sync_message' => ''
          },
          renewal_date: next_billing_date,
          subscription_status: subscription.status,
        )

        # If a referral exists, and it's not completed - check the invoice status & mark completed if paid
        referral = UserReferral.where(referred_user_id: user_or_creator.id).first if user_or_creator&.present?
        if referral.present? && referral.accepted? && subscription.latest_invoice.present?
          update_referral_status if subscription.latest_invoice.paid
        end

        Rails.logger.info "Synced subscription products for subscriber #{id}: #{products.join(', ')}"

        # Sync the school hubspot subscription status if school
        if subscriber.is_a?(School)
          current_billing_start_date = subscription.current_period_start ? Time.at(subscription.current_period_start).to_date : nil
          sync_school_subscription_to_hubspot(subscriber, products, next_billing_date, current_billing_start_date)
        end

        true
      elsif subscription
        # Subscription exists but is not active
        update(
          active_products: {
            'products' => [],
            'synced_at' => Time.current.iso8601,
            'subscription_status' => subscription.status,
            'sync_message' => ''
          },
          renewal_date: nil,
          subscription_status: subscription.status,
        )

        Rails.logger.info "Synced subscription status for subscriber #{id}: #{subscription.status} (no active products)"
        true
      else
        # No subscription found
        update(
          active_products: {
            'products' => [],
            'synced_at' => Time.current.iso8601,
            'subscription_status' => '',
            'sync_message' => 'No active subscription found',
          },
          renewal_date: nil,
          subscription_status: '',
        )

        Rails.logger.info "No subscription found for subscriber #{id}, marked as cancelled"
        false
      end
    rescue => e
      # merge in the error message to the active_products hash
      update(active_products: (active_products.presence || {}).merge('sync_error' => e.message, 'synced_at' => Time.current.iso8601))
      Rails.logger.error "Error syncing subscription data for subscriber #{id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  # Delegate name and email to the associated model
  def name
    if subscriber.is_a?(School)
      subscriber.name
    elsif subscriber.is_a?(User)
      subscriber.name
    end
  end

  def email
    if subscriber.is_a?(School)
      subscriber.finance_email
    elsif subscriber.is_a?(User)
      subscriber.email
    end
  end

  # Convenience method to set creator
  def set_creator(user)
    return unless user.is_a?(User)

    # Clear any existing creator first to maintain single creator relationship
    SubscriptionCreator.where(subscriber_id: id).destroy_all

    # Create new association
    SubscriptionCreator.create(user: user, subscriber: self)
  end

  # CC email the creator if different from the creator's email
  def cc_email
    creator_email = creator&.email
    creator_email unless creator_email == email
  end

  def free_subscription_for_form
    data = free_subscription || {}

    # Convert date strings to Date objects for form fields
    %w[ai science geography].each do |product|
      date_keys = ["#{product}_expires_at", "#{product}_trial_start"]
      date_keys.each do |date_key|
        next unless data[date_key].present? && data[date_key].is_a?(String)

        begin
          data[date_key] = Date.parse(data[date_key])
        rescue ArgumentError
          data[date_key] = nil
        end
      end
    end

    data
  end

  def free_trial_dates_for_hubspot
    mutation_data = {}
    free_subscription_options.each do |product|
      if product.enabled
        # if enabled, set the date as '' to clear, else set the date
        mutation_data["#{product.key}_trial_start_date"] = hubspot_date_value(product.trial_begins)
        mutation_data["#{product.key}_trial_end_date"] = hubspot_date_value(product.expires_at)
      else
        # if not enabled, clear with ''
        mutation_data["#{product.key}_trial_start_date"] = ''
        mutation_data["#{product.key}_trial_end_date"] = ''
      end
    end
    mutation_data
  end

  def update_referral_status
    UserReferral.where(referred_user_id: user_or_creator.id)&.first&.request_referral_payout if user_or_creator&.present?
  end

  def user_or_creator
    subscriber.is_a?(User) ? subscriber : creator
  end

  private

  # Determine if address validation is required
  # This could be based on subscription type or other factors
  def address_required?
    # For now, we'll require address for all subscribers
    # This can be modified based on specific business rules
    subscription_status.present? && %w[active past_due active_until_period_end].include?(subscription_status)
  end

  def find_product_key_from_stripe_id(stripe_product_id)
    # Look up product key from StripeProductPrice table
    product_record = StripeProductPrice.find_by(
      test_product_id: stripe_product_id
    ) || StripeProductPrice.find_by(
      live_product_id: stripe_product_id
    )

    product_record&.product
  end

  def update_subscriber_renewal_month(renewal_date)
    # Only update school renewal month if subscriber is a School
    return unless subscriber.is_a?(School) && renewal_date.present?
    subscriber.update_column(:renewal_month, renewal_date.month)
  end

  def hubspot_date_value(date)
    return '' unless date.is_a?(Date) || date.is_a?(Time)
    date.to_date
  end

  def sync_school_subscription_to_hubspot(school, products, renewal_date, current_billing_start_date)
    # Only sync if the school has a HubSpot ID and a private app token is set
    # Also check if the subscription status is active
    return unless school.is_a?(School) && school.hubspot_id.present?
    return unless ENV['HUBSPOT_PRIVATE_APP_TOKEN'].present?
    return unless school.hubspot_id.present?

    require 'hubspot-api-client'
    access_token = ENV['HUBSPOT_PRIVATE_APP_TOKEN']
    client = Hubspot::Client.new(access_token: access_token)

    begin
      # Prepare properties to sync to HubSpot
      properties = {
        geography_subscription: products.include?('geography'),
        ai_subscription_enabled: products.include?('ai'),
        finance_contact_email: subscriber.finance_email,
        finance_contact_name: subscriber.finance_name,
        subscription_status: products.include?('science') ? 'Subscribed' : 'Trial Ended',
        renewal_date: products.include?('science') ? renewal_date : nil,
        geography_renewal_date: products.include?('geography') ? renewal_date : nil,
        subscription_start_date: products.include?('science') ? current_billing_start_date : nil,
        ai_sub_start_date: products.include?('ai') ? current_billing_start_date : nil,
        geography_sub_start_date: products.include?('geography') ? current_billing_start_date : nil,
        # trial dates
        **free_trial_dates_for_hubspot
      }

      # Filter out nil values
      properties.compact!

      # Update company in HubSpot
      client.crm.companies.basic_api.update(
        company_id: school.hubspot_id,
        simple_public_object_input: { properties: properties }
      )

      { success: true }
    rescue => e
      # Log the error and return failure
      message = "Failed to sync school to HubSpot: #{e.message}"
      Rails.logger.error(message)
      school.update_column(:hubspot_errors, (school.hubspot_errors || []) + [message])
      { success: false, message: message }
    end
  end

  def sync_trial_dates_to_hubspot
    # Only sync if the school has a HubSpot ID and a private app token is set
    # Also check if the subscription status is active
    return if Rails.env.test?
    return unless subscriber.is_a?(School) && subscriber.hubspot_id.present?
    return unless ENV['HUBSPOT_PRIVATE_APP_TOKEN'].present?
    return unless subscriber.hubspot_id.present?

    require 'hubspot-api-client'
    access_token = ENV['HUBSPOT_PRIVATE_APP_TOKEN']
    client = Hubspot::Client.new(access_token: access_token)

    begin
      # Update company in HubSpot
      client.crm.companies.basic_api.update(
        company_id: subscriber.hubspot_id,
        simple_public_object_input: { properties: free_trial_dates_for_hubspot }
      )

      { success: true }
    rescue => e
      # Log the error and return failure
      message = "Failed to sync school free subscription dates to HubSpot: #{e.message}"
      Rails.logger.error(message)
      subscriber.update_column(:hubspot_errors, (subscriber.hubspot_errors || []) + [message])
      { success: false, message: message }
    end
  end

  def free_subscription_reason_required
    return unless free_subscription.present?

    %w[ai science geography].each do |product|
      errors.add(:base, "Reason is required for free #{product.humanize} subscription") if free_subscription[product] == '1' && free_subscription["#{product}_body"].blank?
    end
  end
end
