# == Schema Information
#
# Table name: questionnaires
#
#  id                             :bigint           not null, primary key
#  include_demographics_questions :boolean          default(FALSE)
#  is_onboarding_questionnaire    :boolean          default(FALSE)
#  name                           :string
#  published                      :boolean          default(FALSE)
#  questionnaire_questions_count  :integer          default(0), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  new_library_unit_id            :bigint
#
# Indexes
#
#  index_questionnaires_on_new_library_unit_id  (new_library_unit_id)
#
class Questionnaire < ApplicationRecord
  validates :name, presence: true
  has_many :questionnaire_questions, dependent: :destroy
  has_many :questionnaire_answers, through: :questionnaire_questions
  has_many :questionnaire_users, dependent: :destroy
  belongs_to :new_library_unit, class_name: 'NewLibrary::Unit', optional: true

  def duplicate
    new_questionnaire = self.dup
    new_questionnaire.name = "#{self.name} (copy)"
    if new_questionnaire.save
      self.questionnaire_questions.each do |question|
        new_question = question.dup
        new_question.questionnaire_id = new_questionnaire.id
        new_question.save
        question.questionnaire_options.each do |option|
          new_option = option.dup
          new_option.questionnaire_question_id = new_question.id
          new_option.save
        end
      end
    end
    return new_questionnaire
  end

  def v2_as_json(options=nil)
    as_json().merge({ new_library_unit: new_library_unit  })
  end
end
