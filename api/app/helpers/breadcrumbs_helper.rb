# app/helpers/breadcrumbs_helper.rb
module B<PERSON><PERSON><PERSON>bsHelper
  def add_root_breadcrumb user, pupil = nil
    # Add root breadcrumb based on user type
    @breadcrumbs ||= []
    if pupil.present? || user&.pupil?
      add_breadcrumb "Dashboard", "/pupil"
    elsif user&.teacher?
      add_breadcrumb "Dashboard", "/school/dashboard"
    else
      add_breadcrumb "Home", root_path
    end
  end

  def add_breadcrumb(name, path = nil)
    @breadcrumbs ||= []
    @breadcrumbs << [name, path]
  end

  def breadcrumbs_any?
    @breadcrumbs.present?
  end

  def render_breadcrumbs(dark_text: true, omit_text_color: false)
    return "" if @breadcrumbs.blank?

    breadcrumb_html = content_tag(:nav, aria: { label: "Breadcrumb" }, class: "text-sm") do
      content_tag(:ol, class: "flex flex-wrap #{omit_text_color ? '' :dark_text ? 'text-gray-500' : 'text-gray-300'} space-x-2") do
        @breadcrumbs.map.with_index do |(name, path), index|
          is_last = index == @breadcrumbs.size - 1
          classes = "flex items-center space-x-2"

          content_tag(:li, class: classes) do
            output = ""
            if index > 0
              output += content_tag(:span, "›", class: "mx-1 #{omit_text_color ? '' : dark_text ? 'text-gray-400' : 'text-gray-200'}")
            end

            if path && !is_last
              output += link_to(name, path, class: "hover:#{omit_text_color ? '' : dark_text ? 'text-gray-700' : 'text-white'} transition-colors")
            else
              output += content_tag(:span, name, class: "#{omit_text_color ? '' : dark_text ? 'text-gray-700' : 'text-white'} font-medium")
            end
            output.html_safe
          end
        end.join.html_safe
      end
    end

    breadcrumb_html + breadcrumb_json_ld.html_safe
  end

  def build_contextual_breadcrumbs
    add_context_from_params
    yield
  end

  private

  def breadcrumb_json_ld
    return "" if @breadcrumbs.blank?

    items = @breadcrumbs.map.with_index do |(name, path), i|
      item_data = {
        "@type": "ListItem",
        "position": i + 1,
        "name": name
      }
      
      # Only include 'item' field if we have a valid path
      if path.present?
        # Ensure we have a full URL for schema.org
        item_url = path.start_with?('http') ? path : "#{request.base_url}#{path}"
        item_data["item"] = item_url
      end
      
      item_data
    end

    json = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": items
    }

    # Ensure the JSON output is NOT HTML-escaped
    tag.script(type: "application/ld+json") { json.to_json.html_safe }
  end

  def add_context_from_params
    add_breadcrumb "Dashboard", helpers.universal_dashboard_link
    routes = Rails.application.routes.url_helpers

    case params[:from_context]
    when 'pupil'
      pupil = Pupil.find_by(id: params[:from_id])
      if pupil
        add_breadcrumb "Pupils", "/school/pupils"
        add_breadcrumb pupil.name, "/school/pupils/#{pupil.id}/edit"
        return true
      end
    when 'class'
      form = Form.find_by(id: params[:from_id])
      if form
        add_breadcrumb "Classes", "/school/classes"
        add_breadcrumb form.name, "/school/classes/#{form.id}/pupils"
        return true
      end
    when 'teacher'
      teacher = Teacher.find_by(id: params[:from_id])
      if teacher
        add_breadcrumb "Teachers", "/school/teachers"
        add_breadcrumb teacher.name, "/school/teachaers/#{teacher.id}/edit"
        return true
      end
    end

    false
  end
end