# frozen_string_literal: true

class HorizontalCardComponent < ViewComponent::Base
  def initialize(tags: nil, title:, pre_title: nil, image_background_img: nil, image_background_fileboy_id: nil, image: nil, image_fileboy_id: nil, overlay_image: nil, overlay_image_fileboy_id: nil, image_backround_color: nil, body: nil, path: nil, background_color: "bg-white", color: "text-gray-900", large: false, path_text: "View", alt_text: nil)
    fileboy_background_image = image_background_fileboy_id ? "https://www.developingexperts.com/file-cdn/images/get/#{image_background_fileboy_id}?transform=resize:600x300~fit:cover;format:webp;quality:75" : nil
    fileboy_image = image_fileboy_id ? "https://www.developingexperts.com/file-cdn/images/get/#{image_fileboy_id}?transform=resize:600x300~fit:contain;format:webp;quality:75" : nil

    @tags = tags
    @title = title
    @background = fileboy_background_image || image_background_img
    @image = fileboy_image || image
    @alt_text = alt_text.nil? ? title : alt_text

    @image_backround_color = image_backround_color
    @body = body
    @path = path
    @background_color = background_color
    @color = color

    @large = large

    @path_text = path_text
    @pre_title = pre_title
  end
end
