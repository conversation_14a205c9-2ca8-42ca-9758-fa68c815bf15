# frozen_string_literal: true

Rails.application.routes.draw do
  require 'sidekiq/web'
  mount Sidekiq::Web => '/sidekiq'

  resource :subscription, only: %i[new create show edit update] do
    collection do
      get :cancel
      post :process_cancellation
      post :reactivate
      get :success
      post :validate_discount
      post :update_po_number

      get :customer_portal # Redirects to Stripe Customer Portal
    end
  end
  post 'stripe/webhooks', to: 'stripe_webhooks#create'

  get 'events/campaign/:id/click', to: 'static/trackings#campaign'
  get 'events/link_view', to: 'static/trackings#link_view'
  get 'events/film-view', to: 'static/trackings#film_view'

  # Only in development
  if Rails.env.development?
    resources :test_webhooks, only: [:index] do
      collection do
        post :trigger
        post :retry_events
        get :view_event
      end
    end
  end

  resources :tours, path: '/tours', controller: 'static/tours' do
    get :tour_videos, on: :collection
  end
  get '/tours/:id', to: 'static/tours#show'

  post 'dev_login', to: 'sessions#dev_login', as: :dev_login if Rails.env.development?

  get '/auth/wonde_oauth', to: 'static/session_accounts#wonde_oauth_callback'
  get '/auth/my_login_oauth', to: 'static/session_accounts#my_login_oauth_callback'

  get '/auth/:provider/callback', to: 'sessions#oauth_callback'

  scope :api do
    get '/documents/:id', to: 'documents#document_url_redirect'
    get '/external-link/:id', to: 'links#view_external_link'
    scope :v1 do
      namespace :organisation do
        resources :new_library_units, only: %i[show]
      end
      namespace :admin do
        post '/users/:id/block', to: 'users#block'
        post '/users/:id/unblock', to: 'users#unblock'

        get '/deleted_teachers', to: 'teachers#deleted_teachers'
        resources :new_library_units, only: %i[show edit update create index destroy] do
          member { post :update_template_order }
        end

        post '/get_lesson_template_results_for_select_list', to: 'lesson_templates#get_results_for_select_list'
        post '/get_library_unit_results_for_select_list', to: 'new_library_units#get_results_for_select_list'
        post '/get_library_year_results_for_select_list', to: 'new_library_years#get_results_for_select_list'

        resources :reports, only: %i[] do
          collection do
            get :download_teacher_reports
            get :download_school_reports
            get :download_champions_reports
            get :download_new_school_reports
            get :subscription_wonde_status_report
            get :download_schools_extended_stats
            get :download_schools_subscribed
            get :custom_lessons_report
            get :teacher_activity_by_units
          end
        end

        resources :lesson_templates, as: :api_1_lesson_templates do
          member do
            get :load_rocket_words_quiz
            get :file_links
            get :download_content
            post :upload_content
            post :duplicate
            get :presentation
            get :get_tag_suggestions
          end

          collection do
            post :get_form_ids_for_templates
          end
        end

        resources :sponsors do
          collection do
            get :for_select
          end
        end

        resources :lesson_groups do
          member { post :add_template }
        end

        resources :slides do
          collection { post :update_order }
        end

        resources :keywords do
          collection { post :update_order }
        end

        resources :schools, only: %i[create show edit update index destroy] do
          get :teachers_for_school
          get :forms_for_school

          collection do
            get :summary_stats
            get :for_select
          end

          member do
            get :send_welcome_emails
            post :assign_templates_for_forms
            post :send_multiple_welcome_emails
            post :delete_forms
          end
        end

        resources :countries do
          collection { get :for_select_list }
        end

        resources :teachers, only: %i[show edit update create index destroy] do
          collection do
            post :destroy_all
            post :permanently_delete
          end
          member do
            post :send_welcome_email
          end
        end

        resources :forms, only: %i[show edit update create index destroy] do
          collection do
            get :class_graph_data
            post :mass_destroy
          end

          member do
            post :add_pupils
            post :remove_pupils
            post :build_lessons
          end
        end

        resources :lesson_documents, only: %i[show edit update create index destroy] do
          collection do
            post :skip_translation_for_documents
            post :requires_translation_for_documents
          end
        end

        resources :users, as: :api_users
        resources :admins,
                  :authors,
                  :tutors,
                  :testimonials,
                  :new_library_documents,
                  :new_library_curricula,
                  :new_library_years,
                  :questions,
                  :template_assessments,
                  :risk_assessments,
                  :ranks,
                  :xls_imports,
                  :rewards,
                  only: %i[show edit update create index destroy]

        resources(:pupils, only: %i[show edit update create index destroy]) do
          collection do
            post :mass_destroy
          end

          member do
            get :lessons
          end
        end

        resources :lessons, only: %i[index show] do
          member do
            post :remove_from_form
            post :reschedule
            get :lesson_pupil_marks
            post :destroy_lesson_result_for_pupil
          end
        end
      end

      namespace :guardian do
        get '/dashboard', to: 'dashboard#show'
        #         get '/dashboard/get_sign_in_token_for_pupil/:id', to: "dashboard#get_sign_in_token_for_pupil"

        resources(:pupils, only: %i[show index])
        get '/motds/latest', to: 'motds#latest'

        resources :slides, only: %i[show] do
        end

        resources :lesson_templates, only: %i[show] do
          member do
            get :load_rocket_words_quiz
          end
        end

        resources :lessons, only: %i[index show]

        #         resources :all_lesson_templates, only: %i[]

        resources(
          :new_library_documents,
          :new_library_curricula,
          :new_library_years,
          :new_library_units,
          only: %i[show index]
        )
      end

      namespace :school do
        get '/dashboard', to: 'dashboard#show'
        get '/dashboard/get_sign_in_token_for_pupil/:id', to: 'dashboard#get_sign_in_token_for_pupil'

        resources :sponsors, only: %i[index show]

        resources :forms, only: %i[index show edit] do
          get :class_graph_data

          collection do
            get :forms_for_current_user
            get :forms_with_matching_template_lesson_count
            post :remove_templates
          end

          member do
            get :pupil_letter_data_for_form
            post :build_lessons
          end
        end

        resources :slides, only: %i[show] do
        end

        resources :all_forms, only: %i[index show edit update create destroy] do
          collection do
            get :forms_for_school
          end

          member do
            post :add_pupils
            post :remove_pupils
            post :build_lessons
            get :pupil_letter_data_for_form
          end
        end

        resources(
          :new_library_documents,
          :new_library_curricula,
          :new_library_years,
          only: %i[show index]
        )

        resources :new_library_units, only: %i[show index] do
          member do
            post :forms_assigned
          end
        end

        get '/reports', to: 'reports#show'
        get '/mark_book/:type', to: 'mark_book#show'

        resources :lesson_templates, only: %i[show] do
          member do
            post :update_pupil_results
            get :load_rocket_words_quiz
            get :presentation
          end

          collection do
            post :get_form_ids_for_templates
          end
        end

        resources :all_lesson_templates, only: %i[] do
          collection do
            post :get_form_ids_for_templates
          end
        end

        resources :pupils, only: %i[show edit update create index destroy] do
          member do
            get :lessons
            post :update_template_results
          end
        end

        resources :all_pupils, only: %i[show edit update create index destroy] do
          collection do
            post :mass_destroy
          end
        end

        resources :teachers, only: %i[show edit update index] do
          collection do
            get :get_teacher_account
          end
        end

        resources :all_teachers, only: %i[show edit update create index destroy]

        resources :lessons, only: %i[index show], as: :api_lesson do
          member do
            post :pupils_with_lessons_results
            post :update_pupils_lessons_results
            post :remove_from_form
            post :reschedule
          end
        end

        resources :xls_imports, only: %i[index show create update]
      end

      namespace :pupil do
        get '/dashboard', to: 'dashboard#show'
        get '/lessons', to: 'lessons#index'
        get '/lessons/:id', to: 'lessons#show'
        post '/quip_quiz_results/submit_results', to: 'quip_quiz_results#submit_results'

        resources :rewards

        resources :all_lesson_templates, only: %i[] do
          collection do
            post :get_form_ids_for_templates
          end
        end

        resources :lesson_templates, only: %i[show] do
          member do
            get :load_rocket_words_quiz
            get :pupil_documents
          end
        end

        resources :quiz_attempts, only: [:create]

        resources :slides, only: [:show] do
        end
      end

      namespace :anonymous do
        get '/schools/teacher_count', to: 'schools#teacher_count'
        get '/schools', to: 'schools#index'
        get '/lessons/:id', to: 'lessons#show'
        resources :slides, only: [:show]
        resources :units, only: [:show] do
          collection do
            post :homepage
          end
        end
        resources :lesson_templates, only: %i[show] do
          collection do
            post :homepage
          end
          member do
            get :load_rocket_words_quiz
          end
        end

        resources :countries do
          collection do
            get :for_select_list
            get :all_for_select_list
          end
        end
      end

      get '/library', to: 'app#library'
      get '/community_library', to: 'app#community_library'
      post '/create_word_search_result', to: 'app#create_word_search_result'

      get '/sessions/ip', to: 'sessions#is_ip_blacklisted'
      post '/sessions/signIn', to: 'sessions#sign_in_action'
      post '/sessions/signOut', to: 'sessions#sign_out_action'
      post '/sessions/signUp', to: 'sessions#sign_up'
      post '/sessions/tokenSignIn', to: 'sessions#token_sign_in'
      post '/sessions/swap_accounts', to: 'sessions#swap_accounts'
      post '/sessions/recover_password', to: 'sessions#recover_password'
      post '/sessions/reset_password', to: 'sessions#reset_password'
      get '/sessions/current', to: 'sessions#current'
      get '/sessions/intercom_data', to: 'sessions#intercom_data'

      post '/sessions/complete_sign_up', to: 'sessions#complete_sign_up'

      post '/sessions/validate_user', to: 'sessions#validate_user'

      ###############################################################
      post '/sessions/late_assign_uk_school', to: 'sessions#late_assign_uk_school'
      post '/sessions/teacher_create_profile', to: 'sessions#teacher_create_profile'

      post '/sessions/job_hunter_sign_up', to: 'sessions#job_hunter_sign_up'
      post '/sessions/job_hunter_create_profile', to: 'sessions#job_hunter_create_profile'
      ###############################################################

      get '/anon-site', to: 'app#anon_site'
      get '/twitter/feed', to: 'app#twitter_feed'
    end

    namespace :v2 do
      post '/error_logs', to: 'error_logs#create'
      resources :form_units do
        get :forms_without, on: :collection
        get :form_unit_questionnaires_to_take, on: :collection
        get :active_upcoming_units, on: :collection
      end
      resources :live_stream_documents, only: %i[create update destroy] do
        post :reorder, on: :collection
      end
      resources :live_streams, only: %i[create index show update destroy] do
        get :active_live_stream, on: :collection
        get :upcoming_live_streams, on: :collection
        get :past_live_streams, on: :collection
        post :publish_stream, on: :member
        post :unpublish_stream, on: :member
        post :pin_message, on: :member
      end
      resources :live_stream_messages, only: %i[create destroy index] do
        post :reply, on: :member
      end
      resources :questionnaires do
        post :statistics, on: :member
        post :take, on: :collection
        get :taken_questionnaire, on: :member
        post :user_took_questionnaire, on: :member
        post :duplicate, on: :member
      end
      resources :questionnaire_questions, only: %i[index show update create destroy] do
        post :reorder, on: :collection

        post :demographics_stats, on: :member
        post :demographics_counties, on: :member
      end
      resources :questionnaire_options, only: %i[create update destroy]
      resources :questionnaire_answers, only: %i[create]
      resources :new_library_units do
        post :reorder, on: :collection
      end
      resources :new_library_years do
        post :update_order, on: :collection
      end

      resources :new_library_curricula do
        get :show_data, on: :member
        get :for_preview, on: :member
        get :all_curricula, on: :collection
      end

      resources :hubspot_syncs, only: %i[index]
      resource :lesson_plan_views, only: %i[create] do
        post :create_for_self, on: :collection
        get :stats_for_school, on: :collection
      end
      resources :motds, only: %i[index]
      resources :quip_questions
      resources :countries, only: %i[index]
      resources :campaigns do
        get :predicted_reach, on: :member
        get :school_interactions_index, on: :member
      end

      resources :campaign_lessons
      resources :campaign_lesson_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end
      resources :campaign_lesson_external_clicks do
        collection do
          get :link_statistics
          get :demographics_data
        end
      end

      resources :campaign_vacancy_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end
      resources :campaign_vacancy_external_clicks do
        collection do
          get :link_statistics
          get :demographics_data
        end
      end

      resources :quizzes
      resources :quiz_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end

      resources :tours
      resources :tour_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end

      resources :campaign_courses
      resources :campaign_course_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end
      resources :campaign_course_external_clicks do
        collection do
          get :link_statistics
          get :demographics_data
        end
      end

      resources :campaign_events
      resources :campaign_event_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end
      resources :campaign_event_external_clicks do
        collection do
          get :link_statistics
          get :demographics_data
        end
      end

      resources :campaign_units do
        collection do
          get :units_index
        end
        post :update_lesson_templates, on: :member
      end
      resources :campaign_unit_views do
        collection do
          get :view_statistics
          get :demographics_data
        end
      end
      resources :campaign_unit_external_clicks do
        collection do
          get :link_statistics
          get :demographics_data
        end
      end

      resources :profile_views
      resources :profile_external_clicks

      resources :videos
      resources :video_views do
        collection do
          post :create_for_current_user
          get :view_statistics
          get :demographics_data
        end
      end

      post '/slack/video_error', to: 'slack#send_log_message'
      resources :events do
        member do
          get :view_external_link
          get :related_careers
          get :related_courses
          get :related_vacancies
          get :related_events
          get :related_lesson_templates
        end
      end
      resources :career_vacancies do
        get :vacancy_categories, on: :collection

        member do
          get :view_external_link
          get :related_careers
          get :related_courses
          get :related_vacancies
          get :related_events
          get :related_lesson_templates
        end
      end
      resources :career_courses do
        get :course_organisations, on: :collection

        member do
          get :view_external_link
          get :related_careers
          get :related_courses
          get :related_vacancies
          get :related_events
          get :related_lesson_templates
        end
      end
      resources :organisations do
        get :related_careers, on: :member
        get :summary_stats, on: :member
        get :lesson_template_index, on: :member
      end
      resources :uk_schools, only: [:index] do
        get :schools_without_assignment, on: :collection
        get :sign_up_attempts, on: :collection
        post :upload, on: :collection
      end
      resources :rankings, only: [] do
        get :logins_count, on: :collection
        get :logins_streak, on: :collection
        get :top_scorer, on: :collection
        get :average_summative_quiz_score, on: :collection
        get :average_rocket_quiz_score, on: :collection
        get :lesson_views, on: :collection
        get :achievement_points, on: :collection
        get :viewing_presentations, on: :collection
      end
      resources :years, only: %i[index show]
      resources :units, only: %i[index show] do
        get :templates_for_user, on: :member
        get :campaign_units_for, on: :member
        get :unit_for_show, on: :member
      end
      resources :curricula, only: %i[index show]
      resources :tracking_logins, only: [:index]
      resources :accreditations do
        post :update_weights, on: :collection
        get :unpaginated_index, on: :collection
      end
      resources :pupils do
        get :guardian_ids, on: :member
        get :login_code_csv, on: :collection
        get :my_tracking_data, on: :collection
        put :add_guardian, on: :member
        put :remove_guardian, on: :member
        get :mark_book_data, on: :member
        get :lessons_with_marks, on: :member
        get :next_lesson, on: :member
        get :unlocked_lessons, on: :member
        get :sign_in_token, on: :member
        get :qr_signin_link, on: :member
        post :award_points, on: :member
        get :active_homework, on: :member
      end
      resources :lessons do
        post :toggle_taught, on: :member
        post :generate_presentation_with_ai, on: :member
        post :generate_keywords_with_ai, on: :member
        post :generate_keyword_from_name, on: :member
        get :my_lessons, on: :collection
      end
      resources :forms do
        delete :removePupil, on: :member
        delete :removeTeacher, on: :member
        post :addTeacher, on: :member
        post :addPupil, on: :member
        post :remove_all_lessons, on: :member
        get :with_pupils, on: :collection
      end
      resources :trackings do
        post :chronological_data, on: :collection
        post :presentation_view_end, on: :collection
        post :track_film_viewed, on: :collection
        get :presentation_views, on: :collection
      end
      resources :schools do
        get :location_list, on: :collection
        get :stats, on: :member
        get :stats_admin, on: :member
        get :sync_hubspot, on: :member
        get :sync_hubspot_signup, on: :member
        get :sync_hubspot_subscribe, on: :member
        post :delete_all_pupils, on: :member
        get :can_sync_hubspot, on: :member
        post :merge_with_school, on: :member
        get :wonde_import, on: :member
        get :last_wonde_import, on: :member
        get :latest_wonde_import, on: :member
        get :request_wonde_access, on: :member
        get :wonde_check_to_archive, on: :member
        post :wonde_archive_records, on: :member
        get :anonymous_select_schools, on: :collection
        get :extended_index, on: :collection
        get :extended_index_csv, on: :collection
        get :schools_subscriptions_stats, on: :collection
        get :get_school_manage, on: :collection
        put :update_school_manage, on: :collection
        put :update_science_leaders, on: :collection
        get :resource_history, on: :member
        post :disable_wonde_auto_sync, on: :member
        member do
          delete :admin_wipe_data
          get :get_non_latest_wonde_import_data
          delete :delete_non_latest_wonde_import_data
          get :hubspot_details
          patch :update_hubspot_details
        end
        get :regions, on: :collection
      end

      resources :sign_up_events

      resources :curriculum_documents do
        post :reorder, on: :collection
      end

      resources :wonde_manager, only: [] do
        get :authorise, on: :collection
        get :request_access, on: :member
        get :dev_set_school, on: :member
        post :webhook_authorised_school, on: :collection
      end

      resources :my_login_manager, only: [] do
        get :authorise, on: :collection
      end

      resources :wonde_import, only: [:update] do
        get :wonde_import_forms, on: :member
        get :wonde_import_pupils, on: :member
        get :wonde_import_teachers, on: :member
        post :save_form_changes, on: :member
        post :save_pupil_changes, on: :member
        post :save_teacher_changes, on: :member
        post :save_import, on: :member
        get :wonde_import_errors, on: :member
        get :wonde_teachers_valid, on: :member
        get :wonde_import_teacher_ids, on: :member
      end

      resources :cache_statistics, only: [] do
        get :get_cache, on: :collection
      end

      resources :cache_statistics, only: [] do
        get :get_cache, on: :collection
      end

      resources :teachers do
        get :task_progress, on: :member
        get :stats, on: :member
      end
      resources :guardians

      resources :industry_tags
      resources :lesson_template_folders, only: %i[index create update destroy] do
        post :add_templates_to_folder, on: :member
      end
      resources :lesson_templates do
        get :data_for_user, on: :member
        get :template_versions, on: :member
        get :load_rocket_words_quiz, on: :member
        get :load_quiz_questions, on: :member
        get :primary_unit_for, on: :member
        get :recommended_careers, on: :member
        post :new_lesson_template_plans_index, on: :collection
        post :set_presentation, on: :member
        get :new_lesson_plan_pdf, on: :member
        post :new_lesson_plan_pdf, on: :member
        get :new_lesson_plan_pdfs, on: :collection
        get :presentation_rating, on: :member
      end
      resources :user_lesson_templates do
        post :reset, on: :member
        post :publish, on: :member
        post :unpublish, on: :member
        post :set_as_default, on: :member
        get :get_default, on: :member
        get :template_versions, on: :member
        get :presentation, on: :member
        get :quip_quiz, on: :member
        post :duplicate, on: :member
        post :duplicate_files, on: :member
      end
      resources :sponsors

      resources :lesson_slides, only: %i[index show] do
        get :narration, on: :member
        get :for_end_of_unit_assessment, on: :collection
        post :set_as_current, on: :member
      end

      resources :lesson_keywords, only: %i[index update create destroy] do
        post :update_order, on: :collection
        get :narration, on: :member
      end
      resources :lesson_documents, only: %i[index update create destroy]
      resources :authors, only: %i[index update create] do
        get :my_author, on: :collection
        get :my_organisation_author, on: :collection
      end

      resources :quiz_old_questions, only: [] do
        get :narration, on: :member
      end

      post '/quizzes/by-quiz-key/:quizKey', to: 'quip_quiz_proxy#quiz_data'
      post '/quizzes/increment-quiz-views/:quizId', to: 'quip_quiz_proxy#increment_view'
      post '/quizzes/increment-complete-count/:quizId', to: 'quip_quiz_proxy#increment_complete'

      resources :careers do
        get :career_families, on: :collection
        member do
          get :related_careers
          get :related_courses
          get :related_vacancies
          get :related_events
          get :related_lesson_templates
        end
      end

      post '/word_search_lobbies/user-stats', to: 'word_search_lobbies#user_stats'

      resources :word_search_lobbies do
        get :lobby_index, on: :collection
        post :join, on: :member
        post :leave, on: :member
        post :submitAnswer, on: :member
      end

      resources :word_search_lobby_users do
        post :increment_score, on: :member
        post :generate_words, on: :member
        post :word_pool, on: :member
      end

      resources :exemplar_works

      resources :users do
        member do
          get :career_feed
          get :user_provider
          post :update_user_provider_to_email
          post :track_template_viewed
          post :hubspot_contact_sync
          post :get_sign_in_token
        end
        collection do
          get :career_profile_user_stats
          get :hubspot_visitor_verification
          post :create_employer
        end
      end

      resources :training_routes

      resources :achievements, only: %i[index show create update destroy] do
        collection do
          get :data_for_pupil
          get :check_completion
        end
      end

      resources :admin_dashboard, only: [] do
        get :engagement_stats, on: :collection
        get :school_engagement_stats, on: :collection
        get :school_teacher_engagement_stats, on: :collection
      end

      resources :pupil_unit_marks, only: [] do
        get :marks_for, on: :collection
        post :set_mark, on: :collection
      end

      resources :quip_quizzes, only: %i[index show update destroy] do
        post :build_quiz, on: :collection
        post :update_quiz, on: :member
      end

      ##################
      # REFERRALS
      get '/referrals', to: 'referrals#index'
      get '/referrals/referrals_for', to: 'referrals#referrals_for'
      get '/referrals/my_data', to: 'referrals#my_data'
      ##################

      resources :career_tags, only: %i[index show create update destroy] do
        collection do
          post :create_from_array
        end
      end

      resources :debug, only: [] do
        collection do
          get :tracking_stats
          get :user_lesson_template_stats
        end
      end

      resources :scientific_enquiry_types

      post '/leaderboards/leaderboard2', to: 'leaderboards#leaderboard2'
      post '/leaderboards/my_leaderboard2', to: 'leaderboards#my_leaderboard2'
      post '/leaderboards/dashboard2', to: 'leaderboards#dashboard2'

      resources :de_socials
      resources :de_medias

      resources :custom_sign_up_urls do
        get :url_from, on: :collection
      end
      # HOMEWORKS

      resources :homeworks do
        get :pupil_index, on: :collection
        get :pupil_show, on: :member
        post :pupil_submit, on: :member
        post :duplicate, on: :member
      end
      resources :homework_tasks do
        get :show_for_marking, on: :member
      end
      resources :homework_task_submissions

      # HOMEWORKS END

      get '/presentations', to: 'presentations#index'
      get '/presentations/:id', to: 'presentations#show'
      get '/presentations/:id/copy', to: 'presentations#show_for_copy'
      get '/presentations/:id/with_reviews', to: 'presentations#presentation_with_reviews'
      post '/presentations/create', to: 'presentations#create'
      post '/presentations/:id/update', to: 'presentations#update'
      post '/presentations/:id/set_published_to', to: 'presentations#set_published_to'
      post '/presentations/:id/leave_review', to: 'presentations#leave_review'
    end
  end

  match '/', to: 'static#home', via: :get
  match '/video/:id', to: 'static#video', via: :get
  match '/video/:id', to: 'static#video_view', via: :post
  match '/childrens-code', to: 'static#childrens_code', via: :get
  match '/teacher-and-schools', to: 'static#schools', via: :get
  match '/careers-info', to: 'static#careers_info', via: :get
  match '/plans', to: 'static#plans_and_pricing', via: :get
  match '/product', to: 'static#product', via: :get
  match '/research', to: 'static#research', via: :get
  match '/reporting', to: 'static#reporting', via: :get
  match '/vacancies', to: 'static#vacancy_index', via: :get
  match '/vacancies/:id', to: 'static#vacancy_show', via: :get
  match '/about', to: 'static#about_us', via: :get
  match '/gdpr', to: 'static#gdpr', via: :get
  match '/enquiry', to: 'static#enquiry', via: :get
  match '/subscription_form_submission', to: 'static#subscription_form_submission', via: :post
  match '/cookies', to: 'static#cookie_policy', via: :get
  match '/legal', to: 'static#legal', via: :get
  match '/privacy', to: 'static#privacy_policy', via: :get
  match '/terms', to: 'static#terms_and_conditions', via: :get
  match '/footer_subscribe', to: 'static#footer_subscribe', via: :post
  match '/morgan-sindall', to: 'static#morgan_sindall', via: :get
  match '/affiliates', to: 'static#affiliates', via: :get
  match '/careers-ai', to: 'static#careers_ai', via: :get
  match '/lessons-ai', to: 'static#lessons_ai', via: :get
  match '/subjects/science', to: 'static#science_subject', via: :get
  match '/subjects/geography', to: 'static#geography_subject', via: :get
  match '/approach', to: 'static#approach', via: :get
  match '/send', to: 'static#send_page', via: :get

  match '/unit-library', to: 'static_library#curriculum_index', via: :get
  match '/unit-library/curriculum/:id', to: 'static_library#curriculum_show', via: :get, as: 'unit_library_curriculum'
  match '/unit-library/curriculum/:curriculum_id/years/:id', to: 'static_library#years_show', via: :get, as: 'unit_library_years'
  match '/unit-library/units/:id', to: 'static_library#units_show', via: :get, as: 'unit_library_units'

  get '/word-search/:template_id/(:lesson_id)', to: 'static_game#lesson_template_word_search', as: 'word_search'
  post '/word-search/:template_id', to: 'static_game#submit_lesson_template_word_search', as: 'word_search_submit'

  match '/static/logout', to: 'static#logout', via: :get

  namespace :admin do
    root to: '/static_admin/dashboard#index'

    resources :quip_quiz_results, controller: '/static_admin/quip_quiz_results', only: %i[index]

    get '/uk_schools/:id', to: '/static_admin/uk_schools#show'

    resources :live_streams, controller: '/static_admin/live_streams' do
      member do
        get :preview
        get :documents
        get :messages
        get :delete
        post :submit_reply
        post :pin_message
      end

      resources :live_stream_documents, controller: '/static_admin/live_stream_documents', only: %i[new edit create update destroy]
    end

    resources :campaigns, path: 'campaigns', controller: '/static_admin/campaigns', only: %i[index show edit update new create] do
      member do
        get :kpi
        post :cache_kpi
        get :impressions
        get 'impressions/lessons', action: :impressions_lessons
        get 'impressions/units', action: :impressions_units
        get 'impressions/schools', action: :impressions_schools
        get 'impressions/top_careers', action: :impressions_top_careers
        get :videos
        get :tours
        post :clear_impressions_cache
        post :clear_kpi_cache

        get :units
      end

      resources :campaign_units, controller: '/static_admin/campaign_units', only: %i[create update destroy]
    end

    resources :flows, controller: '/static_admin/flows', except: [:show] do
      member do
        post :publish
        get :steps
        get :enrollments
        get :documents  # Add this line
      end
      resources :flow_steps, path: 'steps', controller: '/static_admin/flow_steps', except: [:index, :show] do
        member do
          post :reorder
        end
      end
      resources :flow_documents, path: 'documents', controller: '/static_admin/flow_documents', except: [:index, :show] do
        collection do
          put :reorder
        end
      end

      get '/summary/:enrollment_id', on: :member, action: :summary, as: 'summary'
    end

    resources :lesson_templates, path: 'templates', controller: '/static_admin/lesson_templates', only: %i[index edit update destroy new create] do
      post :convert_from_quip, on: :member
      get :presentation, on: :member
      get :quiz, on: :member
      put :quiz, on: :member, action: :update_quiz
      get :documents, on: :member
      get :keywords, on: :member
      get :delete, on: :member
      put :relink, on: :member
      get :generate_with_ai, on: :member
      get :generate_keywords_with_ai, on: :member
      get :generate_keyword_content_from_name, on: :member
      get :feedback, on: :member
      get :proof, on: :member
      get :check_proof_generation_status, on: :member
      post :start_proof_generation, on: :member
      get :careers, on: :member
      get :add_career, on: :member

      post :add_career_path, on: :member
      post :remove_career_path, on: :member

      member do
        get :lesson_plan
        put :lesson_plan, action: :update_lesson_plan
      end 

      resources :slides, controller: '/static_admin/slides', only: %i[create update destroy] do
        collection do
          put :reorder 
          post :generate_quiz_question
          get :quip_question_details
        end
      end

      resources :keywords, controller: '/static_admin/lesson_keywords', only: %i[create update destroy] do
        collection do
          put :reorder 
        end
      end

      resources :documents, controller: '/static_admin/lesson_documents', only: %i[create update destroy]
    end

    resources :lesson_feedbacks, path: "lesson-feedback", only: %i[index], controller: '/static_admin/lesson_feedbacks'

    resources :subscription_dashboard, only: %i[index show], controller: '/static_admin/subscription_dashboard' do
      collection do
        get :subscribers
        get :analytics
        get :test_emails
        post :send_test_emails
        get :sync_status
        post :trigger_sync
      end
      member do
        post :void_invoice
        post :mark_invoice_as_paid
        get :tracking_events
      end
      member do
        post :send_reminder
      end
    end
    resources :stripe_events, only: %i[index show destroy], controller: '/static_admin/stripe_events' do
      member do
        post :retry
        get :manual_intervention_guide
      end
      collection do
        post :clear_old_events
        post :clear_resolved
        post :retry_all_critical
        get :report
      end
    end
    resources :discount_codes, controller: '/static_admin/discount_codes' do
      member do
        post :toggle_active
      end
    end

    resources :referrals, only: %i[index create new], controller: '/static_admin/referrals' do
      get :toggle_actioned, on: :member
      get :referrals_for, on: :member
    end

    resources :new_referrals, only: %i[index], controller: '/static_admin/new_referrals' do
      get :search_users, on: :collection
      get :toggle_paid_at, on: :member
    end

    resources :dashboard, only: [:index], controller: '/static_admin/dashboard' do
      collection do
        get :fetch_schools
        get :fetch_teachers
        get :fetch_pupils
      end
    end
    resources :events, path: 'events', controller: '/static_admin/events' do
      delete 'remove_file', to: '/static_admin/events#remove_file', as: 'remove_file', on: :member
      get 'toggle_publish', to: '/static_admin/events#toggle_publish', as: 'toggle_publish', on: :member
    end
    resources :tours, controller: '/static_admin/tours' do
      patch :update_scene, on: :member
      get 'build_sheet', on: :collection
      get :import, on: :collection
      post :import, on: :collection, action: :seed_tour
    end
    resources :error_logs, controller: '/static_admin/error_logs', only: %i[index] do
      get 'delete', on: :member
      get :delete_related, on: :member
    end
    resources :articles, controller: '/static_admin/articles' do 
      collection do
        get :new_ai_article
        post :generate_titles
        post :generate_article
        post :create_from_ai
        get "check_generation_status/:session_id", action: :check_generation_status
      end
      
    end
    resources :de_socials, path: 'de-socials', controller: '/static_admin/de_socials'
    resources :motds, controller: '/static_admin/motds'
    resources :landing_pages, controller: '/static_admin/landing_pages' do
      member do
        post :generate_content
        patch :publish
        patch :unpublish
        get :preview
        get :generation_status
      end
      collection do
        post :bulk_generate
        get :search_units
        get :search_lessons
      end
    end
    resources :marketing_links, only: [:new, :create], controller: '/static_admin/marketing_links'
    resources :glossaries, controller: '/static_admin/glossaries' do
      post 'regenerate-audio', to: '/static_admin/glossaries#regenerate_audio', as: 'regenerate_audio', on: :member
    end

    resources :organisations, controller: '/static_admin/organisations' do
      get :search, on: :collection
    end

    resources :enquiry_skills, path: 'enquiry-skills', controller: '/static_admin/enquiry_skills', as: 'scientific_enquiry_types'
    resources :custom_sign_up_urls, path: 'custom-sign-up-urls', controller: '/static_admin/custom_sign_up_urls'
    resources :sign_up_events, path: 'sign-up-events', controller: '/static_admin/sign_up_events'
    resources :team_members, path: 'team-members', controller: '/static_admin/team_members'
    resources :job_listings, path: 'job-listings', controller: '/static_admin/job_listings'
    resources :curriculum_definitions, path: 'curriculum-definitions', controller: '/static_admin/curriculum_definitions'
    resources :years, path: 'library-years', controller: '/static_admin/years' do
      resources :library_documents, path: 'documents', controller: '/static_admin/library_documents'
    end
    resources :units, path: 'library-units', controller: '/static_admin/units' do
      post 'duplicate', action: :duplicate, on: :member

      resources :library_documents, path: 'documents', controller: '/static_admin/library_documents'
    end
    resources :videos, controller: '/static_admin/videos' do
      resources :library_documents, path: 'documents', controller: '/static_admin/library_documents'
      get 'build_sheet', on: :collection
      get :search, on: :collection
      patch :reanalyze, on: :member
      patch :fetch_video_data, on: :member
      patch :copy_keywords_to_tags, on: :member
    end
    resources :teachers, only: [:index], controller: '/static_admin/teachers'
    resources :pupils, only: [:index], controller: '/static_admin/pupils'
    resources :forms, only: [:index], controller: '/static_admin/forms'
    resources :affiliates, controller: '/static_admin/affiliates' do
      get :get_schools, action: :get_schools, on: :collection
    end
    resources :dashboard_notifications, path: 'dashboard-notifications', controller: '/static_admin/dashboard_notifications'
    resources :import_schools, only: [:new, :create], path: 'import-schools', controller: '/static_admin/import_schools' do
      collection do
        get :map_columns
        post :validate_data
        get :confirm_import
        post :import
        get :summary
        get :download_template
      end
    end
    resources :static_schools, path: 'schools', controller: '/static_admin/schools' do
      get 'search', on: :collection
      get 'stripe_subscription', to: '/static_admin/schools#stripe_subscription', as: 'stripe_subscription', on: :member
      put 'subscription', to: '/static_admin/schools#update_subscription', as: 'subscription', on: :member
      delete 'subscription', to: '/static_admin/schools#delete_subscriber', on: :member, as: 'delete_subscriber'
      resources :teachers, except: %i[index edit update], controller: '/static_admin/teachers', as: 'teacher', on: :member do
        delete :destroy_all, on: :collection
        patch :reset_pw_all, on: :collection
        patch :reset_pw, on: :member
      end
      resources :forms, except: [:index], controller: '/static_admin/forms', as: 'form', on: :member
      resources :pupils, except: %i[edit update], controller: '/static_admin/pupils', as: 'pupil', on: :member
      post 'generate_sample_class', on: :member
      post 'sync_hubspot', on: :member
      post :send_welcome_emails, on: :member, as: 'send_welcome_emails'
      post :send_teacher_welcome_emails, on: :member, as: 'send_teacher_welcome_emails'
      post :admin_wipe_data, on: :member, as: 'admin_wipe_data'
      get :schools_for_merge, on: :member
      post :merge_with_school, on: :member
      post :manage_subscriber, on: :member

      get 'wonde-sync', to: '/static_admin/school_wonde#index', as: 'wonde_sync', on: :member
      post 'wonde-sync/create', to: '/static_admin/school_wonde#create', as: 'wonde_sync_create', on: :member
      get 'wonde-sync/:wonde_import_id/cancel', to: '/static_admin/school_wonde#cancel', as: 'wonde_sync_cancel', on: :member
      get 'wonde-sync/:wonde_import_id/download', to: '/static_admin/school_wonde#download', as: 'wonde_sync_download', on: :member
      get 'wonde-sync/:wonde_import_id/edit', to: '/static_admin/school_wonde#edit', as: 'wonde_sync_edit', on: :member
      put 'wonde-sync/:wonde_import_id/edit', to: '/static_admin/school_wonde#update_teacher', as: 'wonde_sync_update_teacher', on: :member
      get 'wonde-sync/:wonde_import_id/invalid_teachers', to: '/static_admin/school_wonde#invalid_teachers', as: 'wonde_sync_invalid_teachers', on: :member
      get 'wonde-sync/:wonde_import_id/review', to: '/static_admin/school_wonde#review', as: 'wonde_sync_review', on: :member
      get 'wonde-sync/:wonde_import_id/finalize', to: '/static_admin/school_wonde#finalize', as: 'wonde_sync_finalize', on: :member
      get 'wonde-sync/:wonde_import_id/save', to: '/static_admin/school_wonde#save', as: 'wonde_sync_save', on: :member
      get 'wonde-sync/:wonde_import_id/processing', to: '/static_admin/school_wonde#processing', as: 'wonde_sync_processing', on: :member

      get :details, on: :member
      get :legacy_subscription, on: :member
      get :hubspot, on: :member
      get :teachers, on: :member
      get :pupils, on: :member, path: 'school-pupils'
      get :classes, on: :member
      get :actions, on: :member
      get :school_invites, on: :member
    end

    resources :documents, controller: '/static_admin/documents' do
      delete 'remove_file', to: '/static_admin/documents#remove_file', as: 'remove_file', on: :member
    end
    resources :curricula, path: 'library-curricula', controller: '/static_admin/curricula'
    resources :subjects, path: 'library-subjects', controller: '/static_admin/subjects'
    resources :accreditations, controller: '/static_admin/accreditations' do
      post :update_weights, on: :collection
    end
    resources :lessons, path: 'lesson-templates', controller: '/static_admin/lessons' do
      get 'ai-import', to: '/static_admin/lessons#ai_lesson_importer', on: :collection
      get 'request_ai_lesson', to: '/static_admin/lessons#request_ai_lesson', on: :collection
      post 'save_ai_lesson', to: '/static_admin/lessons#import_ai_lesson', on: :collection
    end
    resources :careers, path: 'careers', controller: '/static_admin/careers' do
      resources :training_routes, path: 'training-routes', controller: '/static_admin/training_routes'
    end

    resources :career_builder, path: 'career-builder', only: %i[index edit update destroy], controller: '/static_admin/career_builder' do
      member do
        post :bulk_update_job_family
      end
    end

    resources :job_families, path: 'job-families', controller: '/static_admin/job_families' do
      # === Careers ===
      get    "careers",                          action: :careers,           on: :member
      
      # === Myths & Facts ===
      get    "myth-fact",                        action: :myth_fact,         on: :member
      post   :add_myth_fact,                                                 on: :member
      patch  "update_myth_fact/:index",          action: :update_myth_fact, on: :member, as: :update_myth_fact
      delete "remove_myth_fact/:index",          action: :remove_myth_fact, on: :member, as: :remove_myth_fact

      # === Roles ===
      get    "roles",                            action: :roles,             on: :member
      post   :add_role,                                                      on: :member
      patch  "update_role/:index",              action: :update_role,       on: :member, as: :update_role
      delete "remove_role/:index",              action: :remove_role,       on: :member, as: :remove_role

      # === Skills ===
      get    "skills",                           action: :skills,            on: :member
      post   :add_skill,                                                     on: :member
      patch  "update_skill/:index",             action: :update_skill,      on: :member, as: :update_skill
      delete "remove_skill/:index",             action: :remove_skill,      on: :member, as: :remove_skill

      # === Progression ===
      get    "progression",                      action: :progression,       on: :member
      post   :add_progression_step,                                         on: :member
      patch  "update_progression_step/:index",  action: :update_progression_step, on: :member, as: :update_progression_step
      delete "remove_progression_step/:index",  action: :remove_progression_step, on: :member, as: :remove_progression_step

      # === Industries ===
      get    "industries",                       action: :industries,        on: :member
      post   :add_industry,                                                 on: :member
      patch  "update_industry/:index",          action: :update_industry,   on: :member, as: :update_industry
      delete "remove_industry/:index",          action: :remove_industry,   on: :member, as: :remove_industry

      # === Workplaces ===
      get    "workplaces",                       action: :workplaces,        on: :member
      post   :add_workplace,                                                on: :member
      patch  "update_workplace/:index",         action: :update_workplace,  on: :member, as: :update_workplace
      delete "remove_workplace/:index",         action: :remove_workplace,  on: :member, as: :remove_workplace
    end

    resources :curriculum_documents, except: [:show], path: 'curriculum-documents', controller: '/static_admin/curriculum_documents'
    resources :curriculum_document_groups, path: 'curriculum-documents/groups', controller: '/static_admin/curriculum_document_groups'

    resources :wonde_imports, path: 'wonde-imports', controller: '/static_admin/wonde_imports' do
      get :todo, on: :collection
      get :active, on: :collection
      get :done, on: :collection
      get :problems, on: :collection
      post :start_import, on: :collection
    end
    resources :stripe_subscriptions, path: 'stripe-subscription', except: %i[show], controller: '/static_admin/stripe_subscriptions' do
      post :sync, on: :member
      post :resume, on: :member
      post :cancel, on: :member
      post :force_cancel, on: :member
    end
    resources :career_builder, path: 'career-builder', only: %i[index edit update destroy], controller: '/static_admin/career_builder' do
      get :logs, action: :logs, on: :member
      get :dump, action: :dump, on: :member
    end

    resources :presentation_feedbacks, path: 'presentation-feedbacks', only: %i[index], controller: '/static_admin/presentation_feedbacks' do
      post :new_comment, on: :member
    end
    resources :images, only: [:index, :show, :edit, :update], controller: '/static_admin/images' do
      member do
        patch :reanalyze
      end
    end
    resources :documents, only: [:index, :show, :edit, :update], controller: '/static_admin/documents' do
      member do
        patch :reanalyze
      end
    end
    resources :users, path: 'users', only: %i[index new create edit update], controller: '/static_admin/users' do
      get :stripe_subscription, on: :member
      get :legacy_subscription, on: :member
      get :password, on: :member
      get :devices, on: :member
      get :school, on: :member
      get :actions, on: :member
      post :connect_school, on: :member
      get :connect_school_info, on: :member
      post :send_password_reset_email, on: :member
      get :generate_password_reset_link, on: :member
      patch :toggle_restrict, on: :member
      delete :sign_out_all_devices, on: :member
      post :manage_subscriber, on: :member
      post :sync_mailchimp, on: :member
      get :mailchimp, on: :member
      delete 'subscription', to: '/static_admin/users#delete_subscriber', on: :member, as: 'delete_subscriber'
      get :permissions, on: :member
      patch :update_permissions, on: :member
    end
    
    resources :mailchimp, only: [:index], controller: '/static_admin/mailchimp' do
      collection do
        post :sync_all
        get :sync_status
        post :retry_failed
      end
      member do
        get :preview_mapping # for individual users
      end
    end

    resources :event_registrations, controller: '/static_admin/event_registrations' do
      member do
        get :registrants
        get :export_registrants
        patch :toggle_active
      end
      
      resources :event_registrants, only: [:index, :show, :destroy, :create], controller: '/static_admin/event_registrants' do
        member do
          post :resync_mailchimp
        end
      end
    end

    resources :flagged_images, path: 'flagged-images', only: %i[index new create destroy], controller: '/static_admin/flagged_images'
    resources :uk_schools_uploader, path: 'uk-schools-uploader', only: %i[index], controller: '/static_admin/uk_schools_uploader' do
      post :upload, on: :collection
      get 'import_progress/:job_id', on: :collection, action: :import_progress
    end

    resources :quip_quizzes, path: 'quip-quizzes', only: %i[index], controller: '/static_admin/quip_quizzes'

    resources :exemplar_works, path: 'exemplar-works', controller: '/static_admin/exemplar_works'

    get '*path', to: redirect('/admin')
  end

  namespace :school do

    resources :invites, controller: '/static_school/school_invites', path: 'invites' do
      member do
        patch :resend
      end
    end

    resources :lesson_editing, path: 'lesson-editing', controller: '/static_school/lesson_editing', only: %i[index new create edit destroy] do
      member do 
        get :show_details
        get :show_lesson_plan 
        get :show_presentation
        get :show_quiz
        get :show_keywords
        get :show_documents
        get :show_delete
        get :show_json_update
        get :preview_modifications
        post :update_from_json
        put :update_details
        put :update_lesson_plan
        post :convert_new_presentation_to_slides
        get :toggle_published
        post :modify_with_ai
        post :apply_ai_changes

        get :show_careers
        get :add_career
        post :add_career_path
        post :remove_career_path
      end
      post :ai_debug_update, on: :collection

      post :manage_folder, on: :collection
      delete :manage_folder, on: :collection, action: :delete_folder
      post :move_templates, on: :collection

      get :new_from_scratch, path: 'new-from-scratch', on: :collection

      get :new_from_templates, path: 'new-from-templates', on: :collection
      get :preview, on: :collection
      get :combine_status, on: :collection

      get :generate_with_ai, on: :collection

      post :accept_copy, on: :collection
      post :clear_folder, on: :collection

      resources :keywords, controller: '/static_school/lesson_keywords', only: %i[create update destroy] do
        collection do
          put :reorder 
        end
      end

      get '/independent_learning/:id/tasks', as: 'independent_learning_tasks', controller: '/static_school/independent_learning', action: :tasks

      resources :documents, controller: '/static_school/lesson_documents', only: %i[create update destroy]

      resources :slides, controller: '/static_school/slides', only: %i[create update destroy] do
        collection do
          put :reorder
          post :generate_quiz_question
          get :quip_question_details
        end
      end
    end

    resources :mark_book, path: 'mark-book', only: %i[index show], controller: '/static_school/mark_books' do
      post 'update_unit_mark', on: :member
      post 'update_unit_mark_labels', on: :member
      post 'update_lesson_mark', on: :member
      post 'update_pupil_notes', on: :member
      get 'build_sheet', on: :member
    end

    resources :teachers, only: %i[index new create edit update destroy], controller: '/static_school/teachers' do
      get :classes, on: :member
      get :actions, on: :member
      get :password, on: :member
      post :new_password, on: :member
      post :send_password_reset_email, on: :member
      get :generate_password_reset_link, on: :member
    end

    resources :forms, path: 'classes', only: %i[index new create edit update destroy], controller: '/static_school/forms' do
      get :pupils, on: :member
      get :lessons, on: :member
      patch :remove_lessons, on: :member
      patch :reschedule_lesson, on: :member
      get :print_letters, on: :member
      patch :add_pupil, on: :member
      patch :remove_pupil, on: :member
      get :pupil_search, on: :collection
      patch :add_teacher, on: :member
      patch :remove_teacher, on: :member
    end

    resources :curriculum_documents, path: 'curriculum-documents', only: %i[index], controller: '/static_school/curriculum_documents'

    resources :pupils, only: %i[index new create edit update destroy], controller: '/static_school/pupils' do
      get :download_login_codes, on: :collection, defaults: { format: :csv }
      get :classes, on: :member
      get :letter, on: :member
      get :print_letter, on: :member
    end

    resources :data_imports, path: 'data-imports', controller: '/static_school/data_imports' do
      collection do
        resources :xls, path: 'xls', controller: '/static_school/xls_data_imports', only: %i[index show new edit create update destroy]
      end
    end

    resources :exemplar_works, path: 'exemplar-works', controller: '/static_school/exemplar_works'

    resources :settings, controller: '/static_school/settings'
    get '/user_roles', to: '/static_school/settings#user_roles', as: :school_user_roles
    patch '/user_roles', to: '/static_school/settings#update_user_roles'
    get '/platform_configuration', to: '/static_school/settings#platform_configuration', as: :school_platform_configuration
    patch '/platform_configuration', to: '/static_school/settings#update_platform_configuration'

    get '/add-lessons', controller: '/static_school/add_lessons', action: :add_to_form
    post '/add-lessons', controller: '/static_school/add_lessons', action: :add_to_form_submit

    get '/dashboard', controller: '/static_school/dashboard', action: :index, as: 'static_dashboard'
    put '/dashboard/update_finance_contact', controller: '/static_school/dashboard', action: :update_finance_contact, as: :update_finance_contact
    put '/dashboard/update_science_leaders', controller: '/static_school/dashboard', action: :update_science_leaders, as: :update_science_leaders
    put '/dashboard/update_geography_leaders', controller: '/static_school/dashboard', action: :update_geography_leaders, as: :update_geography_leaders

    get '/updates', controller: '/static_school/motds', action: :index, as: 'motds'
    resources :live_lessons, path: 'youtube', only: %i[index], controller: '/static_school/live_lessons' do
      collection do
        get :live
        get :upcoming
        get :history
        get '/history/:id', action: :history_show, as: 'history_show'
        get '/live_stream_messages/:stream_id', action: :live_stream_messages, as: 'live_stream_messages'
        post '/live_stream_messages/:live_stream_id/new', action: :new_live_stream_message, as: 'new_live_stream_message'
      end
    end

    resources :weekly_digest, path: 'weekly-digest', only: %i[index], controller: '/static_school/weekly_digest' do
      get 'school-activity', action: :school_activity, on: :collection
      
      get 'send-digest', action: :send_digest, on: :collection
    end
  end

  namespace :pupil do
    get '/', controller: '/static_pupil/dashboard', action: :index
    get '/set_pupil', controller: '/static_pupil/dashboard', action: :set_pupil
    get '/return_to_dashboard', controller: '/static_pupil/dashboard', action: :return_to_dashboard

    get '/lessons', controller: '/static_pupil/lessons', action: :lesson_index, as: 'lessons_index'
    get '/lessons/:lesson_id', controller: '/static_pupil/lessons', action: :lesson_show, as: 'lesson_show'

    get '/lessons/:lesson_id/word-search-quiz', controller: '/static_pupil/lessons', action: :word_search, as: :lesson_show_word_search
    get '/lessons/:lesson_id/rocket-words-quiz', controller: '/static_pupil/lessons', action: :rocket_word_quiz, as: :lesson_show_rocket_word_quiz
    get '/lessons/:lesson_id/assessment-quiz', controller: '/static_pupil/lessons', action: :assessment_quiz, as: :lesson_show_assessment_quiz
    get '/lessons/:lesson_id/lesson-plan', controller: '/static_pupil/lessons', action: :lesson_plan, as: :lesson_show_lesson_plan

    get '/games', controller: '/static_pupil/games', action: :index
    get '/games/blast-words', controller: '/static_pupil/games', action: :blast_words
    get '/games/blast-words/:lesson_id/play', controller: '/static_pupil/games', action: :blast_words_play
    get '/games/fetch_word_searches', controller: '/static_pupil/games', action: :fetch_word_searches

    match '/games/blast-words/:lobby_id', to: '/static_pupil/games#blast_words_show', via: :get
    match '/games/blast-words/:lobby_id/leaderboard', to: '/static_pupil/games#blast_words_leaderboard', via: :get
    match '/games/blast-words/:lobby_id/join', to: '/static_pupil/games#join_lobby', via: :get
    match '/games/blast-words/:lobby_id/leave', to: '/static_pupil/games#leave_lobby', via: :get
    match '/games/blast-words/:lobby_id/increment_score', to: '/static_pupil/games#increment_score', via: :post
    match '/games/blast-words/:lobby_id/dev_start_blast_words', to: '/static_pupil/games#dev_start_blast_words', via: :get

    get '/careers', controller: '/static_pupil/careers', action: :index
    get '/careers/tours', controller: '/static_pupil/careers', action: :tours_index

    get '/leaderboard', controller: '/static_pupil/leaderboard', action: :index
    get '/leaderboard/points', controller: '/static_pupil/leaderboard', action: :points

    get '/independent-learning', controller: '/static_pupil/independent_learning', action: :index
    get '/independent-learning/:id', controller: '/static_pupil/independent_learning', action: :show
    post '/independent-learning/:id', controller: '/static_pupil/independent_learning', action: :pupil_submit
    get '/independent-learning/quiz/:id', controller: '/static_pupil/independent_learning', action: :quiz_show
    get '/independent-learning/:id/task/:task_id', controller: '/static_pupil/independent_learning', action: :task_show
    post '/independent-learning/:id/task/:task_id', controller: '/static_pupil/independent_learning', action: :task_submit

    get '/account', controller: '/static_pupil/account', action: :index
    patch '/account', controller: '/static_pupil/account', action: :update
    get '/account/presentation-settings', controller: '/static_pupil/account', action: :presentation_settings
    patch '/account/update_presentation_settings', controller: '/static_pupil/account', action: :update_presentation_settings
    get '/account/progress', controller: '/static_pupil/account', action: :progress
    get '/account/history', controller: '/static_pupil/account', action: :history
  end

  post '/videos/convert_url', controller: 'static/videos', action: :convert_url, as: :convert_video_url
  get '/videos/download/:id', controller: 'static/videos', action: :download_video, as: 'download_video'
  post '/videos/from_data', controller: 'static/videos', action: :video_from_data
  get '/videos/search', controller: 'static/videos', action: :search, as: :search_videos

  get '/user/account', to: 'static_user/accounts#current_account_redirect', as: 'current_user_account'
  namespace :user do
    resources :referrals, controller: '/static_user/user_referrals', path: 'referrals' do
      member do
        patch :resend
      end
      collection do
        post :update_referral_code
      end
    end
    resources :accounts, path: 'account', controller: '/static_user/accounts', only: %i[show update] do
      post :close_account, on: :collection
      get 'feature_preview', on: :member
      patch 'toggle_beta_feature', on: :member
      # Presentation settings page
      get 'presentation_settings', on: :member
      patch 'update_presentation_settings', on: :member
      # Communication Preferences page
      get 'communication_preferences', on: :member
      patch 'toggle_communication_preference', on: :member
    end

    resources :stripe_subscriptions, path: 'subscription', only: %i[], controller: '/static_user/stripe_subscriptions' do
      get :subscriptions, on: :collection, as: :subscriptions
      get :cancel, on: :member
    end
  end

  post '/sessions/hubspot_visitor_verification', to: 'static_session#hubspot_visitor_verification', as: :sessions_hubspot_visitor_verification
  post '/sessions/swap_accounts', to: 'static_session#swap_accounts'
  post '/sessions/:id/get_sign_in_token', to: 'static_session#get_sign_in_token'

  get '/school/subscription', to: 'static_school/pages#subscription'
  get '/subscribe', to: 'static/subscription_requests#subscribe_hs'

  resources :articles, only: %i[index show], controller: 'static/articles'
  resources :images, only: [:create], controller: 'static/images' do 
    collection do
      get :search

      post :upload_fileboy_image
      post :process_pexels_image
    end
  end

  resources :flows, path: "courses", only: [:index, :show], controller: 'static/flows' do
    member do
      get :summary
      post :enroll
      delete :unenroll
    end
    resources :flow_steps, path: "steps", only: [:show], controller: 'static/flow_steps' do
      member do
        post :complete
        post :update_notes
      end
    end
  end

  resources :job_families, path: "careers/job-families", only: %i[index show], controller: 'static/job_families'
  # Career Routes #
  get '/careers', to: 'static/careers#index', as: 'careers_index'
  get '/careers/generate', to: 'static/career_builder#index_v2', as: 'careers_generate'
  get '/careers/search', to: 'static/careers#search', as: 'careers_search'
  get '/careers/favourites', to: 'static/careers#favourites', as: 'careers_favourites'
  get '/careers/search/:id', to: 'static/career_builder#show_v2', as: 'careers_search_show'
  get '/careers/interactive', to: 'static/career_builder#interactive', as: 'careers_interactive'


  post '/files/upload_fileboy_file', to: 'static/files#upload_fileboy_file'

  resources :events, controller: 'static/events', only: %i[index show]

  match :glossary_search_block, to: 'htmx_components#glossary_search_block', via: :get
  match :footer, to: 'htmx_components#footer', via: :get
  match :school_sidebar, to: 'htmx_components#school_sidebar', via: :get
  match :session_header, to: 'htmx_components#session_header', via: :get
  match :dev_toolbar, to: 'htmx_components#dev_toolbar', via: :get
  match :de_social_panel, to: 'htmx_components#de_social_panel', via: :get

  resources 'glossary', only: %i[show index], controller: 'static/glossaries' do
    get 'letter/:letter', action: :letter, on: :collection, as: 'letter'
    get 'all', action: :get_all_entries, on: :collection
    post :toggle_favourite, on: :member
  end

  resources :documents, only: %i[index], controller: 'static/documents'
  resources :videos, only: %i[show index], controller: 'static/videos'

  resources 'career_builder', path: 'career-builder', only: %i[index show], controller: 'static/career_builder' do
    collection do
      get :lead
      get :suggestions
      get :saved

      post :generate_lead_careers
      post :regenerate_lead_careers
      post :generate_career_path
      post :generate_facts_for_career
      get :career_path_status
      
      # NEW V2 ENDPOINTS
      get :index_v2
      post :generate_career_path_v2
      post :generate_facts_for_career_v2
      get :career_path_status_v2
      post :generate_lead_careers_v2
      get :suggestions_v2
    end

    member do
      get :logs
      get :progressions
      get :qualifications
      get :apprenticeships
      
      # NEW V2 ENDPOINTS
      get :show_v2

      post :generate_audio_for_section
      post :remove_career
      post :toggle_favourite_career_path
      
      # NEW V2 ENDPOINTS
      post :regenerate_career_path_v2
    end
  end

  get '/quiz-builder/new', controller: :static_quiz_builder, action: :new, as: :new_quiz
  get '/quiz-builder/:id/edit', controller: :static_quiz_builder, action: :edit, as: :edit_quiz
  get '/quiz-builder/:id', controller: :static_quiz_builder, action: :show, as: :show_quiz
  post '/quiz-builder', controller: :static_quiz_builder, action: :create, as: :create_quiz
  put '/quiz-builder/:id', controller: :static_quiz_builder, action: :update, as: :update_quiz
  post '/quiz-builder/upload-file', controller: :static_quiz_builder, action: :upload_file, as: :quiz_upload_file
  post '/quiz-builder/build_placeholder_quiz', controller: :static_quiz_builder, action: :build_placeholder_quiz

  get '/missions/:id/word-search', controller: 'static/lesson_templates', action: :word_search, as: :static_missions_word_search
  get '/missions/:id/assessment-quiz', controller: 'static/lesson_templates', action: :assessment_quiz, as: :static_missions_assessment_quiz
  get '/missions/:id/rocket-words-quiz', controller: 'static/lesson_templates', action: :rocket_word_quiz, as: :static_missions_rocket_word_quiz
  get '/missions/:id/lesson-plan', controller: 'static/lesson_templates', action: :lesson_plan, as: :static_missions_plan
  get '/missions/:id', controller: 'static/lesson_templates', action: :show, as: :static_missions
  get '/missions/:id/data', controller: 'static/lesson_templates', action: :json, as: :static_missions_json
  post '/missions/:id/ai-query', controller: 'static/lesson_templates', action: :ai_query, as: :static_missions_ai_query

  get '/event-registrations/:id/register', to: 'static/event_registrations#show', as: :event_registration
  post '/event-registrations/:id/register', to: 'static/event_registrations#create'

  get '/lessons/:id', controller: 'static/lessons', action: :show, as: :school_lesson

  get '/sponsorships', to: 'static/sponsors#index', as: 'sponsors'
  resources :sponsors, only: [:show], controller: 'static/sponsors'

  get '/deai/videos', to: 'lesson_ai#videos'
  get '/deai/videos/:id', to: 'lesson_ai#video_show'
  get '/deai/careers', to: 'lesson_ai#careers'
  get '/deai/careers/:id', to: 'lesson_ai#career_show'
  get '/deai/documents', to: 'lesson_ai#documents'
  get '/deai/documents/:id', to: 'lesson_ai#document_show'
  get '/deai/curriculum_definitions', to: 'lesson_ai#curriculum_definitions'
  get '/deai/user', to: 'lesson_ai#user'
  get '/deai/flagged_images', to: 'lesson_ai#flagged_images'
  post '/deai/flag_image', to: 'lesson_ai#flag_image'
  get '/deai/author-user/:id', to: 'lesson_ai#author_user'

  post '/flag_media', to: 'flag_media#flag_media'

  get '/alpha/presentation/ai/:id', to: 'static/presentation#ai_show', as: 'static_presentation_ai'

  get 'presentation/slide/:slide_id', to: 'static/presentation#show_single_slide', as: 'presentation_show_single_slide'

  get '/presentation/static/:id', to: 'static/presentation#template_slides_presentation', as: 'static_presentation_2'
  get '/presentation/new/:id', to: 'static/presentation#new_presentation', as: 'static_presentation_3'
  get '/presentation/:id/(:lesson_id)', to: 'static/presentation#show', as: 'static_presentation'

  post '/presentation/quiz_slide', to: 'static/presentation#quiz_slide', as: 'static_presentation_quiz_slide'
  post '/presentations/feedback', to: 'static/new_presentation_feedback#create', as: 'static_new_presentation_feedback'

  post '/audio/for_text', to: 'static/audio#for_text', as: 'static_audio_for_text'

  get '/accounts/password/new', to: 'static/session_passwords#new'
  get '/accounts/:organisation_id/password/new', to: 'static/session_passwords#new'

  post '/accounts/password/new', to: 'static/session_passwords#create'
  post '/accounts/:organisation_id/password/new', to: 'static/session_passwords#create'

  get '/accounts/password/edit', to: 'static/session_passwords#edit'
  get '/accounts/:organisation_id/password/edit', to: 'static/session_passwords#edit'

  post '/accounts/password/edit', to: 'static/session_passwords#update'
  post '/accounts/:organisation_id/password/edit', to: 'static/session_passwords#update'

  get '/uk_schools/search', to: 'static/uk_schools#search', as: 'uk_schools_search'

  #######################
  # SIGN IN / UP ROUTES #
  #######################
  get '/accounts/sign-in', to: 'static/session_accounts#accounts_sign_in', as: 'accounts_sign_in'
  get '/accounts/:oid/sign-in', to: 'static/session_accounts#accounts_sign_in'
  post '/accounts/sign-in', to: 'static/session_accounts#accounts_sign_in_submit', as: 'accounts_sign_in_submit'

  get '/accounts/new/user/(:oid)', to: 'static/session_accounts#user', as: 'accounts_new_user'
  post '/accounts/new/user/(:oid)', to: 'static/session_accounts#user_submit', as: 'accounts_new_user_submit'

  get '/accounts/new/school/(:oid)', to: 'static/session_accounts#school', as: 'accounts_new_school'
  post '/accounts/new/school/(:oid)', to: 'static/session_accounts#school_validate', as: 'accounts_new_school_validate'

  get '/accounts/new/school_details/(:oid)', to: 'static/session_accounts#school_details', as: 'accounts_new_school_details'
  post '/accounts/new/school_details/(:oid)', to: 'static/session_accounts#create', as: 'accounts_new_school_create'
  post '/accounts/new/request_school_invite/(:oid)', to: 'static/session_accounts#request_school_invite', as: 'accounts_new_request_school_invite'

  get '/accounts/new/home/<USER>', to: 'static/session_accounts#home', as: 'accounts_new_home'

  get '/accounts/new/oauth/:resource', to: 'static/session_accounts#oauth_callback'

  get '/accounts/new/international/(:oid)', to: 'static/session_accounts#international', as: 'accounts_new_international'

  get '/accounts/new', to: 'static/session_accounts#new', as: 'accounts_new'
  get '/accounts/new/:oid', to: 'static/session_accounts#new_accounts', as: 'accounts_new_organisation'
  get '/accounts/:oid/sign-up', to: 'static/session_accounts#new_accounts'

  get '/sign-up/(:part_1)/(:part_2)/(:part_3)/(:part_4)/(:part_5)', to: 'static/session_accounts#custom_signup_url', as: 'custom_sign_up'

  #######################

  get '/quiz/quiz_details', to: 'static/quip_quiz#quiz_details', as: :quiz_details

  post '/quiz/rocket-words/:template_id/(:lesson_id)', to: 'static/quip_quiz#rocket_word_quiz_submit', as: 'rocket_word_quizzes'
  post '/quiz/template/:template_id/(:lesson_id)', to: 'static/quip_quiz#quiz_by_template_submit', as: 'template_quizzes'
  post '/quiz/flow_step/:flow_step_id', to: 'static/quip_quiz#quiz_by_flow_step_submit', as: 'flow_step_quizzes'

  post '/quiz/id/:id', to: 'static/quip_quiz#quiz_by_id_submit', as: 'quip_quiz_by_id'
  get '/quiz/results/:id', to: 'static/quip_quiz#result', as: 'quip_quiz_results'

  get '/document/:fileboy_id', controller: 'static/documents', action: :serve_document, as: 'serve_document'

  get '/track/:marketing_identifier/:link_destination', to: 'static#marketing_link', via: :get, as: 'marketing_tracking_link'

  get '/resources/:id', to: 'static/landing_pages#show', as: 'landing_page'
  get '/resources', to: 'static/landing_pages#index', as: 'landing_pages_index'

  get '/accept/school-invite/:token', to: 'static/invitation_acceptances#accept_school_invite', as: 'accept_school_invite'
  get '/accept/referral/:token', to: 'static/invitation_acceptances#accept_user_referral', as: 'accept_user_referral'

  # config/routes.rb
  get '/invitations/school-signup', to: 'static/invitation_signup#school_invite_signup', as: 'invitation_school_signup'
  post '/invitations/school-signup', to: 'static/invitation_signup#create_school_invite_user'
  get '/invitations/user-type-selection', to: 'static/invitation_signup#user_type_selection', as: 'invitation_user_type_selection'

  if Rails.env.development?
    get 'email_preview', to: 'email_preview#index', as: 'email_preview_index'
    get 'email_preview/:method', to: 'email_preview#show', as: 'email_preview_show'
    get 'email_preview/:class_name/:method', to: 'email_preview#show'
    get 'rails_email_preview/:method', to: redirect { |path_params, _req|
      "/rails/mailers/subscription_mailer/#{path_params[:method]}"
    }, as: 'rails_email_preview'
  end

  get '/sitemap.xml' => 'sitemaps#show', format: 'xml'

  match '*path', to: 'static_index_html#get', via: :get

  root 'static_index_html#get'
end
