# == Schema Information
#
# Table name: lesson_template_career_paths
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  career_path_id     :bigint           not null
#  lesson_template_id :bigint           not null
#
# Indexes
#
#  index_lesson_template_career_paths_on_career_path_id      (career_path_id)
#  index_lesson_template_career_paths_on_lesson_template_id  (lesson_template_id)
#
# Foreign Keys
#
#  fk_rails_...  (career_path_id => career_paths.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#
require 'rails_helper'

RSpec.describe LessonTemplateCareerPath, type: :model do
  pending "add some examples to (or delete) #{__FILE__}"
end
