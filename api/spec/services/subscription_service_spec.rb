# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SubscriptionService, type: :service do
  include VCRHelpers
  include StripeVcrHelper

  # Configure Stripe test environment
  before(:all) do
    # Ensure test environment configuration
    Rails.configuration.stripe = {
      publishable_key: 'pk_test_dummy',
      secret_key: 'sk_test_dummy',
      endpoint_secret: 'whsec_dummy',
      environment: :test,
      portal_configuration_id: 'pc_test123'
    }
  end

  before do
    create(:stripe_product_price, product: 'science', size: 'individual', test_price_id: 'price_1QwmQJLeOZ19GUJIwzThcNH4')
    create(:stripe_product_price, product: 'science', size: 'small', test_price_id: 'price_1QwmQKLeOZ19GUJIDixhIzy2')
    create(:stripe_product_price, product: 'geography', size: 'small', test_price_id: 'price_1QwmQLLeOZ19GUJIV29QMNFS')
  end

  # Helper method to create a test user with a subscriber
  def create_test_user_with_subscriber
    user = create(:user)
    subscriber = create(:subscriber, :for_user,
                        subscriber: user,
                        stripe_customer_id: "cus_#{SecureRandom.hex(10)}",
                        email: user.email)
    [user, subscriber]
  end

  describe '#create_subscription', :vcr do
    # It is important the IDS, name, email, customer id, etc are consistent otherwise the stripe cassette will not match the request
    # and the test will fail
    # Create the subscriber first, then add it to the user to ensure the USER has the subscriber, as the service looks at the USER data
    let(:subscriber) { create(:subscriber, :for_user, id: 147, stripe_customer_id: 'cus_SAaGADho8vJiPI') }
    let(:user) { create(:user, email: '<EMAIL>', name: "Vesta O'Kon", id: 147, subscriber: subscriber, referral_code: 'ref-code-1') }
    let(:service) { described_class.new(user) }

    context 'with individual subscription' do
      it 'creates a subscription successfully' do
        # Set up fake Stripe IDs that will be consistent between test runs
        allow(SecureRandom).to receive(:hex).and_return('abc123')
        allow(SecureRandom).to receive(:uuid).and_return('test-uuid-12345')
        # Do not regen the record as it will fail to match if the test data changed
        use_stripe_cassette('subscription/create_individual', record: :once) do
          # Prepare test data
          product_keys = [:science]
          size_key = :individual
          return_url = 'http://example.com/success' # Provide a dummy return URL

          # Create the subscription with a return_url
          result = service.create_subscription(product_keys, size_key, return_url)

          # Assertions
          expect(result).to be_a(Hash)
          expect(result[:checkout_session]).to be_present
          expect(result[:checkout_url]).to be_present
        end
      end
    end

    context 'with school subscription' do
      it 'creates an invoice-based subscription successfully' do
        # record once so it stops regnerating the cassette and then failing because test ids changed on stripe
        use_stripe_cassette('subscription/create_school', record: :once) do
          # Prepare test data
          product_keys = %i[science geography]
          size_key = :small

          # Create the subscription
          result = service.create_subscription(product_keys, size_key)

          # Assertions
          expect(result).to be_a(Hash)
          expect(result[:subscription]).to be_present
          expect(result[:invoice]).to be_present
        end
      end
    end
  end

  describe '#update_po_number' do
    let(:user) { create(:user, :with_subscriber) }
    let(:service) { described_class.new(user) }
    let(:stripe_customer) { double('Stripe::Customer', invoice_settings: OpenStruct.new(custom_fields: existing_fields)) }

    before do
      user.subscriber.update(stripe_customer_id: 'cus_123')
      allow(Stripe::Customer).to receive(:retrieve).with('cus_123').and_return(stripe_customer)
      allow(Stripe::Customer).to receive(:update)
    end

    context 'when updating existing PO number' do
      let(:existing_fields) { [{ name: 'PO Number', value: 'OLD123' }] }

      it 'updates the PO number field' do
        service.update_po_number('NEW456')

        expect(Stripe::Customer).to have_received(:update).with('cus_123', {
                                                                  invoice_settings: {
                                                                    custom_fields: [{ name: 'PO Number', value: 'NEW456' }]
                                                                  }
                                                                })
      end
    end

    context 'when removing existing PO number' do
      let(:existing_fields) { [{ name: 'PO Number', value: 'OLD123' }] }

      it 'removes the PO number field when value is blank' do
        service.update_po_number(nil)

        expect(Stripe::Customer).to have_received(:update).with('cus_123', {
                                                                  invoice_settings: {
                                                                    custom_fields: ''
                                                                  }
                                                                })
      end
    end

    context 'when adding new PO number field' do
      let(:existing_fields) { [] }

      it 'adds a new custom field' do
        service.update_po_number('NEW789')

        expect(Stripe::Customer).to have_received(:update).with('cus_123', {
                                                                  invoice_settings: {
                                                                    custom_fields: [{ name: 'PO Number', value: 'NEW789' }]
                                                                  }
                                                                })
      end
    end

    context 'when Stripe raises an error' do
      let(:existing_fields) { [] }

      before do
        allow(Stripe::Customer).to receive(:update).and_raise(Stripe::StripeError.new('boom'))
      end

      it 'logs and raises an error' do
        expect(Rails.logger).to receive(:error).at_least(:once)
        expect { service.update_po_number('FAIL') }.to raise_error(StandardError, /Error updating PO number/)
      end
    end

    context 'when subscriber has no stripe_customer_id' do
      let(:existing_fields) { [] }

      before do
        user.subscriber.update(stripe_customer_id: nil)
      end

      it 'does nothing' do
        expect(Stripe::Customer).not_to receive(:retrieve)
        expect(service.update_po_number('ANY')).to be_nil
      end
    end
  end

  describe '#get_current_plan_key_from_details' do
    let(:user) { create(:user, :with_subscriber) }
    let(:service) { described_class.new(user) }

    def build_subscription(nickname)
      price = double('Stripe::Price', nickname: nickname)
      item = double('Stripe::SubscriptionItem', price: price)
      items = double(data: [item])
      double('Stripe::Subscription', id: 'sub_123', items: items)
    end

    context 'when nickname includes (Monthly)' do
      it 'returns :individual_monthly' do
        subscription = build_subscription('Science - Individual (Monthly)')
        expect(service.get_current_plan_key_from_details(subscription)).to eq(:individual_monthly)
      end
    end

    context 'when nickname matches known size names' do
      it 'returns the correct plan key for all annual plans' do
        StripeProducts::BASE_PRICES.except(:individual_monthly).each_key do |plan_key|
          size_key = StripeProducts.get_base_size_from_plan_key(plan_key)
          size_name = StripeProducts::SIZES[size_key][:name]
          nickname = "AI - #{size_name}"

          subscription = build_subscription(nickname)
          expect(service.get_current_plan_key_from_details(subscription)).to eq(plan_key), "Expected #{plan_key} from nickname '#{nickname}'"
        end
      end
    end

    context 'when nickname does not match any known sizes' do
      it 'returns nil and logs a warning' do
        subscription = build_subscription('Unknown Product - Unknown Size')
        expect(Rails.logger).to receive(:warn).with(/Could not reliably determine plan_key/)
        expect(service.get_current_plan_key_from_details(subscription)).to be_nil
      end
    end

    context 'when subscription has no items' do
      it 'returns nil' do
        items = double(data: [])
        subscription = double('Stripe::Subscription', id: 'sub_000', items: items)
        expect(service.get_current_plan_key_from_details(subscription)).to be_nil
      end
    end

    context 'when price is missing from the first item' do
      it 'returns nil' do
        item = double('Stripe::SubscriptionItem', price: nil)
        items = double(data: [item])
        subscription = double('Stripe::Subscription', id: 'sub_999', items: items)
        expect(service.get_current_plan_key_from_details(subscription)).to be_nil
      end
    end
  end

  describe '#calculate_collective_discount' do
    let(:user) { create(:user, :with_subscriber) }
    let(:service) { described_class.new(user) }

    let(:price_1) { double('Stripe::Price', unit_amount: 10000) } # £100
    let(:price_2) { double('Stripe::Price', unit_amount: 5000) }  # £50
    let(:price_ids) { ['price_1', 'price_2'] }

    let(:percent_coupon) { double('Stripe::Coupon', percent_off: 20.0, amount_off: nil) } # 20% off
    let(:amount_coupon) { double('Stripe::Coupon', percent_off: nil, amount_off: 1000) }  # £10 off
    let(:discounts) { [{ coupon: 'coupon_1' }, { coupon: 'coupon_2' }] }

    before do
      allow(Stripe::Price).to receive(:retrieve).with('price_1').and_return(price_1)
      allow(Stripe::Price).to receive(:retrieve).with('price_2').and_return(price_2)
      allow(Stripe::Coupon).to receive(:retrieve).with('coupon_1').and_return(percent_coupon)
      allow(Stripe::Coupon).to receive(:retrieve).with('coupon_2').and_return(amount_coupon)
    end

    context 'with percentage and amount discounts' do
      it 'calculates combined discount correctly' do
        # Mock the combined coupon creation
        allow(service).to receive(:create_combined_discount_coupon).with(4000).and_return('combined_coupon_123')

        result = service.calculate_collective_discount(discounts, price_ids)

        # Total: £150, 20% off = £120, then £10 off = £110
        # Total discount: £150 - £110 = £40 = 4000 cents
        expect(service).to have_received(:create_combined_discount_coupon).with(4000)
        expect(result).to eq([{ coupon: 'combined_coupon_123' }])
      end
    end

    context 'with only percentage discounts' do
      let(:percent_coupon_2) { double('Stripe::Coupon', percent_off: 10.0, amount_off: nil) }

      before do
        allow(Stripe::Coupon).to receive(:retrieve).with('coupon_2').and_return(percent_coupon_2)
      end

      it 'applies discounts sequentially' do
        allow(service).to receive(:create_combined_discount_coupon).with(4200).and_return('combined_coupon_456')

        result = service.calculate_collective_discount(discounts, price_ids)

        # Total: £150, 20% off = £120, then 10% off = £108
        # Total discount: £150 - £108 = £42 = 4200 cents
        expect(service).to have_received(:create_combined_discount_coupon).with(4200)
        expect(result).to eq([{ coupon: 'combined_coupon_456' }])
      end
    end

    context 'with only amount discounts' do
      let(:amount_coupon_1) { double('Stripe::Coupon', percent_off: nil, amount_off: 2000) } # £20 off
      let(:amount_coupon_2) { double('Stripe::Coupon', percent_off: nil, amount_off: 1000) } # £10 off

      before do
        allow(Stripe::Coupon).to receive(:retrieve).with('coupon_1').and_return(amount_coupon_1)
        allow(Stripe::Coupon).to receive(:retrieve).with('coupon_2').and_return(amount_coupon_2)
      end

      it 'sums amount discounts correctly' do
        allow(service).to receive(:create_combined_discount_coupon).with(3000).and_return('combined_coupon_789')

        result = service.calculate_collective_discount(discounts, price_ids)

        # Total: £150, £20 off = £130, then £10 off = £120
        # Total discount: £150 - £120 = £30 = 3000 cents
        expect(service).to have_received(:create_combined_discount_coupon).with(3000)
        expect(result).to eq([{ coupon: 'combined_coupon_789' }])
      end
    end

    context 'when discounts exceed total price' do
      let(:large_amount_coupon) { double('Stripe::Coupon', percent_off: nil, amount_off: 20000) } # £200 off

      before do
        allow(Stripe::Coupon).to receive(:retrieve).with('coupon_1').and_return(large_amount_coupon)
        allow(Stripe::Coupon).to receive(:retrieve).with('coupon_2').and_return(amount_coupon)
      end

      it 'caps discount at total price' do
        allow(service).to receive(:create_combined_discount_coupon).with(15000).and_return('combined_coupon_max')

        result = service.calculate_collective_discount(discounts, price_ids)

        # Total: £150, £200 off would be negative, so capped at £0
        # Total discount: £150 - £0 = £150 = 15000 cents
        expect(service).to have_received(:create_combined_discount_coupon).with(15000)
        expect(result).to eq([{ coupon: 'combined_coupon_max' }])
      end
    end

    context 'when Stripe raises an error' do
      before do
        allow(Stripe::Price).to receive(:retrieve).and_raise(Stripe::StripeError.new('API error'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).at_least(:once)
        expect { service.calculate_collective_discount(discounts, price_ids) }.to raise_error(Stripe::StripeError)
      end
    end
  end

  describe '#create_combined_discount_coupon' do
    let(:user) { create(:user, :with_subscriber) }
    let(:service) { described_class.new(user) }
    let(:total_discount_amount) { 2500 } # £25

    before do
      user.subscriber.update(id: 123)
      allow(SecureRandom).to receive(:hex).with(4).and_return('ABCD')
    end

    context 'when successful' do
      let(:created_coupon) { double('Stripe::Coupon', id: 'COMBINED_123_ABCD') }

      before do
        allow(Stripe::Coupon).to receive(:create).and_return(created_coupon)
      end

      it 'creates a combined discount coupon with correct parameters' do
        result = service.create_combined_discount_coupon(total_discount_amount)

        expect(Stripe::Coupon).to have_received(:create).with({
          id: 'COMBINED_123_ABCD',
          duration: 'once',
          name: 'Combined Discount (£25.0 off)',
          amount_off: 2500,
          currency: 'gbp',
          metadata: {
            environment: 'test',
            subscriber_id: 123,
            combined_discount: 'true'
          }
        })

        expect(result).to eq('COMBINED_123_ABCD')
      end

      it 'logs coupon creation' do
        expect(Rails.logger).to receive(:info).with('Created combined discount coupon: COMBINED_123_ABCD with £25.0 off')

        service.create_combined_discount_coupon(total_discount_amount)
      end
    end

    context 'when Stripe raises an error' do
      before do
        allow(Stripe::Coupon).to receive(:create).and_raise(Stripe::StripeError.new('Coupon creation failed'))
      end

      it 'logs error and returns nil' do
        expect(Rails.logger).to receive(:error).at_least(:once)

        result = service.create_combined_discount_coupon(total_discount_amount)

        expect(result).to be_nil
      end
    end

    context 'when non-Stripe error occurs' do
      before do
        allow(Stripe::Coupon).to receive(:create).and_raise(StandardError.new('Generic error'))
      end

      it 'logs error and returns nil' do
        expect(Rails.logger).to receive(:error).at_least(:once)

        result = service.create_combined_discount_coupon(total_discount_amount)

        expect(result).to be_nil
      end
    end

    context 'with fractional pounds' do
      let(:total_discount_amount) { 2567 } # £25.67

      let(:created_coupon) { double('Stripe::Coupon', id: 'COMBINED_123_ABCD') }

      before do
        allow(Stripe::Coupon).to receive(:create).and_return(created_coupon)
      end

      it 'formats pounds correctly to 2 decimal places' do
        service.create_combined_discount_coupon(total_discount_amount)

        expect(Stripe::Coupon).to have_received(:create).with(hash_including(
          name: 'Combined Discount (£25.67 off)',
          amount_off: 2567
        ))
      end
    end
  end

  describe 'discount consolidation integration' do
    let(:user) { create(:user, :with_subscriber) }
    let(:service) { described_class.new(user) }

    before do
      user.subscriber.update(id: 123)
      allow(service).to receive(:find_or_create_stripe_customer).and_return(double('Stripe::Customer', id: 'cus_123'))
      allow(service).to receive(:create_checkout_session).and_return({ checkout_url: 'https://checkout.stripe.com' })
    end

    context 'when individual subscription has multiple discounts' do
      it 'consolidates discounts for checkout session' do
        product_keys = [:science, :geography]
        plan_key = :individual
        return_url = 'http://example.com/success'

        # Mock StripeProducts methods
        allow(StripeProducts).to receive(:get_price_id).and_return('price_123')
        allow(StripeProducts).to receive(:get_discount_coupon_id).and_return('bundle_coupon')
        allow(StripeProducts).to receive(:get_base_size_from_plan_key).with(:individual).and_return(:individual)

        # Mock discount service for custom discount
        discount_service = double('DiscountService')
        allow(DiscountService).to receive(:new).and_return(discount_service)
        allow(discount_service).to receive(:apply_discount_code).and_return({
          success: true,
          stripe_coupon_id: 'custom_coupon'
        })

        # Mock the discount consolidation
        expect(service).to receive(:calculate_collective_discount).with(
          [{ coupon: 'bundle_coupon' }, { coupon: 'custom_coupon' }],
          ['price_123', 'price_123']
        ).and_return([{ coupon: 'combined_coupon' }])

        # Mock checkout session creation with consolidated discount
        expect(service).to receive(:create_checkout_session).with(
          anything,
          ['price_123', 'price_123'],
          [:science, :geography],
          :individual,
          return_url,
          [{ coupon: 'combined_coupon' }]
        )

        result = service.create_subscription(product_keys, plan_key, return_url, 'DISCOUNT_CODE')

        expect(result[:checkout_url]).to eq('https://checkout.stripe.com')
      end
    end

    context 'when individual subscription has single discount' do
      it 'does not consolidate single discount' do
        product_keys = [:science]
        plan_key = :individual
        return_url = 'http://example.com/success'

        allow(StripeProducts).to receive(:get_price_id).and_return('price_123')
        allow(StripeProducts).to receive(:get_discount_coupon_id).and_return(nil) # No bundle discount for single product
        allow(StripeProducts).to receive(:get_base_size_from_plan_key).with(:individual).and_return(:individual)

        # Should not call calculate_collective_discount
        expect(service).not_to receive(:calculate_collective_discount)

        expect(service).to receive(:create_checkout_session).with(
          anything,
          ['price_123'],
          [:science],
          :individual,
          return_url,
          [] # No discounts
        )

        service.create_subscription(product_keys, plan_key, return_url)
      end
    end

    context 'when school subscription has multiple discounts' do
      it 'does not consolidate discounts for invoice subscription' do
        product_keys = [:science, :geography]
        plan_key = :small

        allow(StripeProducts).to receive(:get_price_id).and_return('price_123')
        allow(StripeProducts).to receive(:get_discount_coupon_id).and_return('bundle_coupon')
        allow(StripeProducts).to receive(:get_base_size_from_plan_key).with(:small).and_return(:small)
        allow(service).to receive(:create_invoice_subscription).and_return({ subscription: 'sub_123' })

        # Should not call calculate_collective_discount for school subscriptions
        expect(service).not_to receive(:calculate_collective_discount)

        service.create_subscription(product_keys, plan_key)
      end
    end
  end
end
